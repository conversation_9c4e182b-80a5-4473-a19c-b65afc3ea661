import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import {JVxeTypes,JVxeColumn} from '/@/components/jeecg/JVxeTable/types'
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '姓名',
    align:"center",
    dataIndex: 'personName'
   },
   {
    title: '年龄',
    align:"center",
    dataIndex: 'age'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'gender_dictText'
   },
   {
    title: '学历',
    align:"center",
    dataIndex: 'degree'
   },
   {
    title: '职业',
    align:"center",
    dataIndex: 'occupation'
   },
   {
    title: '婚姻状况',
    align:"center",
    dataIndex: 'maritalStatus_dictText'
   },
   {
    title: '户籍所在地',
    align:"center",
    dataIndex: 'householdRegistration'
   },
   {
    title: '民族',
    align:"center",
    dataIndex: 'national'
   },
   {
    title: '外键',
    align:"center",
    dataIndex: 'pid'
   },
   {
    title: '报告单位',
    align:"center",
    dataIndex: 'reportUnit'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '姓名',
    field: 'personName',
    component: 'Input',
  },
  {
    label: '年龄',
    field: 'age',
    component: 'Input',
  },
  {
    label: '性别',
    field: 'gender',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"gender"
     },
  },
  {
    label: '学历',
    field: 'degree',
    component: 'Input',
  },
  {
    label: '职业',
    field: 'occupation',
    component: 'Input',
  },
  {
    label: '婚姻状况',
    field: 'maritalStatus',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"marital_status"
     },
  },
  {
    label: '户籍所在地',
    field: 'householdRegistration',
    component: 'Input',
  },
  {
    label: '民族',
    field: 'national',
    component: 'Input',
  },
  {
    label: '外键',
    field: 'pid',
    component: 'Input',
  },
  {
    label: '报告单位',
    field: 'reportUnit',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];
//子表单数据
//子表表格配置
export const aidsQuestionnairesSubColumns: JVxeColumn[] = [
    {
      title: 'C01',
      key: 'c01',
      type: JVxeTypes.select,
      options:[],
      dictCode:"is_status",
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: 'C02',
      key: 'c02',
      type: JVxeTypes.select,
      options:[],
      dictCode:"is_status",
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: 'C03',
      key: 'c03',
      type: JVxeTypes.select,
      options:[],
      dictCode:"is_status",
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: 'C04',
      key: 'c04',
      type: JVxeTypes.select,
      options:[],
      dictCode:"is_status",
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: 'C05',
      key: 'c05',
      type: JVxeTypes.select,
      options:[],
      dictCode:"is_status",
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
  ]


// 高级查询数据
export const superQuerySchema = {
  personName: {title: '姓名',order: 0,view: 'text', type: 'string',},
  age: {title: '年龄',order: 1,view: 'text', type: 'string',},
  gender: {title: '性别',order: 2,view: 'list', type: 'string',dictCode: 'gender',},
  degree: {title: '学历',order: 3,view: 'text', type: 'string',},
  occupation: {title: '职业',order: 4,view: 'text', type: 'string',},
  maritalStatus: {title: '婚姻状况',order: 5,view: 'list', type: 'string',dictCode: 'marital_status',},
  householdRegistration: {title: '户籍所在地',order: 6,view: 'text', type: 'string',},
  national: {title: '民族',order: 7,view: 'text', type: 'string',},
  pid: {title: '外键',order: 8,view: 'text', type: 'string',},
  reportUnit: {title: '报告单位',order: 9,view: 'text', type: 'string',},
  //子表高级查询
  aidsQuestionnairesSub: {
    title: '哨点监测调查问卷子表',
    view: 'table',
    fields: {
        pid: {title: '外键',order: 0,view: 'text', type: 'string',},
        c01: {title: 'C01',order: 1,view: 'list', type: 'string',dictCode: 'is_status',},
        c02: {title: 'C02',order: 2,view: 'list', type: 'string',dictCode: 'is_status',},
        c03: {title: 'C03',order: 3,view: 'list', type: 'string',dictCode: 'is_status',},
        c04: {title: 'C04',order: 4,view: 'list', type: 'string',dictCode: 'is_status',},
        c05: {title: 'C05',order: 5,view: 'list', type: 'string',dictCode: 'is_status',},
    }
  },
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
// 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}