<template>
  <div>
    <BasicForm @register="registerForm" ref="formRef"/>
    <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated  @change="handleChangeTabs">
      <a-tab-pane tab="tb_dossier_thisvisit" key="tbDossierThisvisit" :forceRender="true">
        <TbDossierThisvisitForm ref="tbDossierThisvisitForm" :disabled="formDisabled"></TbDossierThisvisitForm>
      </a-tab-pane>
      <a-tab-pane tab="tb_case_check_cure" key="tbCaseCheckCure" :forceRender="true">
        <JVxeTable
          keep-source
          resizable
          ref="tbCaseCheckCure"
          v-if="tbCaseCheckCureTable.show"
          :loading="tbCaseCheckCureTable.loading"
          :columns="tbCaseCheckCureTable.columns"
          :dataSource="tbCaseCheckCureTable.dataSource"
          :height="340"
          :rowNumber="true"
          :rowSelection="true"
          :disabled="formDisabled"
          :toolbar="true"
        />
      </a-tab-pane>
    </a-tabs>

    <div style="width: 100%;text-align: center" v-if="!formDisabled">
      <a-button @click="handleSubmit" pre-icon="ant-design:check" type="primary">提 交</a-button>
    </div>
  </div>
</template>

<script lang="ts">

import {BasicForm, useForm} from '/@/components/Form/index';
import { computed, defineComponent, reactive, ref, unref } from 'vue';
import {defHttp} from '/@/utils/http/axios';
import { propTypes } from '/@/utils/propTypes';
import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods';
import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils';
import TbDossierThisvisitForm from './TbDossierThisvisitForm.vue'
import {getBpmFormSchema,tbCaseCheckCureColumns} from '../TbPanorama.data';
import {saveOrUpdate,tbDossierThisvisitList,tbCaseCheckCureList} from '../TbPanorama.api';

export default defineComponent({
  name: "TbPanoramaForm",
  components:{
    BasicForm,
    TbDossierThisvisitForm,
  },
  props:{
    formData: propTypes.object.def({}),
    formBpm: propTypes.bool.def(true),
  },
  setup(props){
    const [registerForm, { setFieldsValue, setProps }] = useForm({
      labelWidth: 150,
      schemas: getBpmFormSchema(props.formData),
      showActionButtonGroup: false,
      baseColProps: {span: 12}
    });

    const formDisabled = computed(()=>{
      if(props.formData.disabled === false){
        return false;
      }
      return true;
    });

    const refKeys = ref(['tbDossierThisvisit', 'tbCaseCheckCure', ]);
    const activeKey = ref('tbDossierThisvisit');
    const tbDossierThisvisitForm = ref();
    const tbCaseCheckCure = ref();
    const tableRefs = {tbCaseCheckCure, };
    const tbCaseCheckCureTable = reactive({
      loading: false,
      dataSource: [],
      columns:tbCaseCheckCureColumns,
      show: false
    })

    const [handleChangeTabs,handleSubmit,requestSubTableData,formRef] = useJvxeMethod(requestAddOrEdit,classifyIntoFormData,tableRefs,activeKey,refKeys,validateSubForm);

    function classifyIntoFormData(allValues) {
      let main = Object.assign({}, allValues.formValue)
      return {
        ...main, // 展开
        tbDossierThisvisitList: tbDossierThisvisitForm.value.getFormData(),
        tbCaseCheckCureList: allValues.tablesValue[0].tableData,
      }
    }
    //校验所有一对一子表表单
    function validateSubForm(allValues){
      return new Promise((resolve, _reject)=>{
        Promise.all([
          tbDossierThisvisitForm.value.validateForm(0),
        ]).then(() => {
          resolve(allValues)
        }).catch(e => {
          if (e.error === VALIDATE_FAILED) {
            // 如果有未通过表单验证的子表，就自动跳转到它所在的tab
            activeKey.value = e.index == null ? unref(activeKey) : refKeys.value[e.index]
          } else {
            console.error(e)
          }
        })
      })
    }

    //表单提交事件
    async function requestAddOrEdit(values) {
      await saveOrUpdate(values, true);
    }

    const queryByIdUrl = '/tbPanorama/tbPanorama/queryById';
    async function initFormData(){
      let params = {id: props.formData.dataId};
      const data = await defHttp.get({url: queryByIdUrl, params});
      //设置表单的值
      await setFieldsValue({...data});
      tbDossierThisvisitForm.value.initFormData(tbDossierThisvisitList, data.id);
      requestSubTableData(tbCaseCheckCureList, {id: data.id}, tbCaseCheckCureTable, ()=>{
        tbCaseCheckCureTable.show = true;
      });
      //默认是禁用
      await setProps({disabled: formDisabled.value})
    }

    initFormData();

    return {
      registerForm,
      formDisabled,
      formRef,
      handleSubmit,
      activeKey,
      handleChangeTabs,
      tbDossierThisvisitForm,
      tbCaseCheckCure,
      tbCaseCheckCureTable,
    }
  }
});
</script>
