import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '分类名称',
    align:"center",
    dataIndex: 'categoryname'
   },
   {
    title: '分类描述',
    align:"center",
    dataIndex: 'categorydescription'
   },
   {
    title: '操作类型',
    align:"center",
    dataIndex: 'operationtype'
   },
   {
    title: '操作人',
    align:"center",
    dataIndex: 'operator'
   },
   {
    title: '操作状态',
    align:"center",
    dataIndex: 'state_dictText'
   },
   {
    title: '错误信息',
    align:"center",
    dataIndex: 'errormessage'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "分类名称",
      field: 'categoryname',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "分类描述",
      field: 'categorydescription',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "操作类型",
      field: 'operationtype',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '分类名称',
    field: 'categoryname',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入分类名称!'},
          ];
     },
  },
  {
    label: '分类描述',
    field: 'categorydescription',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入分类描述!'},
          ];
     },
  },
  {
    label: '操作类型',
    field: 'operationtype',
    component: 'Input',
  },
  {
    label: '操作人',
    field: 'operator',
    component: 'Input',
  },
  {
    label: '操作状态',
    field: 'state',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"TEMPLATE__STATE"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入操作状态!'},
          ];
     },
  },
  {
    label: '错误信息',
    field: 'errormessage',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  categoryname: {title: '分类名称',order: 0,view: 'text', type: 'string',},
  categorydescription: {title: '分类描述',order: 1,view: 'text', type: 'string',},
  operationtype: {title: '操作类型',order: 2,view: 'text', type: 'string',},
  operator: {title: '操作人',order: 3,view: 'text', type: 'string',},
  state: {title: '操作状态',order: 4,view: 'list', type: 'string',dictCode: 'TEMPLATE__STATE',},
  errormessage: {title: '错误信息',order: 5,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}