import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '所属地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: '接待机构名称',
    align:"center",
    dataIndex: 'recevorg_dictText'
   },
   {
    title: '接待日期',
    align:"center",
    dataIndex: 'recevdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '姓名',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'sex_dictText'
   },
   {
    title: '出生日期',
    align:"center",
    dataIndex: 'borndate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '身份证号',
    align:"center",
    dataIndex: 'idcard'
   },
   {
    title: '年龄',
    align:"center",
    dataIndex: 'age'
   },
   {
    title: '人群',
    align:"center",
    dataIndex: 'peopleType_dictText'
   },
   {
    title: 'HIV暴露风险评估结果',
    align:"center",
    dataIndex: 'bodycheckresult_dictText'
   },
   {
    title: '检测日期',
    align:"center",
    dataIndex: 'bodycheckdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '梅毒ELISA/TPPA检测结果',
    align:"center",
    dataIndex: 'sypcheckresult1_dictText'
   },
   {
    title: '梅毒ELISA/TPPA检测日期',
    align:"center",
    dataIndex: 'sypcheckdate1',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '梅毒RPR/TRUST检测结果',
    align:"center",
    dataIndex: 'sypcheckresult2_dictText'
   },
   {
    title: '梅毒RPR/TRUST检测日期',
    align:"center",
    dataIndex: 'sypcheckdate2',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '适用性评估结果',
    align:"center",
    dataIndex: 'exposureinter6_dictText'
   },
   {
    title: '求询者是否同意服药预防',
    align:"center",
    dataIndex: 'exposureinter7_dictText'
   },
   {
    title: '处方',
    align:"center",
    dataIndex: 'preschoice_dictText'
   },
   {
    title: '开始服药日期',
    align:"center",
    dataIndex: 'startmeching',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '随访次数',
    align:"center",
    dataIndex: 'followupcount_dictText'
   },
   {
    title: 'HIV抗体检测结果',
    align:"center",
    dataIndex: 'hivcheckresult_dictText'
   },
   {
    title: '检测日期',
    align:"center",
    dataIndex: 'checkdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [

  {
    label: '所属地区',
    field: 'zonecode',
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.orgcode=''
          //所属部门修改后更新负责部门下拉框数据
          
          updateSchema([
            {
              field: 'recevorg',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '接待机构名称',
    field: 'recevorg',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
	{
      label: "姓名",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "身份证号",
      field: 'idcard',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [

  {
    label: '所属地区',
    field: 'zonecode',
    required: true,
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.orgcode=''
          //所属部门修改后更新负责部门下拉框数据
          
          updateSchema([
            {
              field: 'recevorg',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '接待机构名称',
    field: 'recevorg',
    required: true,
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: '接待日期',
    field: 'recevdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入接待日期!'},
          ];
     },
  },
  {
    label: '姓名',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入姓名!'},
                 { min: 1, max: 10, message: '长度在 1 到 10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sex",
        type: "radio"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入性别!'},
          ];
     },
  },
  {
    label: '出生日期',
    field: 'borndate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入出生日期!'},
          ];
     },
  },
  {
    label: '身份证号',
    field: 'idcard',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入身份证号!'},
                 { min: 15, max: 18, message: '长度在 15-18 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '年龄',
    field: 'age',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入年龄!'},
                 { pattern: /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9])$/, message: '请输入正确年龄!'},
          ];
     },
  },
  {
    label: '人群',
    field: 'peopleType',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_peopleType"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入人群!'},
          ];
     },
  },
  {
    label: '人群-其他',
    field: 'peopleOther',
    component: 'Input',
  },
  {
    label: 'HIV暴露风险评估结果',
    field: 'bodycheckresult',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_check_result"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入HIV暴露风险评估结果!'},
          ];
     },
  },
  {
    label: '检测日期',
    field: 'bodycheckdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入检测日期!'},
          ];
     },
  },
  {
    label: '梅毒ELISA/TPPA检测结果',
    field: 'sypcheckresult1',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_check_yinyang"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入梅毒ELISA/TPPA检测结果!'},
          ];
     },
  },
  {
    label: '梅毒ELISA/TPPA检测日期',
    field: 'sypcheckdate1',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入梅毒ELISA/TPPA检测日期!'},
          ];
     },
  },
  {
    label: '梅毒RPR/TRUST检测结果',
    field: 'sypcheckresult2',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_check_yinyang"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入梅毒RPR/TRUST检测结果!'},
          ];
     },
  },
  {
    label: '梅毒RPR/TRUST检测日期',
    field: 'sypcheckdate2',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入梅毒RPR/TRUST检测日期!'},
          ];
     },
  },
  {
    label: '适用性评估结果',
    field: 'exposureinter6',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_apply_result"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入适用性评估结果!'},
          ];
     },
  },
  {
    label: '求询者是否同意服药预防',
    field: 'exposureinter7',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_agree_result"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入求询者是否同意服药预防!'},
          ];
     },
  },
  {
    label: '处方',
    field: 'preschoice',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_prescription"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入处方!'},
          ];
     },
  },
  {
    label: '其他处方',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '开始服药日期',
    field: 'startmeching',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入开始服药日期!'},
          ];
     },
  },
  {
    label: '随访次数',
    field: 'followupcount',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_follow_count"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入随访次数!'},
          ];
     },
  },
  {
    label: 'HIV抗体检测结果',
    field: 'hivcheckresult',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_check_result"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入HIV抗体检测结果!'},
          ];
     },
  },
  {
    label: '检测日期',
    field: 'checkdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入检测日期!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
 /* zonecode: {title: '所属地区',order: 0,view: 'text', type: 'string',},
  recevorg: {title: '接待机构名称',order: 1,view: 'text', type: 'string',}, */
  recevdate: {title: '接待日期',order: 2,view: 'date', type: 'string',},
  name: {title: '姓名',order: 3,view: 'text', type: 'string',},
  sex: {title: '性别',order: 4,view: 'radio', type: 'string',dictCode: 'aids_sex',},
  borndate: {title: '出生日期',order: 5,view: 'date', type: 'string',},
  idcard: {title: '身份证号',order: 6,view: 'text', type: 'string',},
  age: {title: '年龄',order: 7,view: 'text', type: 'string',},
  peopleType: {title: '人群',order: 8,view: 'list', type: 'string',dictCode: 'aids_peopleType',},
  bodycheckresult: {title: 'HIV暴露风险评估结果',order: 10,view: 'list', type: 'string',dictCode: 'aids_check_result',},
  bodycheckdate: {title: '检测日期',order: 11,view: 'date', type: 'string',},
  sypcheckresult1: {title: '梅毒ELISA/TPPA检测结果',order: 12,view: 'list', type: 'string',dictCode: 'aids_check_yinyang',},
  sypcheckdate1: {title: '梅毒ELISA/TPPA检测日期',order: 13,view: 'date', type: 'string',},
  sypcheckresult2: {title: '梅毒RPR/TRUST检测结果',order: 14,view: 'list', type: 'string',dictCode: 'aids_check_yinyang',},
  sypcheckdate2: {title: '梅毒RPR/TRUST检测日期',order: 15,view: 'date', type: 'string',},
  exposureinter6: {title: '适用性评估结果',order: 16,view: 'list', type: 'string',dictCode: 'aids_apply_result',},
  exposureinter7: {title: '求询者是否同意服药预防',order: 17,view: 'list', type: 'string',dictCode: 'aids_agree_result',},
  preschoice: {title: '处方',order: 18,view: 'list', type: 'string',dictCode: 'aids_prescription',},
  startmeching: {title: '开始服药日期',order: 20,view: 'date', type: 'string',},
  followupcount: {title: '随访次数',order: 21,view: 'list', type: 'string',dictCode: 'aids_follow_count',},
  hivcheckresult: {title: 'HIV抗体检测结果',order: 22,view: 'list', type: 'string',dictCode: 'aids_check_result',},
  checkdate: {title: '检测日期',order: 23,view: 'date', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
