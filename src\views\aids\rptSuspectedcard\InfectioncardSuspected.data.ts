import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import {JVxeTypes,JVxeColumn} from '/@/components/jeecg/JVxeTable/types'
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
   title: '卡片编号',
   align:"center",
   dataIndex: 'cardCode'
  },
  {
    title: '报告地区',
    align: 'center',
    dataIndex: 'apanagecode_dictText',
  },
  {
    title: '报告单位',
    align: 'center',
    dataIndex: 'rptorgcode_dictText',
  },
   {
    title: '患者姓名',
    align:"center",
    dataIndex: 'patientName'
   },
   {
    title: '患儿家长姓名',
    align:"center",
    dataIndex: 'parentName'
   },
   {
    title: '身份证件号码',
    align:"center",
    dataIndex: 'idCard'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'sex1_dictText'
   },
   {
    title: '出生日期',
    align:"center",
    dataIndex: 'birthdayDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '患者工作单位',
    align:"center",
    dataIndex: 'unit'
   },
   {
    title: '联系电话',
    align:"center",
    dataIndex: 'telp'
   },
   {
    title: '现住址类型',
    align:"center",
    dataIndex: 'areatype_dictText'
   },
   {
    title: '现住地址国标',
    align:"center",
    dataIndex: 'addrcode'
   },
   {
    title: '现住详细地址',
    align:"center",
    dataIndex: 'addr'
   },
   {
    title: '职业',
    align:"center",
    dataIndex: 'groupId_dictText'
   },
   {
    title: '疾病名称',
    align:"center",
    dataIndex: 'diseaseId1_dictText'
   },
   {
    title: '发病日期',
    align:"center",
    dataIndex: 'startDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '诊断日期',
    align:"center",
    dataIndex: 'diagnosedate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '死亡日期',
    align:"center",
    dataIndex: 'deaddate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '病例分类',
    align:"center",
    dataIndex: 'casetype_dictText'
   },
   {
    title: '填卡医生',
    align:"center",
    dataIndex: 'inputdoctor'
   },
   {
    title: '医生联系电话',
    align:"center",
    dataIndex: 'telp1'
   },
   {
    title: '医生填卡日期',
    align:"center",
    dataIndex: 'filltime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '密切接触者有无相同症状',
    align:"center",
    dataIndex: 'contactflag_dictText'
   },
   {
    title: '补卡日期',
    align:"center",
    dataIndex: 'afterFillDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'notes'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '报告地区',
    field: 'apanagecode',
    component: 'Zonecode',
    
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.rptorgcode=''
          //所属部门修改后更新负责部门下拉框数据
          updateSchema([
            {
              field: 'rptorgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'rptorgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
	{
      label: "患者姓名",
      field: "patientName",
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '报告地区',
    field: 'apanagecode',
    required: true,
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.rptorgcode=''
          //所属部门修改后更新负责部门下拉框数据
          updateSchema([
            {
              field: 'rptorgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'rptorgcode',
    required: true,
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: '卡片编号',
    field: 'cardCode',
    component: 'Input',
  },
  {
    label: '卡片ID',
    field: 'cardId',
    component: 'InputNumber',
  },
  {
    label: '患者姓名',
    field: 'patientName',
    component: 'Input',
  },
  {
    label: '患儿家长姓名',
    field: 'parentName',
    component: 'Input',
  },
  {
    label: '身份证件号码',
    field: 'idCard',
    component: 'Input',
  },
  {
    label: '性别',
    field: 'sex1',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sex",
        type: "radio"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入性别!'},
          ];
     },
  },
  {
    label: '出生日期',
    field: 'birthdayDate',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },    
  },
  {
    label: '患者工作单位',
    field: 'unit',
    component: 'Input',
  },
  {
    label: '联系电话',
    field: 'telp',
    component: 'Input',
  },
  {
    label: '现住址类型',
    field: 'areatype',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_dd_areatype"
     },
  },
  {
    label: '现住地址国标',
    field: 'addrcode',
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          //所属部门修改后更新负责部门下拉框数据
          
        },
      };
    },
  },
  {
    label: '现住详细地址',
    field: 'addr',
    component: 'Input',
  },
  {
    label: '职业',
    field: 'groupId',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_nultitude",
        stringToNumber:"false"
     },
  },
  {
    label: '其他职业',
    field: 'occupation',
    component: 'Input',
  },
  {
    label: '疾病名称',
    field: 'diseaseId1',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_udp_disaster_type",
        stringToNumber:"false"
     },
  },
  {
    label: '发病日期',
    field: 'startDate',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },    
  },
  {
    label: '诊断日期',
    field: 'diagnosedate',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },    
  },
  {
    label: '死亡日期',
    field: 'deaddate',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },    
  },
  {
    label: '病例分类',
    field: 'casetype',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_casetype"
     },
  },
  {
    label: '填卡医生',
    field: 'inputdoctor',
    component: 'Input',
  },
  {
    label: '医生联系电话',
    field: 'telp1',
    component: 'Input',
  },
  {
    label: '医生填卡日期',
    field: 'filltime',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },    
  },
  {
    label: '密切接触者有无相同症状',
    field: 'contactflag',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '补卡日期',
    field: 'afterFillDate',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },    
  },
  {
    label: '备注',
    field: 'notes',
    component: 'InputTextArea',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];
//子表单数据
export const suspectedRptMasculineFormSchema: FormSchema[] = [
  {
    label: '婚姻',
    field: 'marriage',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_marriage",
        type: "radio"
     },
  },
  {
    label: '民族',
    field: 'nation',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_nation"
     },
  },
  {
    label: '文化程度',
    field: 'educationback',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_culture"
     },
  },
  {
    label: '户籍地类型',
    field: 'areatype',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_dd_areatype"
     },
  },
  {
    label: '户籍所在地国标',
    field: 'addrcode',
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          //所属部门修改后更新负责部门下拉框数据
        },
      };
    },
  },
  {
    label: '户籍详细地址',
    field: 'addr',
    component: 'Input',
  },
  {
    label: '梅毒',
    field: 'luestype',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_luestype"
     },
  },
  {
    label: '生殖道沙眼衣原体感染',
    field: 'sinfect',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_sinfect"
     },
  },
  {
    label: '接触史',
    field: 'intouchhis',
    component: 'JSelectMultiple',
    componentProps:{
        dictCode:"aids_mas_dd_touchhis"
     },
  },
  {
    label: '接触史其它',
    field: 'touothers',
    component: 'Input',
  },
  {
    label: '注射毒品史与病人共用过注射器的人数',
    field: 'injectcount',
    component: 'Input',
  },
  {
    label: '非婚异性性接触史与病人有非婚性行为的人数',
    field: 'nonwebcount',
    component: 'Input',
  },
  {
    label: '男男性行为史 发生同性性行为的人数',
    field: 'smcount',
    component: 'Input',
  },
  {
    label: '性病史',
    field: 'venerealhistory',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_touchhis",
        type: "radio"
     },
  },
  {
    label: '最有可能感染途径',
    field: 'infectroute',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_infectionRouteCode",
        type: "radio"
     },
  },
  {
    label: '感染途径其它',
    field: 'infecothers',
    component: 'Input',
  },
  {
    label: '样本来源',
    field: 'sampleorigin',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_infectionRouteCode",
        type: "radio"
     },
  },
  {
    label: '样本来源其它',
    field: 'srcothers',
    component: 'Input',
  },
  {
    label: '实验室检测结论',
    field: 'labconclusion',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_labconclusion",
        type: "radio"
     },
  },
  {
    label: '确认（替代策略）检测阳性日期',
    field: 'affirmmasdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },   
  },
  {
    label: '确认（替代策略）检测单位',
    field: 'affirmmasorg',
    component: 'Input',
  },
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];
//子表表格配置


// 高级查询数据
export const superQuerySchema = {
  apanagecode: {title: '报告地区',order: 0,view: 'text', type: 'string',},
  rptorgcode: {title: '报告单位',order: 1,view: 'text', type: 'string',},
  cardCode: {title: '卡片编号',order: 2,view: 'text', type: 'string',},
  cardId: {title: '卡片ID',order: 3,view: 'number', type: 'number',},
  patientName: {title: '患者姓名',order: 4,view: 'text', type: 'string',},
  parentName: {title: '患儿家长姓名',order: 5,view: 'text', type: 'string',},
  idCard: {title: '身份证件号码',order: 6,view: 'text', type: 'string',},
  sex1: {title: '性别',order: 7,view: 'radio', type: 'string',dictCode: 'aids_sex',},
  birthdayDate: {title: '出生日期',order: 8,view: 'date', type: 'string',},
  unit: {title: '患者工作单位',order: 9,view: 'text', type: 'string',},
  telp: {title: '联系电话',order: 10,view: 'text', type: 'string',},
  areatype: {title: '现住址类型',order: 11,view: 'list', type: 'string',dictCode: 'aids_dd_areatype',},
  addrcode: {title: '现住地址国标',order: 12,view: 'text', type: 'string',},
  addr: {title: '现住详细地址',order: 13,view: 'text', type: 'string',},
  groupId: {title: '职业',order: 14,view: 'number', type: 'number',dictCode: 'aids_nultitude',},
  diseaseId1: {title: '疾病名称',order: 15,view: 'number', type: 'number',dictCode: 'aids_udp_disaster_type',},
  startDate: {title: '发病日期',order: 16,view: 'date', type: 'string',},
  diagnosedate: {title: '诊断日期',order: 17,view: 'date', type: 'string',},
  deaddate: {title: '死亡日期',order: 18,view: 'date', type: 'string',},
  casetype: {title: '病例分类',order: 19,view: 'list', type: 'string',dictCode: 'aids_casetype',},
  inputdoctor: {title: '填卡医生',order: 20,view: 'text', type: 'string',},
  telp1: {title: '医生联系电话',order: 21,view: 'text', type: 'string',},
  filltime: {title: '医生填卡日期',order: 22,view: 'date', type: 'string',},
  contactflag: {title: '密切接触者有无相同症状',order: 23,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  afterFillDate: {title: '补卡日期',order: 24,view: 'date', type: 'string',},
  notes: {title: '备注',order: 25,view: 'textarea', type: 'string',},
  //子表高级查询
  suspectedRptMasculine: {
    title: '初筛数据附表',
    view: 'table',
    fields: {
        marriage: {title: '婚姻',order: 0,view: 'radio', type: 'string',dictCode: 'aids_mas_dd_marriage',},
        nation: {title: '民族',order: 1,view: 'list', type: 'string',dictCode: 'aids_nation',},
        educationback: {title: '文化程度',order: 2,view: 'list', type: 'string',dictCode: 'aids_mas_dd_culture',},
        areatype: {title: '户籍地类型',order: 3,view: 'list', type: 'string',dictCode: 'aids_dd_areatype',},
        addrcode: {title: '户籍所在地国标',order: 4,view: 'text', type: 'string',},
        addr: {title: '户籍详细地址',order: 5,view: 'text', type: 'string',},
        luestype: {title: '梅毒',order: 6,view: 'list', type: 'string',dictCode: 'aids_mas_dd_luestype',},
        sinfect: {title: '生殖道沙眼衣原体感染',order: 7,view: 'list', type: 'string',dictCode: 'aids_mas_dd_sinfect',},
        intouchhis: {title: '接触史',order: 8,view: 'list_multi', type: 'string',dictCode: 'aids_mas_dd_touchhis',},
        touothers: {title: '接触史其它',order: 9,view: 'text', type: 'string',},
        injectcount: {title: '注射毒品史与病人共用过注射器的人数',order: 10,view: 'text', type: 'string',},
        nonwebcount: {title: '非婚异性性接触史与病人有非婚性行为的人数',order: 11,view: 'text', type: 'string',},
        smcount: {title: '男男性行为史 发生同性性行为的人数',order: 12,view: 'text', type: 'string',},
        venerealhistory: {title: '性病史',order: 13,view: 'radio', type: 'string',dictCode: 'aids_mas_dd_touchhis',},
        infectroute: {title: '最有可能感染途径',order: 14,view: 'radio', type: 'string',dictCode: 'aids_infectionRouteCode',},
        infecothers: {title: '感染途径其它',order: 15,view: 'text', type: 'string',},
        sampleorigin: {title: '样本来源',order: 16,view: 'radio', type: 'string',dictCode: 'aids_infectionRouteCode',},
        srcothers: {title: '样本来源其它',order: 17,view: 'text', type: 'string',},
        labconclusion: {title: '实验室检测结论',order: 18,view: 'radio', type: 'string',dictCode: 'aids_mas_dd_labconclusion',},
        affirmmasdate: {title: '确认（替代策略）检测阳性日期',order: 19,view: 'date', type: 'string',},
        affirmmasorg: {title: '确认（替代策略）检测单位',order: 20,view: 'text', type: 'string',},
        cardId: {title: '卡片id',order: 21,view: 'number', type: 'number',},
        opflag: {title: '操作标识 0：专病系统地市/省级操作 1：大疫情系统操作   2：专病系统国家平台操作',order: 22,view: 'number', type: 'number',},
    }
  },
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
// 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
