import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '删除状态',
    align: "center",
    dataIndex: 'state_dictText'
  },
  {
    title: '错误信息',
    align: "center",
    dataIndex: 'errormessage'
  },
  {
    title: '影响记录数',
    align: "center",
    dataIndex: 'affectedrecords'
  },
  {
    title: '日志编号',
    align: "center",
    dataIndex: 'logid'
  },
  {
    title: '时间戳',
    align: "center",
    dataIndex: 'timestamp'
  },
  {
    title: '提示信息',
    align: "center",
    dataIndex: 'promptmessage'
  },
];

// 高级查询数据
export const superQuerySchema = {
  state: {title: '删除状态',order: 0,view: 'list', type: 'string',dictCode: 'TEMPLATE__STATE',},
  errormessage: {title: '错误信息',order: 1,view: 'text', type: 'string',},
  affectedrecords: {title: '影响记录数',order: 2,view: 'text', type: 'string',},
  logid: {title: '日志编号',order: 3,view: 'text', type: 'string',},
  timestamp: {title: '时间戳',order: 4,view: 'datetime', type: 'string',},
  promptmessage: {title: '提示信息',order: 5,view: 'text', type: 'string',},
};
