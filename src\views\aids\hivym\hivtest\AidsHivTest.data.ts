import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '个人编码',
    align:"center",
    dataIndex: 'gerenma'
   },
   {
    title: '既往HIV抗体检测情况',
    align:"center",
    dataIndex: 'pasthiv_dictText'
   },
   {
    title: '本次是否进行hiv抗体筛查检测',
    align:"center",
    dataIndex: 'benchiv_dictText'
   },
   {
    title: '筛查检测结果',
    align:"center",
    dataIndex: 'bencjieg_dictText'
   },
   {
    title: '筛查检测日期',
    align:"center",
    dataIndex: 'scdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'hiv确证检测结果',
    align:"center",
    dataIndex: 'bencjhiv_dictText'
   },
   {
    title: 'hiv确证检测日期',
    align:"center",
    dataIndex: 'qzdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '梅毒血清抗体检测',
    align:"center",
    dataIndex: 'mdjg_dictText'
   },
   {
    title: '梅毒血清抗体检测日期',
    align:"center",
    dataIndex: 'mddate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "个人编码",
      field: 'gerenma',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "筛查检测结果",
      field: 'bencjieg',
      component: 'JDictSelectTag',
      componentProps:{
          dictCode:"aids_zyzxscjcjg"
      },
      //colProps: {span: 6},
 	},
	{
      label: "确证检测结果",
      field: 'bencjhiv',
      component: 'JDictSelectTag',
      componentProps:{
          dictCode:"aids_check_yinyang"
      },
      //colProps: {span: 6},
 	},
	{
      label: "血清抗体检测",
      field: 'mdjg',
      component: 'JDictSelectTag',
      componentProps:{
          dictCode:"aids_check_yinyang"
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '个人编码',
    field: 'gerenma',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入个人编码!'},
          ];
     },
  },
  {
    label: '既往HIV抗体检测情况',
    field: 'pasthiv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_hivktjc",
        type: "radio"
     },
  },
  {
    label: '本次是否进行hiv抗体筛查检测',
    field: 'benchiv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '筛查检测结果',
    field: 'bencjieg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_zyzxscjcjg",
        type: "radio"
     },
  },
  {
    label: '筛查检测日期',
    field: 'scdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'hiv确证检测结果',
    field: 'bencjhiv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_check_yinyang",
        type: "radio"
     },
  },
  {
    label: 'hiv确证检测日期',
    field: 'qzdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '梅毒血清抗体检测',
    field: 'mdjg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_check_yinyang",
        type: "radio"
     },
  },
  {
    label: '梅毒血清抗体检测日期',
    field: 'mddate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  gerenma: {title: '个人编码',order: 0,view: 'text', type: 'string',},
  pasthiv: {title: '既往HIV抗体检测情况',order: 1,view: 'radio', type: 'string',dictCode: 'aids_hivktjc',},
  benchiv: {title: '本次是否进行hiv抗体筛查检测',order: 2,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  bencjieg: {title: '筛查检测结果',order: 3,view: 'radio', type: 'string',dictCode: 'aids_zyzxscjcjg',},
  scdate: {title: '筛查检测日期',order: 4,view: 'date', type: 'string',},
  bencjhiv: {title: 'hiv确证检测结果',order: 5,view: 'radio', type: 'string',dictCode: 'aids_check_yinyang',},
  qzdate: {title: 'hiv确证检测日期',order: 6,view: 'date', type: 'string',},
  mdjg: {title: '梅毒血清抗体检测',order: 7,view: 'radio', type: 'string',dictCode: 'aids_check_yinyang',},
  mddate: {title: '梅毒血清抗体检测日期',order: 8,view: 'date', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
