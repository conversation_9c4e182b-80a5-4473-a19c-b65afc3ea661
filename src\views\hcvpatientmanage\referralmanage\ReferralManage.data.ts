import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '卡片编号',
    align:"center",
    dataIndex: 'idCard'
   },
   {
    title: '患者姓名',
    align:"center",
    dataIndex: 'patientName'
   },
   {
    title: '转出单位',
    align:"center",
    dataIndex: 'transferOutOrg'
   },
   {
    title: '转出时间',
    align:"center",
    dataIndex: 'transferOutTime'
   },
   {
    title: '转入单位',
    align:"center",
    dataIndex: 'transferInOrg'
   },
   {
    title: '转入时间',
    align:"center",
    dataIndex: 'transferInTime'
   },
   {
    title: '审核人',
    align:"center",
    dataIndex: 'reviewer'
   },
   {
    title: '审核时间',
    align:"center",
    dataIndex: 'reviewTime'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '卡片编号',
    field: 'idCard',
    component: 'Input',
  },
  {
    label: '患者姓名',
    field: 'patientName',
    component: 'Input',
  },
  {
    label: '转出单位',
    field: 'transferOutOrg',
    component: 'Input',
  },
  {
    label: '转出时间',
    field: 'transferOutTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '转入单位',
    field: 'transferInOrg',
    component: 'Input',
  },
  {
    label: '转入时间',
    field: 'transferInTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '审核人',
    field: 'reviewer',
    component: 'Input',
  },
  {
    label: '审核时间',
    field: 'reviewTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  idCard: {title: '卡片编号',order: 0,view: 'text', type: 'string',},
  patientName: {title: '患者姓名',order: 1,view: 'text', type: 'string',},
  transferOutOrg: {title: '转出单位',order: 2,view: 'text', type: 'string',},
  transferOutTime: {title: '转出时间',order: 3,view: 'datetime', type: 'string',},
  transferInOrg: {title: '转入单位',order: 4,view: 'text', type: 'string',},
  transferInTime: {title: '转入时间',order: 5,view: 'text', type: 'string',},
  reviewer: {title: '审核人',order: 6,view: 'text', type: 'string',},
  reviewTime: {title: '审核时间',order: 7,view: 'datetime', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
