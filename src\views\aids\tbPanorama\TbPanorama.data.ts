import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import {JVxeTypes,JVxeColumn} from '/@/components/jeecg/JVxeTable/types'
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  /*{
    title: '病案类别',
    align:"center",
    dataIndex: 'dossiertype'
  },
  {
    title: '登记号',
    align:"center",
    dataIndex: 'registrationno'
  },
  {
    title: '现诊断单位',
    align:"center",
    dataIndex: 'nowdiagnoseorgcode_dictText'
  },
  {
    title: '现管理单位',
    align:"center",
    dataIndex: 'nowmanageorgcode_dictText'
  },
  {
    title: '首诊断单位',
    align:"center",
    dataIndex: 'firstdiagnoseorgcode_dictText'
  },
  {
    title: '姓名',
    align:"center",
    dataIndex: 'patientName'
  },
  {
    title: '性别',
    align:"center",
    dataIndex: 'gendercode_dictText'
  },
  {
    title: '登记分类',
    align:"center",
    dataIndex: 'registrationtypecode_dictText'
  },
  {
    title: '治疗分类',
    align:"center",
    dataIndex: 'treatmenttypecode_dictText'
  },
  {
    title: '登记日期',
    align:"center",
    dataIndex: 'tmregister',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },*/
  {
    title: '卡片编号',
    align: 'center',
    dataIndex: 'cardCode',
  },
  {
    title: '报告地区',
    align: 'center',
    dataIndex: 'apanagecode_dictText',
  },
  {
    title: '报告单位',
    align: 'center',
    dataIndex: 'rptorgcode_dictText',
  },
  {
    title: '姓名',
    align: 'center',
    dataIndex: 'patientName',
  },
  {
    title: '性别',
    align: 'center',
    dataIndex: 'sex1_dictText',
  },
  {
    title: '详细地址',
    align: 'center',
    dataIndex: 'addr',
  },
  {
    title: '疾病名称',
    align: 'center',
    dataIndex: 'diseaseId1_dictText',
  },
  {
    title: '病例分类',
    align: 'center',
    dataIndex: 'casetype_dictText',
  },
  {
    title: '填卡医生',
    align: 'center',
    dataIndex: 'inputdoctor',
  },
  {
    title: '填卡日期',
    align: 'center',
    dataIndex: 'filltime',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: "登记号",
    field: "registrationno",
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: "现诊断地区",
    field: "nowdiagnosezonecode",
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
    //colProps: {span: 6},
  },
  {
    label: "现诊断单位",
    field: "nowdiagnoseorgcode",
    component: 'Orgcode',

    //colProps: {span: 6},
  },
  {
    label: "现管理地区",
    field: "nowmanagezonecode",
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
    //colProps: {span: 6},
  },
  {
    label: "现管理单位",
    field: "nowmanageorgcode",
    component: 'Orgcode',
    //colProps: {span: 6},
  },
  {
    label: "姓名",
    field: "patientName",
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: "身份证号",
    field: "idCard",
    component: 'Input',
    //colProps: {span: 6},
  },
  {
    label: "人群分类",
    field: "nultitudetypecode",
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"CROWD_TYPE"
    },
    //colProps: {span: 6},
  },
  {
    label: "HIV检查结果",
    field: "dtHiv",
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    //colProps: {span: 6},
  },
  {
    label: "诊断结果",
    field: "ddSputumresult",
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"DD_SPUTUMRESULT"
    },
    //colProps: {span: 6},
  },
  {
    label: "合并其他结核",
    field: "coalitionsystemtb",
    component: 'JSelectMultiple',
    componentProps:{
      dictCode:"COALITIONSYSTEMTB",
      triggerChange: true
    },
    //colProps: {span: 6},
  },
  {
    label: "登记分类",
    field: "registrationtypecode",
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"SUSPICIOUS_PERSON_REGISTRATION"
    },
    //colProps: {span: 6},
  },
  {
    label: "治疗分类",
    field: "treatmenttypecode",
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"TREATMENTTYPECODE"
    },
    //colProps: {span: 6},
  },
  {
    label: "等待治疗原因",
    field: "waitTreatmentReasonCode",
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"DOSSIERDR_WAITCURE"
    },
    //colProps: {span: 6},
  },
  {
    label: "应服药次数",
    field: "dosenumber",
    component: 'Input', //TODO 范围查询
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '病案类别',
    field: 'dossiertype',
    component: 'Input',
  },
  {
    label: '登记号',
    field: 'registrationno',
    component: 'Input',
    dynamicDisabled:true
  },
  {
    label: '现诊断地区',
    field: 'nowdiagnosezonecode',
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
  },
  {
    label: '现诊断单位',
    field: 'nowdiagnoseorgcode',
    component: 'Orgcode',
  },
  {
    label: '现管理地区',
    field: 'nowmanagezonecode',
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
  },
  {
    label: '现管理单位',
    field: 'nowmanageorgcode',
    component: 'Orgcode',
  },
  {
    label: '首诊断地区',
    field: 'firstdiagnosezonecode',
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
  },
  {
    label: '首诊断单位',
    field: 'firstdiagnoseorgcode',
    component: 'Orgcode',
  },
  {
    label: '首管理地区',
    field: 'firstmanagezonecode',
    component: 'Input',
  },
  {
    label: '首管理单位',
    field: 'firstmanageorgcode',
    component: 'Input',
  },
  {
    label: '姓名',
    field: 'patientName',
    component: 'Input',
  },
  {
    label: '性别',
    field: 'gendercode',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"GENDER_CODE",
      type: "radio"
    },
  },
  {
    label: '联系人1姓名',
    field: 'contactPerson1',
    component: 'Input',
  },
  {
    label: '联系人1监护人1与本人关系',
    field: 'contactsRelCode1',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"CONTACTS_REL_CODE"
    },
  },
  {
    label: '联系人1电话',
    field: 'contactTel1',
    component: 'Input',
  },
  {
    label: '联系人2姓名',
    field: 'contactPerson2',
    component: 'Input',
  },
  {
    label: '联系人2/监护人2与本人关系',
    field: 'contactsRelCode2',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"CONTACTS_REL_CODE"
    },
  },
  {
    label: '联系人2电话',
    field: 'contactTel2',
    component: 'Input',
  },
  {
    label: '国籍',
    field: 'nationality',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"NATIONALITY_CODE"
    },
  },
  {
    label: '民族',
    field: 'nationcode',
    component: 'Input',
  },
  {
    label: '证件类型',
    field: 'idcardtypecode',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"DOSSIERDOCUMENTTYPE"
    },
  },
  {
    label: '身份证号',
    field: 'idCard',
    component: 'Input',
  },
  {
    label: '出生日期',
    field: 'birthday',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '人群分类',
    field: 'nultitudetypecode',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"CROWD_TYPE"
    },
  },
  {
    label: '现地址类型',
    field: 'livingaddressattributioncode',
    component: 'Input',
  },
  {
    label: '现地址选择及国标',
    field: 'livingaddresscode',
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
  },
  {
    label: '现地址详细',
    field: 'livingaddressdetails',
    component: 'Input',
  },
  {
    label: '户籍地址类型',
    field: 'domicileaddressattributioncode',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"DD_TB_PERMANERT_TYPE"
    },
  },
  {
    label: '户籍地址选择及国标',
    field: 'residenceCode',
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
  },
  {
    label: '户籍地址详细',
    field: 'domicileadrressdetails',
    component: 'Input',
  },
  {
    label: '患者联系电话',
    field: 'tel',
    component: 'Input',
  },
  {
    label: '患者工作单位',
    field: 'employerorgname',
    component: 'Input',
  },
  {
    label: '症状',
    field: 'symptomCase',
    component: 'JCheckbox',
    componentProps:{
      dictCode:"SYMPTOMS_CODE"
    },
  },
  {
    label: '发现方式',
    field: 'discoverymodecode',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"DISCOVERYMODECODE"
    },
  },
  {
    label: '本次症状出现日期',
    field: 'dtSymptomStart',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '本次首诊日期',
    field: 'dtCurrentCome',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'HIV检查结果',
    field: 'hiv',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"HIV"
    },
  },
  {
    label: 'HIV检查结果',
    field: 'dtHiv',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '录入日期',
    field: 'operateDate',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '患者项目类别',
    field: 'joinProjectCode',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"JOIN_PROJECT_CODE"
    },
  },
  {
    label: '既往抗结核治疗史',
    field: 'tbCureHis',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"TB_CASE_DISSIER_YOUWU",
      type: "radio"
    },
  },
  {
    label: '本次确诊日期',
    field: 'dtCurrentConfirm',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '患者诊断分型',
    field: 'ddModel',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"DD_MODEL"
    },
  },
  {
    label: '诊断结果',
    field: 'ddSputumresult',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"DD_SPUTUMRESULT"
    },
  },
  {
    label: '病原学结果',
    field: 'aetiologicalresults',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"AETIOLOGICAL_RESULTS"
    },
  },
  {
    label: '合并其他结核',
    field: 'coalitionsystemtb',
    component: 'JSelectMultiple',
    componentProps:{
      dictCode:"COALITIONSYSTEMTB"
    },
  },
  {
    label: '合并症',
    field: 'coalitionIll',
    component: 'JCheckbox',
    componentProps:{
      dictCode:"COALITION_ILL"
    },
  },
  {
    label: '登记分类',
    field: 'registrationtypecode',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"SUSPICIOUS_PERSON_REGISTRATION",
      type: "radio"
    },
  },
  {
    label: '重点人群',
    field: 'isDetainCode',
    component: 'JCheckbox',
    componentProps:{
      dictCode:"IS_DETAIN_OTHER"
    },
  },
  {
    label: '其他人群聚集场所人员详述',
    field: 'isDetainQt',
    component: 'Input',
  },
  {
    label: '治疗分类',
    field: 'treatmenttypecode',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"TREATMENTTYPECODE",
      type: "radio"
    },
  },
  {
    label: '诊断医生',
    field: 'doctor',
    component: 'Input',
  },
  {
    label: '登记日期',
    field: 'tmregister',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '下次检查时间',
    field: 'dtNextCheck',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '是否基层管理',
    field: 'levelmanage',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"LEVELMANAGE",
      type: "radio"
    },
  },
  {
    label: '第一次入户时间',
    field: 'visitdate',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '是否接受抗结核治疗',
    field: 'isAcceptCure',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"DD_TB_IF_TREAT",
      type: "radio"
    },
  },
  {
    label: '未治疗原因',
    field: 'noTreatmentReasonCode',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"DOSSIERDR_CAUSENOCURE"
    },
  },
  {
    label: '等待治疗原因',
    field: 'waitTreatmentReasonCode',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"DOSSIERDR_WAITCURE"
    },
  },
  {
    label: '化疗方案',
    field: 'chemotherapyRegimen',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"DD_TB_CHEMOTHERAPY"
    },
  },
  {
    label: '服药管理地区',
    field: 'supervisionZoneCode',
    component: 'JAreaLinkage',
    componentProps: {
      saveCode: 'region',
    },
  },
  {
    label: '服药管理单位',
    field: 'supervisionOrgCode',
    component: 'Input',
  },
  {
    label: '治疗模式',
    field: 'treatmentModeCode',
    component: 'Input',
  },
  {
    label: '是否使用AmCm',
    field: 'amCm',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"LEVELMANAGE",
      type: "radio"
    },
  },
  {
    label: '是否使用Bedaquiline',
    field: 'bedaquiline',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"LEVELMANAGE",
      type: "radio"
    },
  },
  {
    label: '否使用FDC',
    field: 'isFdcCode',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"LEVELMANAGE",
      type: "radio"
    },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '不良反应出现日期',
    field: 'adroccurdate',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '停止治疗原因',
    field: 'endCause',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"END_CAUSE"
    },
  },
  {
    label: '停止治疗时间',
    field: 'tmEnd',
    component: 'DatePicker',
    componentProps:{
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '应服药次数',
    field: 'dosenumber',
    component: 'Input',
  },
  {
    label: '实际服药次数',
    field: 'realdosenumber',
    component: 'Input',
  },
  {
    label: '应访视次数',
    field: 'visitnumber',
    component: 'Input',
  },
  {
    label: '实际访视次数',
    field: 'realvisitnumber',
    component: 'Input',
  },
  {
    label: '实际服药管理方式',
    field: 'strengType',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"STRENG_TYPE",
      type: "radio"
    },
  },
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false
  },
];
//子表单数据
export const tbDossierThisvisitFormSchema: FormSchema[] = [
  {
    label: 'chiefNotes',
    field: 'chiefNotes',
    component: 'InputTextArea',
  },
  {
    label: '现病史',
    field: 'hpiNotes',
    component: 'InputTextArea',
  },
  {
    label: '本次发病日期',
    field: 'dtsymptomstart',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '本次首诊日期',
    field: 'dtCurrentCome',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '本次就诊时症状',
    field: 'symptomCase',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"samptomCase",
      type: "radio"
    },
  },
  {
    label: '本次就诊时其他症状',
    field: 'symptomCaseOther',
    component: 'Input',
  },
  {
    label: '抗结核治疗史',
    field: 'isAcceptCure',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"GZCODRIS_YN",
      type: "radio"
    },
  },
  {
    label: '服药开始时间',
    field: 'medicinestarttime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '服药结束时间',
    field: 'medicineendtime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '所用药物',
    field: 'takemedicine',
    component: 'JCheckbox',
    componentProps:{
      dictCode:"TAKEMEDICINE"
    },
  },
  {
    label: '卡介苗接种史',
    field: 'bcgVaccine',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"HASNOT",
      type: "radio"
    },
  },
  {
    label: '药品过敏史',
    field: 'isallergy',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"YES_NO",
      type: "radio"
    },
  },
  {
    label: '过敏药物',
    field: 'allergydrug',
    component: 'Input',
  },
  {
    label: '肺结核密切接触史',
    field: 'tbtouchhis',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"YES_NO",
      type: "radio"
    },
  },
  {
    label: '肝病史',
    field: 'liverishhis',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"HASNOT",
      type: "radio"
    },
  },
  {
    label: '肝病史简述',
    field: 'liverishhissketch',
    component: 'Input',
  },
  {
    label: '肾病史',
    field: 'nephropathy',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"HASNOT",
      type: "radio"
    },
  },
  {
    label: '肾病史简述',
    field: 'nephropathyhissketch',
    component: 'Input',
  },
  {
    label: '尘肺',
    field: 'pneumoconiosis',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"HASNOT",
      type: "radio"
    },
  },
  {
    label: '尘肺简述',
    field: 'pneumoconiosissketch',
    component: 'Input',
  },
  {
    label: '接尘史',
    field: 'pickupdust',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"YES_NO",
      type: "radio"
    },
  },
  {
    label: '接尘史简述',
    field: 'pickupdustsketch',
    component: 'Input',
  },
  {
    label: '糖尿病',
    field: 'diabetes',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"HASNOT",
      type: "radio"
    },
  },
  {
    label: '空腹血糖值',
    field: 'bloodsugar',
    component: 'Input',
  },
  {
    label: '糖化血红蛋白',
    field: 'hba1c',
    component: 'Input',
  },
  {
    label: '免疫系统疾病',
    field: 'immune',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"HASNOT",
      type: "radio"
    },
  },
  {
    label: '    免疫疾病',
    field: 'immunedisease',
    component: 'JCheckbox',
    componentProps:{
      dictCode:"immuneDisease"
    },
  },
  {
    label: '是否应用免疫抑制剂',
    field: 'immuneinhibitors',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"HASNOT",
      type: "radio"
    },
  },
  {
    label: '免疫抑制剂类型',
    field: 'immuneinhibitorstype',
    component: 'JCheckbox',
    componentProps:{
      dictCode:"immuneInhibitorsType"
    },
  },
  {
    label: '其他疾病',
    field: 'diseaseOther',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"HASNOT",
      type: "radio"
    },
  },
  {
    label: '其他免疫抑制剂类型',
    field: 'immuneinhibitorstypeother',
    component: 'Input',
  },
  {
    label: '其他免疫疾病',
    field: 'immunediseaseothetr',
    component: 'Input',
  },
  {
    label: '其他疾病简述',
    field: 'diseaseOtherDetail',
    component: 'Input',
  },
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false
  },
];
//子表表格配置
export const tbCaseCheckCureColumns: JVxeColumn[] = [
  {
    title: '治疗药物',
    key: 'therapeuticDrugs',
    type: JVxeTypes.selectMultiple,
    options:[],
    dictCode:"sys_dict_item  where  dict_id='MDRDUCSAMPLEwww' ,item_text,MDRDUCSAMPLE",
    width:"250px",
    placeholder: '请输入${title}',
    defaultValue:'',
  },
  {
    title: '治疗方案',
    key: 'treatmentPlanCode',
    type: JVxeTypes.select,
    options:[],
    dictCode:"CURE_SCHEME",
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
  },
  {
    title: '结束日期',
    key: 'endDate',
    type: JVxeTypes.date,
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
  },
  {
    title: '开始日期',
    key: 'startDate',
    type: JVxeTypes.date,
    width:"200px",
    placeholder: '请输入${title}',
    defaultValue:'',
  },
]


// 高级查询数据
export const superQuerySchema = {
  dossiertype: {title: '病案类别',order: 0,view: 'text', type: 'string',},
  registrationno: {title: '登记号',order: 1,view: 'text', type: 'string',},
  nowdiagnoseorgcode: {title: '现诊断单位',order: 3,view: 'text', type: 'string',},
  nowmanageorgcode: {title: '现管理单位',order: 5,view: 'text', type: 'string',},
  firstdiagnoseorgcode: {title: '首诊断单位',order: 7,view: 'text', type: 'string',},
  patientName: {title: '姓名',order: 10,view: 'text', type: 'string',},
  gendercode: {title: '性别',order: 11,view: 'radio', type: 'string',dictCode: 'GENDER_CODE',},
  registrationtypecode: {title: '登记分类',order: 47,view: 'radio', type: 'string',dictCode: 'SUSPICIOUS_PERSON_REGISTRATION',},
  treatmenttypecode: {title: '治疗分类',order: 50,view: 'radio', type: 'string',dictCode: 'TREATMENTTYPECODE',},
  tmregister: {title: '登记日期',order: 52,view: 'date', type: 'string',},
  //子表高级查询
  tbDossierThisvisit: {
    title: 'tb_dossier_thisvisit',
    view: 'table',
    fields: {
    }
  },
  tbCaseCheckCure: {
    title: 'tb_case_check_cure',
    view: 'table',
    fields: {
      therapeuticDrugs: {title: '治疗药物',order: 0,view: 'list_multi', type: 'string',dictTable: "sys_dict_item  where  dict_id='MDRDUCSAMPLEwww' ", dictCode: 'MDRDUCSAMPLE', dictText: 'item_text',},
      treatmentPlanCode: {title: '治疗方案',order: 1,view: 'list', type: 'string',dictCode: 'CURE_SCHEME',},
      endDate: {title: '结束日期',order: 3,view: 'date', type: 'string',},
      startDate: {title: '开始日期',order: 4,view: 'date', type: 'string',},
    }
  },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[]{
// 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
