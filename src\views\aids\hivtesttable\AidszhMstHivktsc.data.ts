import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '报告地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: '报告机构',
    align:"center",
    dataIndex: 'orgcode_dictText'
   },
   {
    title: '填表日期',
    align:"center",
    dataIndex: 'filltime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '填报年',
    align:"center",
    dataIndex: 'baonian_dictText'
   },
   {
    title: '填报月',
    align:"center",
    dataIndex: 'baoyue_dictText'
   },
   {
    title: '术前检测-筛查人次数',
    align:"center",
    dataIndex: 'shussum'
   },
   {
    title: '术前检测-筛查阳性人次数',
    align:"center",
    dataIndex: 'shusyangsum'
   },
   {
    title: '术前检测-确证检测数',
    align:"center",
    dataIndex: 'shuqsum'
   },
   {
    title: '术前检测-确证阳性数',
    align:"center",
    dataIndex: 'shuqyangsum'
   },
   {
    title: '受血（制品）前检测-筛查人次数',
    align:"center",
    dataIndex: 'soussum'
   },
   {
    title: '受血（制品）前检测-筛查阳性人次数',
    align:"center",
    dataIndex: 'sousyangsum'
   },
   {
    title: '受血（制品）前检测-确证检测数',
    align:"center",
    dataIndex: 'souqsum'
   },
   {
    title: '受血（制品）前检测-确证阳性数',
    align:"center",
    dataIndex: 'souqyangsum'
   },
   {
    title: '性病门诊-筛查人次数',
    align:"center",
    dataIndex: 'xinssum'
   },
   {
    title: '性病门诊-筛查阳性人次数',
    align:"center",
    dataIndex: 'xinsyangsum'
   },
   {
    title: '性病门诊-确证检测数',
    align:"center",
    dataIndex: 'xinqsum'
   },
   {
    title: '性病门诊-确证阳性数',
    align:"center",
    dataIndex: 'xinqyangsum'
   },
   {
    title: '其他就诊者检测-筛查人次数',
    align:"center",
    dataIndex: 'qissum'
   },
   {
    title: '其他就诊者检测-筛查阳性人次数',
    align:"center",
    dataIndex: 'qisyangsum'
   },
   {
    title: '其他就诊者检测-确证检测数',
    align:"center",
    dataIndex: 'qiqsum'
   },
   {
    title: '其他就诊者检测-确证阳性数',
    align:"center",
    dataIndex: 'qiqyangsum'
   },
   {
    title: '婚前检查（含涉外婚检）-筛查人次数',
    align:"center",
    dataIndex: 'hunssum'
   },
   {
    title: '婚前检查（含涉外婚检）-筛查阳性人次数',
    align:"center",
    dataIndex: 'hunsyangsum'
   },
   {
    title: '婚前检查（含涉外婚检）-确证检测数',
    align:"center",
    dataIndex: 'hunqsum'
   },
   {
    title: '婚前检查（含涉外婚检）-确证阳性数',
    align:"center",
    dataIndex: 'hunqyangsum'
   },
   {
    title: '孕产期检查-筛查人次数',
    align:"center",
    dataIndex: 'yunssum'
   },
   {
    title: '孕产期检查-筛查阳性人次数',
    align:"center",
    dataIndex: 'yunsyangsum'
   },
   {
    title: '孕产期检查-确证检测数',
    align:"center",
    dataIndex: 'yunqsum'
   },
   {
    title: '孕产期检查-确证阳性数',
    align:"center",
    dataIndex: 'yunqyangsum'
   },
   {
    title: '自愿咨询检测（vct）-筛查人次数',
    align:"center",
    dataIndex: 'zissum'
   },
   {
    title: '自愿咨询检测（vct）-筛查阳性人次数',
    align:"center",
    dataIndex: 'zisyangsum'
   },
   {
    title: '自愿咨询检测（vct）-确证检测数',
    align:"center",
    dataIndex: 'ziqsum'
   },
   {
    title: '自愿咨询检测（vct）-确证阳性数',
    align:"center",
    dataIndex: 'ziqyangsum'
   },
   {
    title: '阳性者配偶或性伴检测-筛查人次数',
    align:"center",
    dataIndex: 'yangssum'
   },
   {
    title: '阳性者配偶或性伴检测-筛查阳性人次数',
    align:"center",
    dataIndex: 'yangsyangsum'
   },
   {
    title: '阳性者配偶或性伴检测-确证检测数',
    align:"center",
    dataIndex: 'yangqsum'
   },
   {
    title: '阳性者配偶或性伴检测-确证阳性数',
    align:"center",
    dataIndex: 'yangqyangsum'
   },
   {
    title: '女性阳性者子女检测-筛查人次数',
    align:"center",
    dataIndex: 'nussum'
   },
   {
    title: '女性阳性者子女检测-筛查阳性人次数',
    align:"center",
    dataIndex: 'nusyangsum'
   },
   {
    title: '女性阳性者子女检测-确证检测数',
    align:"center",
    dataIndex: 'nuqsum'
   },
   {
    title: '女性阳性者子女检测-确证阳性数',
    align:"center",
    dataIndex: 'nuqyangsum'
   },
   {
    title: '职业暴露检测-筛查人次数',
    align:"center",
    dataIndex: 'zhissum'
   },
   {
    title: '职业暴露检测-筛查阳性人次数',
    align:"center",
    dataIndex: 'zhisyangsum'
   },
   {
    title: '职业暴露检测-确证检测数',
    align:"center",
    dataIndex: 'zhiqsum'
   },
   {
    title: '职业暴露检测-确证阳性数',
    align:"center",
    dataIndex: 'zhiqyangsum'
   },
   {
    title: '娱乐场所人员体检-筛查人次数',
    align:"center",
    dataIndex: 'yussum'
   },
   {
    title: '娱乐场所人员体检-筛查阳性人次数',
    align:"center",
    dataIndex: 'yusyangsum'
   },
   {
    title: '娱乐场所人员体检-确证检测数',
    align:"center",
    dataIndex: 'yuqsum'
   },
   {
    title: '娱乐场所人员体检-确证阳性数',
    align:"center",
    dataIndex: 'yuqyangsum'
   },
   {
    title: '有偿供血（浆）人员检测-筛查人次数',
    align:"center",
    dataIndex: 'youssum'
   },
   {
    title: '有偿供血（浆）人员检测-筛查阳性人次数',
    align:"center",
    dataIndex: 'yousyangsum'
   },
   {
    title: '有偿供血（浆）人员检测-确证检测数',
    align:"center",
    dataIndex: 'youqsum'
   },
   {
    title: '有偿供血（浆）人员检测-确证阳性数',
    align:"center",
    dataIndex: 'youqyangsum'
   },
   {
    title: '无偿献血人员检测-筛查人次数',
    align:"center",
    dataIndex: 'wussum'
   },
   {
    title: '无偿献血人员检测-筛查阳性人次数',
    align:"center",
    dataIndex: 'wusyangsum'
   },
   {
    title: '无偿献血人员检测-确证检测数',
    align:"center",
    dataIndex: 'wuqsum'
   },
   {
    title: '无偿献血人员检测-确证阳性数',
    align:"center",
    dataIndex: 'wuqyangsum'
   },
   {
    title: '出入境人员体检-筛查人次数',
    align:"center",
    dataIndex: 'chussum'
   },
   {
    title: '出入境人员体检-筛查阳性人次数',
    align:"center",
    dataIndex: 'chusyangsum'
   },
   {
    title: '出入境人员体检-确证检测数',
    align:"center",
    dataIndex: 'chuqsum'
   },
   {
    title: '出入境人员体检-确证阳性数',
    align:"center",
    dataIndex: 'chuqyangsum'
   },
   {
    title: '新兵体检-筛查人次数',
    align:"center",
    dataIndex: 'binssum'
   },
   {
    title: '新兵体检-筛查阳性人次数',
    align:"center",
    dataIndex: 'binsyangsum'
   },
   {
    title: '新兵体检-确证检测数',
    align:"center",
    dataIndex: 'binqsum'
   },
   {
    title: '新兵体检-确证阳性数',
    align:"center",
    dataIndex: 'binqyangsum'
   },
   {
    title: '强制/劳教戒毒人员检测-筛查人次数',
    align:"center",
    dataIndex: 'laossum'
   },
   {
    title: '强制/劳教戒毒人员检测-筛查阳性人次数',
    align:"center",
    dataIndex: 'laosyangsum'
   },
   {
    title: '强制/劳教戒毒人员检测-确证检测数',
    align:"center",
    dataIndex: 'laoqsum'
   },
   {
    title: '强制/劳教戒毒人员检测-确证阳性数',
    align:"center",
    dataIndex: 'laoqyangsum'
   },
   {
    title: '妇教所/女劳收教人员检测-筛查人次数',
    align:"center",
    dataIndex: 'fussum'
   },
   {
    title: '妇教所/女劳收教人员检测-筛查阳性人次数',
    align:"center",
    dataIndex: 'fusyangsum'
   },
   {
    title: '妇教所/女劳收教人员检测-确证检测数',
    align:"center",
    dataIndex: 'fuqsum'
   },
   {
    title: '妇教所/女劳收教人员检测-确证阳性数',
    align:"center",
    dataIndex: 'fuqyangsum'
   },
   {
    title: '其他羁押人员体检-筛查人次数',
    align:"center",
    dataIndex: 'jissum'
   },
   {
    title: '其他羁押人员体检-筛查阳性人次数',
    align:"center",
    dataIndex: 'jisyangsum'
   },
   {
    title: '其他羁押人员体检-确证检测数',
    align:"center",
    dataIndex: 'jiqsum'
   },
   {
    title: '其他羁押人员体检-确证阳性数',
    align:"center",
    dataIndex: 'jiqyangsum'
   },
   {
    title: '专题调查（请注明人群）-筛查人次数',
    align:"center",
    dataIndex: 'tissum'
   },
   {
    title: '专题调查（请注明人群）-筛查阳性人次数',
    align:"center",
    dataIndex: 'tisyangsum'
   },
   {
    title: '专题调查（请注明人群）-确证检测数',
    align:"center",
    dataIndex: 'tiqsum'
   },
   {
    title: '专题调查（请注明人群）-确证阳性数',
    align:"center",
    dataIndex: 'tiqyangsum'
   },
   {
    title: '其他（请注明人群）-筛查人次数',
    align:"center",
    dataIndex: 'renssum'
   },
   {
    title: '其他（请注明人群）-筛查阳性人次数',
    align:"center",
    dataIndex: 'rensyangsum'
   },
   {
    title: '其他（请注明人群）-确证检测数',
    align:"center",
    dataIndex: 'renqsum'
   },
   {
    title: '其他（请注明人群）-确证阳性数',
    align:"center",
    dataIndex: 'renqyangsum'
   },
   {
    title: '总计-筛查人次数',
    align:"center",
    dataIndex: 'zongssum'
   },
   {
    title: '总计-筛查阳性人次数',
    align:"center",
    dataIndex: 'zongsyangsum'
   },
   {
    title: '总计-确证检测数',
    align:"center",
    dataIndex: 'zongqsum'
   },
   {
    title: '总计-确证阳性数',
    align:"center",
    dataIndex: 'zongqyangsum'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '报告地区',
    field: 'zonecode',
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.orgcode=''
          //所属部门修改后更新负责部门下拉框数据
          
          updateSchema([
            {
              field: 'orgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'orgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
     {
      label: "填表日期",
      field: "filltime",
      component: 'RangePicker',
      componentProps: {
        valueType: 'Date',
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '报告地区',
    field: 'zonecode',
    required: true,
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.orgcode=''
          //所属部门修改后更新负责部门下拉框数据
          
          updateSchema([
            {
              field: 'orgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'orgcode',
    required: true,
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: '填报年',
    field: 'baonian',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_year_view,code,name"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入填报年!'},
          ];
     },
  },
  {
    label: '填报月',
    field: 'baoyue',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_monthCode"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入填报月!'},
          ];
     },
  },
  {
    label: '术前检测-筛查人次数',
    field: 'shussum',
    component: 'Input',
  },
  {
    label: '术前检测-筛查阳性人次数',
    field: 'shusyangsum',
    component: 'Input',
  },
  {
    label: '术前检测-确证检测数',
    field: 'shuqsum',
    component: 'Input',
  },
  {
    label: '术前检测-确证阳性数',
    field: 'shuqyangsum',
    component: 'Input',
  },
  {
    label: '受血（制品）前检测-筛查人次数',
    field: 'soussum',
    component: 'Input',
  },
  {
    label: '受血（制品）前检测-筛查阳性人次数',
    field: 'sousyangsum',
    component: 'Input',
  },
  {
    label: '受血（制品）前检测-确证检测数',
    field: 'souqsum',
    component: 'Input',
  },
  {
    label: '受血（制品）前检测-确证阳性数',
    field: 'souqyangsum',
    component: 'Input',
  },
  {
    label: '性病门诊-筛查人次数',
    field: 'xinssum',
    component: 'Input',
  },
  {
    label: '性病门诊-筛查阳性人次数',
    field: 'xinsyangsum',
    component: 'Input',
  },
  {
    label: '性病门诊-确证检测数',
    field: 'xinqsum',
    component: 'Input',
  },
  {
    label: '性病门诊-确证阳性数',
    field: 'xinqyangsum',
    component: 'Input',
  },
  {
    label: '其他就诊者检测-筛查人次数',
    field: 'qissum',
    component: 'Input',
  },
  {
    label: '其他就诊者检测-筛查阳性人次数',
    field: 'qisyangsum',
    component: 'Input',
  },
  {
    label: '其他就诊者检测-确证检测数',
    field: 'qiqsum',
    component: 'Input',
  },
  {
    label: '其他就诊者检测-确证阳性数',
    field: 'qiqyangsum',
    component: 'Input',
  },
  {
    label: '婚前检查（含涉外婚检）-筛查人次数',
    field: 'hunssum',
    component: 'Input',
  },
  {
    label: '婚前检查（含涉外婚检）-筛查阳性人次数',
    field: 'hunsyangsum',
    component: 'Input',
  },
  {
    label: '婚前检查（含涉外婚检）-确证检测数',
    field: 'hunqsum',
    component: 'Input',
  },
  {
    label: '婚前检查（含涉外婚检）-确证阳性数',
    field: 'hunqyangsum',
    component: 'Input',
  },
  {
    label: '孕产期检查-筛查人次数',
    field: 'yunssum',
    component: 'Input',
  },
  {
    label: '孕产期检查-筛查阳性人次数',
    field: 'yunsyangsum',
    component: 'Input',
  },
  {
    label: '孕产期检查-确证检测数',
    field: 'yunqsum',
    component: 'Input',
  },
  {
    label: '孕产期检查-确证阳性数',
    field: 'yunqyangsum',
    component: 'Input',
  },
  {
    label: '自愿咨询检测（vct）-筛查人次数',
    field: 'zissum',
    component: 'Input',
  },
  {
    label: '自愿咨询检测（vct）-筛查阳性人次数',
    field: 'zisyangsum',
    component: 'Input',
  },
  {
    label: '自愿咨询检测（vct）-确证检测数',
    field: 'ziqsum',
    component: 'Input',
  },
  {
    label: '自愿咨询检测（vct）-确证阳性数',
    field: 'ziqyangsum',
    component: 'Input',
  },
  {
    label: '阳性者配偶或性伴检测-筛查人次数',
    field: 'yangssum',
    component: 'Input',
  },
  {
    label: '阳性者配偶或性伴检测-筛查阳性人次数',
    field: 'yangsyangsum',
    component: 'Input',
  },
  {
    label: '阳性者配偶或性伴检测-确证检测数',
    field: 'yangqsum',
    component: 'Input',
  },
  {
    label: '阳性者配偶或性伴检测-确证阳性数',
    field: 'yangqyangsum',
    component: 'Input',
  },
  {
    label: '女性阳性者子女检测-筛查人次数',
    field: 'nussum',
    component: 'Input',
  },
  {
    label: '女性阳性者子女检测-筛查阳性人次数',
    field: 'nusyangsum',
    component: 'Input',
  },
  {
    label: '女性阳性者子女检测-确证检测数',
    field: 'nuqsum',
    component: 'Input',
  },
  {
    label: '女性阳性者子女检测-确证阳性数',
    field: 'nuqyangsum',
    component: 'Input',
  },
  {
    label: '职业暴露检测-筛查人次数',
    field: 'zhissum',
    component: 'Input',
  },
  {
    label: '职业暴露检测-筛查阳性人次数',
    field: 'zhisyangsum',
    component: 'Input',
  },
  {
    label: '职业暴露检测-确证检测数',
    field: 'zhiqsum',
    component: 'Input',
  },
  {
    label: '职业暴露检测-确证阳性数',
    field: 'zhiqyangsum',
    component: 'Input',
  },
  {
    label: '娱乐场所人员体检-筛查人次数',
    field: 'yussum',
    component: 'Input',
  },
  {
    label: '娱乐场所人员体检-筛查阳性人次数',
    field: 'yusyangsum',
    component: 'Input',
  },
  {
    label: '娱乐场所人员体检-确证检测数',
    field: 'yuqsum',
    component: 'Input',
  },
  {
    label: '娱乐场所人员体检-确证阳性数',
    field: 'yuqyangsum',
    component: 'Input',
  },
  {
    label: '有偿供血（浆）人员检测-筛查人次数',
    field: 'youssum',
    component: 'Input',
  },
  {
    label: '有偿供血（浆）人员检测-筛查阳性人次数',
    field: 'yousyangsum',
    component: 'Input',
  },
  {
    label: '有偿供血（浆）人员检测-确证检测数',
    field: 'youqsum',
    component: 'Input',
  },
  {
    label: '有偿供血（浆）人员检测-确证阳性数',
    field: 'youqyangsum',
    component: 'Input',
  },
  {
    label: '无偿献血人员检测-筛查人次数',
    field: 'wussum',
    component: 'Input',
  },
  {
    label: '无偿献血人员检测-筛查阳性人次数',
    field: 'wusyangsum',
    component: 'Input',
  },
  {
    label: '无偿献血人员检测-确证检测数',
    field: 'wuqsum',
    component: 'Input',
  },
  {
    label: '无偿献血人员检测-确证阳性数',
    field: 'wuqyangsum',
    component: 'Input',
  },
  {
    label: '出入境人员体检-筛查人次数',
    field: 'chussum',
    component: 'Input',
  },
  {
    label: '出入境人员体检-筛查阳性人次数',
    field: 'chusyangsum',
    component: 'Input',
  },
  {
    label: '出入境人员体检-确证检测数',
    field: 'chuqsum',
    component: 'Input',
  },
  {
    label: '出入境人员体检-确证阳性数',
    field: 'chuqyangsum',
    component: 'Input',
  },
  {
    label: '新兵体检-筛查人次数',
    field: 'binssum',
    component: 'Input',
  },
  {
    label: '新兵体检-筛查阳性人次数',
    field: 'binsyangsum',
    component: 'Input',
  },
  {
    label: '新兵体检-确证检测数',
    field: 'binqsum',
    component: 'Input',
  },
  {
    label: '新兵体检-确证阳性数',
    field: 'binqyangsum',
    component: 'Input',
  },
  {
    label: '强制/劳教戒毒人员检测-筛查人次数',
    field: 'laossum',
    component: 'Input',
  },
  {
    label: '强制/劳教戒毒人员检测-筛查阳性人次数',
    field: 'laosyangsum',
    component: 'Input',
  },
  {
    label: '强制/劳教戒毒人员检测-确证检测数',
    field: 'laoqsum',
    component: 'Input',
  },
  {
    label: '强制/劳教戒毒人员检测-确证阳性数',
    field: 'laoqyangsum',
    component: 'Input',
  },
  {
    label: '妇教所/女劳收教人员检测-筛查人次数',
    field: 'fussum',
    component: 'Input',
  },
  {
    label: '妇教所/女劳收教人员检测-筛查阳性人次数',
    field: 'fusyangsum',
    component: 'Input',
  },
  {
    label: '妇教所/女劳收教人员检测-确证检测数',
    field: 'fuqsum',
    component: 'Input',
  },
  {
    label: '妇教所/女劳收教人员检测-确证阳性数',
    field: 'fuqyangsum',
    component: 'Input',
  },
  {
    label: '其他羁押人员体检-筛查人次数',
    field: 'jissum',
    component: 'Input',
  },
  {
    label: '其他羁押人员体检-筛查阳性人次数',
    field: 'jisyangsum',
    component: 'Input',
  },
  {
    label: '其他羁押人员体检-确证检测数',
    field: 'jiqsum',
    component: 'Input',
  },
  {
    label: '其他羁押人员体检-确证阳性数',
    field: 'jiqyangsum',
    component: 'Input',
  },
  {
    label: '专题调查（请注明人群）-筛查人次数',
    field: 'tissum',
    component: 'Input',
  },
  {
    label: '专题调查（请注明人群）-筛查阳性人次数',
    field: 'tisyangsum',
    component: 'Input',
  },
  {
    label: '专题调查（请注明人群）-确证检测数',
    field: 'tiqsum',
    component: 'Input',
  },
  {
    label: '专题调查（请注明人群）-确证阳性数',
    field: 'tiqyangsum',
    component: 'Input',
  },
  {
    label: '其他（请注明人群）-筛查人次数',
    field: 'renssum',
    component: 'Input',
  },
  {
    label: '其他（请注明人群）-筛查阳性人次数',
    field: 'rensyangsum',
    component: 'Input',
  },
  {
    label: '其他（请注明人群）-确证检测数',
    field: 'renqsum',
    component: 'Input',
  },
  {
    label: '其他（请注明人群）-确证阳性数',
    field: 'renqyangsum',
    component: 'Input',
  },
  {
    label: '总计-筛查人次数',
    field: 'zongssum',
    component: 'Input',
  },
  {
    label: '总计-筛查阳性人次数',
    field: 'zongsyangsum',
    component: 'Input',
  },
  {
    label: '总计-确证检测数',
    field: 'zongqsum',
    component: 'Input',
  },
  {
    label: '总计-确证阳性数',
    field: 'zongqyangsum',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
/*  zonecode: {title: '报告地区',order: 0,view: 'text', type: 'string',},
  orgcode: {title: '报告机构',order: 1,view: 'text', type: 'string',}, */
  filltime: {title: '填表日期',order: 2,view: 'date', type: 'string',},
  baonian: {title: '填报年',order: 3,view: 'list', type: 'string',dictTable: "aids_year_view", dictCode: 'name', dictText: 'code',},
  baoyue: {title: '填报月',order: 4,view: 'list', type: 'string',dictCode: 'aids_monthCode',},
  shussum: {title: '术前检测-筛查人次数',order: 5,view: 'text', type: 'string',},
  shusyangsum: {title: '术前检测-筛查阳性人次数',order: 6,view: 'text', type: 'string',},
  shuqsum: {title: '术前检测-确证检测数',order: 7,view: 'text', type: 'string',},
  shuqyangsum: {title: '术前检测-确证阳性数',order: 8,view: 'text', type: 'string',},
  soussum: {title: '受血（制品）前检测-筛查人次数',order: 9,view: 'text', type: 'string',},
  sousyangsum: {title: '受血（制品）前检测-筛查阳性人次数',order: 10,view: 'text', type: 'string',},
  souqsum: {title: '受血（制品）前检测-确证检测数',order: 11,view: 'text', type: 'string',},
  souqyangsum: {title: '受血（制品）前检测-确证阳性数',order: 12,view: 'text', type: 'string',},
  xinssum: {title: '性病门诊-筛查人次数',order: 13,view: 'text', type: 'string',},
  xinsyangsum: {title: '性病门诊-筛查阳性人次数',order: 14,view: 'text', type: 'string',},
  xinqsum: {title: '性病门诊-确证检测数',order: 15,view: 'text', type: 'string',},
  xinqyangsum: {title: '性病门诊-确证阳性数',order: 16,view: 'text', type: 'string',},
  qissum: {title: '其他就诊者检测-筛查人次数',order: 17,view: 'text', type: 'string',},
  qisyangsum: {title: '其他就诊者检测-筛查阳性人次数',order: 18,view: 'text', type: 'string',},
  qiqsum: {title: '其他就诊者检测-确证检测数',order: 19,view: 'text', type: 'string',},
  qiqyangsum: {title: '其他就诊者检测-确证阳性数',order: 20,view: 'text', type: 'string',},
  hunssum: {title: '婚前检查（含涉外婚检）-筛查人次数',order: 21,view: 'text', type: 'string',},
  hunsyangsum: {title: '婚前检查（含涉外婚检）-筛查阳性人次数',order: 22,view: 'text', type: 'string',},
  hunqsum: {title: '婚前检查（含涉外婚检）-确证检测数',order: 23,view: 'text', type: 'string',},
  hunqyangsum: {title: '婚前检查（含涉外婚检）-确证阳性数',order: 24,view: 'text', type: 'string',},
  yunssum: {title: '孕产期检查-筛查人次数',order: 25,view: 'text', type: 'string',},
  yunsyangsum: {title: '孕产期检查-筛查阳性人次数',order: 26,view: 'text', type: 'string',},
  yunqsum: {title: '孕产期检查-确证检测数',order: 27,view: 'text', type: 'string',},
  yunqyangsum: {title: '孕产期检查-确证阳性数',order: 28,view: 'text', type: 'string',},
  zissum: {title: '自愿咨询检测（vct）-筛查人次数',order: 29,view: 'text', type: 'string',},
  zisyangsum: {title: '自愿咨询检测（vct）-筛查阳性人次数',order: 30,view: 'text', type: 'string',},
  ziqsum: {title: '自愿咨询检测（vct）-确证检测数',order: 31,view: 'text', type: 'string',},
  ziqyangsum: {title: '自愿咨询检测（vct）-确证阳性数',order: 32,view: 'text', type: 'string',},
  yangssum: {title: '阳性者配偶或性伴检测-筛查人次数',order: 33,view: 'text', type: 'string',},
  yangsyangsum: {title: '阳性者配偶或性伴检测-筛查阳性人次数',order: 34,view: 'text', type: 'string',},
  yangqsum: {title: '阳性者配偶或性伴检测-确证检测数',order: 35,view: 'text', type: 'string',},
  yangqyangsum: {title: '阳性者配偶或性伴检测-确证阳性数',order: 36,view: 'text', type: 'string',},
  nussum: {title: '女性阳性者子女检测-筛查人次数',order: 37,view: 'text', type: 'string',},
  nusyangsum: {title: '女性阳性者子女检测-筛查阳性人次数',order: 38,view: 'text', type: 'string',},
  nuqsum: {title: '女性阳性者子女检测-确证检测数',order: 39,view: 'text', type: 'string',},
  nuqyangsum: {title: '女性阳性者子女检测-确证阳性数',order: 40,view: 'text', type: 'string',},
  zhissum: {title: '职业暴露检测-筛查人次数',order: 41,view: 'text', type: 'string',},
  zhisyangsum: {title: '职业暴露检测-筛查阳性人次数',order: 42,view: 'text', type: 'string',},
  zhiqsum: {title: '职业暴露检测-确证检测数',order: 43,view: 'text', type: 'string',},
  zhiqyangsum: {title: '职业暴露检测-确证阳性数',order: 44,view: 'text', type: 'string',},
  yussum: {title: '娱乐场所人员体检-筛查人次数',order: 45,view: 'text', type: 'string',},
  yusyangsum: {title: '娱乐场所人员体检-筛查阳性人次数',order: 46,view: 'text', type: 'string',},
  yuqsum: {title: '娱乐场所人员体检-确证检测数',order: 47,view: 'text', type: 'string',},
  yuqyangsum: {title: '娱乐场所人员体检-确证阳性数',order: 48,view: 'text', type: 'string',},
  youssum: {title: '有偿供血（浆）人员检测-筛查人次数',order: 49,view: 'text', type: 'string',},
  yousyangsum: {title: '有偿供血（浆）人员检测-筛查阳性人次数',order: 50,view: 'text', type: 'string',},
  youqsum: {title: '有偿供血（浆）人员检测-确证检测数',order: 51,view: 'text', type: 'string',},
  youqyangsum: {title: '有偿供血（浆）人员检测-确证阳性数',order: 52,view: 'text', type: 'string',},
  wussum: {title: '无偿献血人员检测-筛查人次数',order: 53,view: 'text', type: 'string',},
  wusyangsum: {title: '无偿献血人员检测-筛查阳性人次数',order: 54,view: 'text', type: 'string',},
  wuqsum: {title: '无偿献血人员检测-确证检测数',order: 55,view: 'text', type: 'string',},
  wuqyangsum: {title: '无偿献血人员检测-确证阳性数',order: 56,view: 'text', type: 'string',},
  chussum: {title: '出入境人员体检-筛查人次数',order: 57,view: 'text', type: 'string',},
  chusyangsum: {title: '出入境人员体检-筛查阳性人次数',order: 58,view: 'text', type: 'string',},
  chuqsum: {title: '出入境人员体检-确证检测数',order: 59,view: 'text', type: 'string',},
  chuqyangsum: {title: '出入境人员体检-确证阳性数',order: 60,view: 'text', type: 'string',},
  binssum: {title: '新兵体检-筛查人次数',order: 61,view: 'text', type: 'string',},
  binsyangsum: {title: '新兵体检-筛查阳性人次数',order: 62,view: 'text', type: 'string',},
  binqsum: {title: '新兵体检-确证检测数',order: 63,view: 'text', type: 'string',},
  binqyangsum: {title: '新兵体检-确证阳性数',order: 64,view: 'text', type: 'string',},
  laossum: {title: '强制/劳教戒毒人员检测-筛查人次数',order: 65,view: 'text', type: 'string',},
  laosyangsum: {title: '强制/劳教戒毒人员检测-筛查阳性人次数',order: 66,view: 'text', type: 'string',},
  laoqsum: {title: '强制/劳教戒毒人员检测-确证检测数',order: 67,view: 'text', type: 'string',},
  laoqyangsum: {title: '强制/劳教戒毒人员检测-确证阳性数',order: 68,view: 'text', type: 'string',},
  fussum: {title: '妇教所/女劳收教人员检测-筛查人次数',order: 69,view: 'text', type: 'string',},
  fusyangsum: {title: '妇教所/女劳收教人员检测-筛查阳性人次数',order: 70,view: 'text', type: 'string',},
  fuqsum: {title: '妇教所/女劳收教人员检测-确证检测数',order: 71,view: 'text', type: 'string',},
  fuqyangsum: {title: '妇教所/女劳收教人员检测-确证阳性数',order: 72,view: 'text', type: 'string',},
  jissum: {title: '其他羁押人员体检-筛查人次数',order: 73,view: 'text', type: 'string',},
  jisyangsum: {title: '其他羁押人员体检-筛查阳性人次数',order: 74,view: 'text', type: 'string',},
  jiqsum: {title: '其他羁押人员体检-确证检测数',order: 75,view: 'text', type: 'string',},
  jiqyangsum: {title: '其他羁押人员体检-确证阳性数',order: 76,view: 'text', type: 'string',},
  tissum: {title: '专题调查（请注明人群）-筛查人次数',order: 77,view: 'text', type: 'string',},
  tisyangsum: {title: '专题调查（请注明人群）-筛查阳性人次数',order: 78,view: 'text', type: 'string',},
  tiqsum: {title: '专题调查（请注明人群）-确证检测数',order: 79,view: 'text', type: 'string',},
  tiqyangsum: {title: '专题调查（请注明人群）-确证阳性数',order: 80,view: 'text', type: 'string',},
  renssum: {title: '其他（请注明人群）-筛查人次数',order: 81,view: 'text', type: 'string',},
  rensyangsum: {title: '其他（请注明人群）-筛查阳性人次数',order: 82,view: 'text', type: 'string',},
  renqsum: {title: '其他（请注明人群）-确证检测数',order: 83,view: 'text', type: 'string',},
  renqyangsum: {title: '其他（请注明人群）-确证阳性数',order: 84,view: 'text', type: 'string',},
  zongssum: {title: '总计-筛查人次数',order: 85,view: 'text', type: 'string',},
  zongsyangsum: {title: '总计-筛查阳性人次数',order: 86,view: 'text', type: 'string',},
  zongqsum: {title: '总计-确证检测数',order: 87,view: 'text', type: 'string',},
  zongqyangsum: {title: '总计-确证阳性数',order: 88,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
