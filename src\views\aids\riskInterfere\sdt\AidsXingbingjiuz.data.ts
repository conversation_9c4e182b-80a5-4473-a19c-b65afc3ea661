import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '实施地区',
    align:"center",
    dataIndex: 'organizationRegion_dictText'
   },
   {
    title: '实施机构',
    align:"center",
    dataIndex: 'organizationName_dictText'
   },
   {
    title: '活动月度',
    align:"center",
    dataIndex: 'activityTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '干预覆盖人数',
    align:"center",
    dataIndex: 'rensum'
   },
   {
    title: '干预覆盖人次数',
    align:"center",
    dataIndex: 'rencisum'
   },
   {
    title: '发放宣传材料份数',
    align:"center",
    dataIndex: 'cailiaosum'
   },
   {
    title: '发放安全套数',
    align:"center",
    dataIndex: 'anquansum'
   },
   {
    title: '发放性病服务包数',
    align:"center",
    dataIndex: 'fuwusum'
   },
   {
    title: 'HIV检测本月检测人数',
    align:"center",
    dataIndex: 'jiancyuesum'
   },
   {
    title: 'HIV检测阳性人数',
    align:"center",
    dataIndex: 'yangxingsum'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "实施地区",
      field: 'organizationRegion',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    //所属部门修改后更新负责部门下拉框数据
                    formModel.organizationName=''
                    updateSchema([
                      {
                        field: 'organizationName',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
 	},
	{
      label: "实施机构",
      field: 'organizationName',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
 	},
     {
      label: "添加时间",
      field: "addtime",
      component: 'RangePicker',
      componentProps: {
        valueType: 'Date',
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  
  {
      label: "实施地区",
      field: 'organizationRegion',
      required:true,
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'organizationName',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "实施机构",
      field: 'organizationName',
      required:true,
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
    label: '活动月度',
    field: 'activityTime',
    component: 'DatePicker',
    componentProps: {
      //mode:'month',
      format: 'YYYY-MM',
      valueFormat: 'YYYY-MM'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入活动日期!'},
          ];
     },
  },
  {
    label: '干预覆盖人数',
    field: 'rensum',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^(?:[1-9]\d{0,3}|0)$/, message: '不符合校验规则!'},
          ];
     },
  },
  {
    label: '干预覆盖人次数',
    field: 'rencisum',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^(?:[1-9]\d{0,3}|0)$/, message: '不符合校验规则!'},
          ];
     },
  },
  {
    label: '发放宣传材料份数',
    field: 'cailiaosum',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^(?:[1-9]\d{0,3}|0)$/, message: '不符合校验规则!'},
          ];
     },
  },
  {
    label: '发放安全套数',
    field: 'anquansum',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^(?:[1-9]\d{0,3}|0)$/, message: '不符合校验规则!'},
          ];
     },
  },
  {
    label: '发放性病服务包数',
    field: 'fuwusum',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^(?:[1-9]\d{0,3}|0)$/, message: '不符合校验规则!'},
          ];
     },
  },
  {
    label: 'HIV检测本月检测人数',
    field: 'jiancyuesum',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^(?:[1-9]\d{0,3}|0)$/, message: '不符合校验规则!'},
          ];
     },
  },
  {
    label: 'HIV检测阳性人数',
    field: 'yangxingsum',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^(?:[1-9]\d{0,3}|0)$/, message: '不符合校验规则!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  rensum: {title: '干预覆盖人数',order: 3,view: 'text', type: 'string',},
  rencisum: {title: '干预覆盖人次数',order: 4,view: 'text', type: 'string',},
  cailiaosum: {title: '发放宣传材料份数',order: 5,view: 'text', type: 'string',},
  anquansum: {title: '发放安全套数',order: 6,view: 'text', type: 'string',},
  fuwusum: {title: '发放性病服务包数',order: 7,view: 'text', type: 'string',},
  jiancyuesum: {title: 'HIV检测本月检测人数',order: 8,view: 'text', type: 'string',},
  yangxingsum: {title: 'HIV检测阳性人数',order: 9,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
