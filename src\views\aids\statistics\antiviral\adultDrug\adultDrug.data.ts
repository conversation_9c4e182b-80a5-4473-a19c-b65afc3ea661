import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   
   {
    title: '地区',
    //align:"left",
    dataIndex: 'apanagecodes',
    width: 100,
   } ,
   {
    title: 'AZT',
    //align:"left",
    dataIndex: 'apanagecodes',
     children: [
       {
        title: '片剂/胶囊300mg',
        //align:"left",
        dataIndex: 'apanagecodes',
       width: 150,
       } ,
     ]
   } ,
   {
    title: '3TC',
    //align:"left",
    dataIndex: 'apanagecodes',
     children: [
       {
        title: '片剂300mg',
        //align:"left",
        dataIndex: 'apanagecodes',
       width: 150,
       } ,
     ]
   } ,
   {
    title: '3TC',
    //align:"left",
    dataIndex: 'apanagecodes',
     children: [
       {
        title: '片剂150mg',
        //align:"left",
        dataIndex: 'apanagecodes',
       width: 150,
       } ,
     ]
   } ,
   {
    title: 'EFV',
    //align:"left",
    dataIndex: 'apanagecodes',
     children: [
       {
        title: '片剂/胶囊600mg',
        //align:"left",
        dataIndex: 'apanagecodes',
       width: 150,
       } ,
     ]
   } ,
   {
    title: 'EFV',
    //align:"left",
    dataIndex: 'apanagecodes',
     children: [
       {
        title: '片剂200mg',
        //align:"left",
        dataIndex: 'apanagecodes',
       width: 150,
       } ,
     ]
   } ,
   {
    title: 'NVP',
    //align:"left",
    dataIndex: 'apanagecodes',
     children: [
       {
        title: '片剂200mg',
        //align:"left",
        dataIndex: 'apanagecodes',
       width: 150,
       } ,
     ]
   } ,
   {
    title: 'LPVR',
    //align:"left",
    dataIndex: 'apanagecodes',
     children: [
       {
        title: '片剂LPV200mg/RTV',
        //align:"left",
        dataIndex: 'apanagecodes',
       width: 150,
       } ,
     ]
   } ,
   {
    title: 'ABC',
    //align:"left",
    dataIndex: 'apanagecodes',
     children: [
       {
        title: '片剂300mg',
        //align:"left",
        dataIndex: 'apanagecodes',
       width: 150,
       } ,
     ]
   } ,
   {
    title: 'TDF',
    //align:"left",
    dataIndex: 'apanagecodes',
     children: [
       {
        title: '片剂300mg',
        //align:"left",
        dataIndex: 'apanagecodes',
       width: 150,
       } ,
     ]
   } ,
   {
    title: 'DTG',
    //align:"left",
    dataIndex: 'apanagecodes',
     children: [
       {
        title: '片剂50mg',
        //align:"left",
        dataIndex: 'apanagecodes',
       width: 150,
       } ,
     ]
   } ,
  {
   title: 'ATZ/3TC',
   //align:"left",
    dataIndex: 'apanagecodes',
    width: 150,
  } ,
   
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
      label: "报告地区",
      field: 'apanagecode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                 formModel.rptorgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      { 
                        field: 'rptorgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "报告机构",
      field: 'rptorgcode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '主索引ID',
    field: 'mpiId',
    component: 'Input',
  },
  {
    label: '检验项目代码',
    field: 'examinationItemCode',
    component: 'Input',
  },
  {
    label: '检验标本号',
    field: 'specimenNo',
    component: 'Input',
  },
  {
    label: '标本类别代码',
    field: 'specimenCategory',
    component: 'Input',
  },
  {
    label: '标本采样日期时间',
    field: 'specimenSamplingDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '接收标本日期时间',
    field: 'specimenReceivingDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '检测日期',
    field: 'examinationDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '检测方法类别代码',
    field: 'examinationMethodCode',
    component: 'Input',
  },
  {
    label: '检验定性结果代码',
    field: 'sourceExaminationResultCode',
    component: 'Input',
  },
  {
    label: '检验定量结果',
    field: 'examinationQuantification',
    component: 'Input',
  },
  {
    label: '检验定量结果计量单位',
    field: 'examinationQuantificationUnit',
    component: 'Input',
  },
  {
    label: '检验定量结果参考区间-上限',
    field: 'examinationQuantificationUpper',
    component: 'Input',
  },
  {
    label: '检验定量结果参考区间-下限',
    field: 'examinationQuantificationLower',
    component: 'Input',
  },
  {
    label: '检验定量结果超出或低于参考值',
    field: 'examinationQuantificationRi',
    component: 'InputNumber',
  },
  {
    label: '检测报告日期',
    field: 'examinationReportDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '月序',
    field: 'month',
    component: 'Input',
  },
  {
    label: '确诊状态',
    field: 'confirmStatusCode',
    component: 'InputNumber',
  },
  {
    label: '创建机构',
    field: 'createOrg',
    component: 'InputNumber',
  },
  {
    label: '修改机构',
    field: 'updateOrg',
    component: 'InputNumber',
  },
  {
    label: '创建人',
    field: 'createUser',
    component: 'Input',
  },
  {
    label: '修改人',
    field: 'updateUser',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  mpiId: {title: '主索引ID',order: 0,view: 'text', type: 'string',},
  examinationItemCode: {title: '检验项目代码',order: 1,view: 'text', type: 'string',},
  specimenNo: {title: '检验标本号',order: 2,view: 'text', type: 'string',},
  specimenCategory: {title: '标本类别代码',order: 3,view: 'text', type: 'string',},
  specimenSamplingDate: {title: '标本采样日期时间',order: 4,view: 'date', type: 'string',},
  specimenReceivingDate: {title: '接收标本日期时间',order: 5,view: 'date', type: 'string',},
  examinationDate: {title: '检测日期',order: 6,view: 'date', type: 'string',},
  examinationMethodCode: {title: '检测方法类别代码',order: 7,view: 'text', type: 'string',},
  sourceExaminationResultCode: {title: '检验定性结果代码',order: 8,view: 'text', type: 'string',},
  examinationQuantification: {title: '检验定量结果',order: 9,view: 'text', type: 'string',},
  examinationQuantificationUnit: {title: '检验定量结果计量单位',order: 10,view: 'text', type: 'string',},
  examinationQuantificationUpper: {title: '检验定量结果参考区间-上限',order: 11,view: 'text', type: 'string',},
  examinationQuantificationLower: {title: '检验定量结果参考区间-下限',order: 12,view: 'text', type: 'string',},
  examinationQuantificationRi: {title: '检验定量结果超出或低于参考值',order: 13,view: 'number', type: 'number',},
  examinationReportDate: {title: '检测报告日期',order: 14,view: 'date', type: 'string',},
  month: {title: '月序',order: 15,view: 'text', type: 'string',},
  confirmStatusCode: {title: '确诊状态',order: 16,view: 'number', type: 'number',},
  createOrg: {title: '创建机构',order: 17,view: 'number', type: 'number',},
  updateOrg: {title: '修改机构',order: 18,view: 'number', type: 'number',},
  createUser: {title: '创建人',order: 19,view: 'text', type: 'string',},
  updateUser: {title: '修改人',order: 20,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
