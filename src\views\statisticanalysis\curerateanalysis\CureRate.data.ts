import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
   title: '治疗日期',
   align:"center",
   dataIndex: 'diagnoseTime'
  },
   {
    title: '患者总数',
    align:"center",
    dataIndex: 'patientNum'
   },
   {
    title: '治愈总数',
    align:"center",
    dataIndex: 'cureNum'
   },
   {
    title: '治愈率',
    align:"center",
    dataIndex: 'cureRate'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
      label: "治疗日期开始",
      field: 'createTimeBegin',
      component: 'DatePicker',
      colProps: {span: 7},
  } ,
  {
      label: "治疗日期结束",
      field: 'createTimeEnd',
      component: 'DatePicker',
      colProps: {span: 7},
  } 
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '患者总数',
    field: 'patientNum',
    component: 'Input',
  },
  {
    label: '治愈总数',
    field: 'cureNum',
    component: 'Input',
  },
  {
    label: '治愈率',
    field: 'cureRate',
    component: 'Input',
  },
];

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
