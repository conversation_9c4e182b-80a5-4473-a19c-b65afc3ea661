import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '所属地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: '所属机构',
    align:"center",
    dataIndex: 'orgcode_dictText'
   },
   {
    title: '药品名称',
    align:"center",
    dataIndex: 'drugName_dictText'
   },
   {
    title: '时间',
    align:"center",
    dataIndex: 'datetime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '前日剩余量',
    align:"center",
    dataIndex: 'surplusRianRi'
   },
   {
    title: '当日出库桶数',
    align:"center",
    dataIndex: 'surplusDangRi'
   },
   {
    title: '实际使用量',
    align:"center",
    dataIndex: 'actualUsage'
   },
   {
    title: '处方量',
    align:"center",
    dataIndex: 'prescriptionQuantity'
   },
   {
    title: '当日耗损量',
    align:"center",
    dataIndex: 'dailyConsumption'
   },
   {
    title: '累计耗损量',
    align:"center",
    dataIndex: 'accumCnsumption'
   },
   {
    title: '剩余量',
    align:"center",
    dataIndex: 'surplus'
   },
   {
    title: '库存量',
    align:"center",
    dataIndex: 'inventory'
   },
   {
    title: '双签1',
    align:"center",
    dataIndex: 'doubleSignature'
   },
   {
    title: '双签2',
    align:"center",
    dataIndex: 'doubleSignature2'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '报告地区',
    field: 'zonecode',
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.orgcode=''
          //所属部门修改后更新负责部门下拉框数据
          
          updateSchema([
            {
              field: 'orgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'orgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
      label: "时间",
      field: "datetime",
      component: 'RangePicker',
      componentProps: {
        valueType: 'Date',
      },
      //colProps: {span: 6},
  },
	{
      label: "药品名称",
      field: 'drugName',
      component: 'JDictSelectTag',
      componentProps:{
          dictCode:"aids_drugCode"
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '报告地区',
    field: 'zonecode',
    required: true,
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.orgcode=''
          //所属部门修改后更新负责部门下拉框数据
          
          updateSchema([
            {
              field: 'orgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'orgcode',
    required: true,
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: '药品名称',
    field: 'drugName',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_drugCode"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入药品名称!'},
          ];
     },
  },
  {
    label: '时间',
    field: 'datetime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入时间!'},
          ];
     },
  },
  {
    label: '前日剩余量',
    field: 'surplusRianRi',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^-?\d+$/, message: '请输入整数!'},
          ];
     },
  },
  {
    label: '当日出库桶数',
    field: 'surplusDangRi',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^-?\d+$/, message: '请输入整数!'},
          ];
     },
  },
  {
    label: '实际使用量',
    field: 'actualUsage',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^-?\d+$/, message: '请输入整数!'},
          ];
     },
  },
  {
    label: '处方量',
    field: 'prescriptionQuantity',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^-?\d+$/, message: '请输入整数!'},
          ];
     },
  },
  {
    label: '当日耗损量',
    field: 'dailyConsumption',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^-?\d+$/, message: '请输入整数!'},
          ];
     },
  },
  {
    label: '累计耗损量',
    field: 'accumCnsumption',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^-?\d+$/, message: '请输入整数!'},
          ];
     },
  },
  {
    label: '剩余量',
    field: 'surplus',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^-?\d+$/, message: '请输入整数!'},
          ];
     },
  },
  {
    label: '库存量',
    field: 'inventory',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^-?\d+$/, message: '请输入整数!'},
          ];
     },
  },
  {
    label: '双签1',
    field: 'doubleSignature',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^.{1,20}$/, message: '请输入1到20位任意字符!'},
          ];
     },
  },
  {
    label: '双签2',
    field: 'doubleSignature2',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^.{1,20}$/, message: '请输入1到20位任意字符!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
 /* zonecode: {title: '所属地区',order: 0,view: 'text', type: 'string',},
  orgcode: {title: '所属机构',order: 1,view: 'text', type: 'string',}, */
  drugName: {title: '药品名称',order: 2,view: 'list', type: 'string',dictCode: 'aids_drugCode',},
  datetime: {title: '时间',order: 3,view: 'date', type: 'string',},
  surplusRianRi: {title: '前日剩余量',order: 4,view: 'text', type: 'string',},
  surplusDangRi: {title: '当日出库桶数',order: 5,view: 'text', type: 'string',},
  actualUsage: {title: '实际使用量',order: 6,view: 'text', type: 'string',},
  prescriptionQuantity: {title: '处方量',order: 7,view: 'text', type: 'string',},
  dailyConsumption: {title: '当日耗损量',order: 8,view: 'text', type: 'string',},
  accumCnsumption: {title: '累计耗损量',order: 9,view: 'text', type: 'string',},
  surplus: {title: '剩余量',order: 10,view: 'text', type: 'string',},
  inventory: {title: '库存量',order: 11,view: 'text', type: 'string',},
  doubleSignature: {title: '双签1',order: 12,view: 'text', type: 'string',},
  doubleSignature2: {title: '双签2',order: 13,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
