import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '编号',
    align:"center",
    dataIndex: 'recordId'
   },
   {
    title: '实施地区',
    align:"center",
    dataIndex: 'organizationRegion_dictText'
   },
   {
    title: '实施机构',
    align:"center",
    dataIndex: 'organizationName_dictText'
   },
   {
    title: '化名/姓名',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '年龄（岁）',
    align:"center",
    dataIndex: 'age'
   },
   {
    title: '联系电话',
    align:"center",
    dataIndex: 'contactNumber'
   },
   {
    title: '检测日期',
    align:"center",
    dataIndex: 'time',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '检测结果',
    align:"center",
    dataIndex: 'result'
   },
   {
    title: '结果告知',
    align:"center",
    dataIndex: 'inform_dictText'
   },
   {
    title: '转介复查',
    align:"center",
    dataIndex: 'referral_dictText'
   },
   {
    title: '是否本年首次检测',
    align:"center",
    dataIndex: 'firstTesting_dictText'
   },
   {
    title: '签名',
    align:"center",
    dataIndex: 'autograph'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
      label: "实施地区",
      field: 'organizationRegion',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    //所属部门修改后更新负责部门下拉框数据
                    formModel.organizationName=''
                    updateSchema([
                      {
                        field: 'organizationName',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "实施机构",
      field: 'organizationName',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
      label: "化名/姓名",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
  },
     {
      label: "添加时间",
      field: "addtime",
      component: 'RangePicker',
      componentProps: {
        valueType: 'Date',
      },
      //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
      label: "实施地区",
      field: 'organizationRegion',
      required:true,
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'organizationName',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "实施机构",
      field: 'organizationName',
      required:true,
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
    label: '编号',
    field: 'recordId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入编号!'},
          ];
     },
  },
  {
    label: '组织名称',
    field: 'zuzhiname',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { pattern: /^.{0,20}$/, message: '请输入0到20位任意字符!'},
          ];
     },
  },
  {
    label: '化名/姓名',
    field: 'name',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入化名/姓名!'},
                 { pattern: /^.{1,20}$/, message: '请输入1到20位任意字符!'},
          ];
     },
  },
  {
    label: '年龄（岁）',
    field: 'age',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入年龄（岁）!'},
                 { pattern: /^([0-9]|[1-9][0-9]|[1-9][0-9][0-9])$/, message: '请输入正确年龄!'},
          ];
     },
  },
  {
    label: '联系电话',
    field: 'contactNumber',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码!'},
          ];
     },
  },
  {
    label: '检测日期',
    field: 'time',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入检测日期!'},
          ];
     },
  },
  {
    label: '检测结果',
    field: 'result',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入检测结果!'},
                 { pattern: /^.{1,100}$/, message: '请输入1到100位任意字符!'},
          ];
     },
  },
  {
    label: '结果告知',
    field: 'inform',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_gaozhi",
        type: "radio"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入结果告知!'},
          ];
     },
  },
  {
    label: '转介复查',
    field: 'referral',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_shifou",
        type: "radio"
     },
  },
  {
    label: '是否本年首次检测',
    field: 'firstTesting',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_shifou",
        type: "radio"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入是否本年首次检测!'},
          ];
     },
  },
  {
    label: '签名',
    field: 'autograph',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入签名!'},
                  { pattern: /^.{1,20}$/, message: '请输入1到20位任意字符!'},
          ];
     },
  },
  {
    label: '备注',
    field: 'beizhu',
    component: 'InputTextArea',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^.{0,100}$/, message: '请输入0到100位任意字符!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  recordId: {title: '编号',order: 0,view: 'text', type: 'string',},
  name: {title: '化名/姓名',order: 1,view: 'text', type: 'string',},
  age: {title: '年龄（岁）',order: 2,view: 'text', type: 'string',},
  contactNumber: {title: '联系电话',order: 3,view: 'text', type: 'string',},
  time: {title: '检测日期',order: 4,view: 'date', type: 'string',},
  result: {title: '检测结果',order: 5,view: 'text', type: 'string',},
  inform: {title: '结果告知',order: 6,view: 'radio', type: 'string',dictCode: 'aids_gaozhi',},
  referral: {title: '转介复查',order: 7,view: 'radio', type: 'string',dictCode: 'aids_shifou',},
  firstTesting: {title: '是否本年首次检测',order: 8,view: 'radio', type: 'string',dictCode: 'aids_shifou',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
