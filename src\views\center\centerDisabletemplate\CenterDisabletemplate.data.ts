import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '模板名称',
    align: "center",
    dataIndex: 'templatename'
  },
  {
    title: '模板状态',
    align: "center",
    dataIndex: 'state_dictText'
  },
  {
    title: '操作类型',
    align: "center",
    dataIndex: 'operationtype'
  },
  {
    title: '操作人',
    align: "center",
    dataIndex: 'operator'
  },
  {
    title: '操作时间',
    align: "center",
    dataIndex: 'operationtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '备注',
    align: "center",
    dataIndex: 'remarks'
  },
  {
    title: '禁用启用',
    align: "center",
    dataIndex: 'states_dictText'
  },
];

// 高级查询数据
export const superQuerySchema = {
  templatename: {title: '模板名称',order: 0,view: 'text', type: 'string',},
  state: {title: '模板状态',order: 1,view: 'list', type: 'string',dictCode: 'TEMPLATE__STATE',},
  operationtype: {title: '操作类型',order: 2,view: 'text', type: 'string',},
  operator: {title: '操作人',order: 3,view: 'text', type: 'string',},
  operationtime: {title: '操作时间',order: 4,view: 'date', type: 'string',},
  remarks: {title: '备注',order: 5,view: 'text', type: 'string',},
  states: {title: '禁用启用',order: 6,view: 'list', type: 'string',dictCode: 'STATESJY',},
};
