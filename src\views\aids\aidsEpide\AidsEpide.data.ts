import {BasicColumn} from '/src/components/Table';
import {FormSchema} from '/src/components/Table';
import { rules} from '/src/utils/helper/validator';
import { render } from '/src/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/src/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '国标码',
    align:"center",
    dataIndex: 'nationnum'
   },
   {
    title: '病例编码',
    align:"center",
    dataIndex: 'patientnum'
   },
   {
    title: '个案编号',
    align:"center",
    dataIndex: 'cardnum'
   },
   {
    title: '首次确认抗体阳性时间',
    align:"center",
    dataIndex: 'firsttime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'HIV抗体阳性报告编号',
    align:"center",
    dataIndex: 'reportnum'
   },
   {
    title: '姓名',
    align:"center",
    dataIndex: 'pname'
   },
   {
    title: '身份证号',
    align:"center",
    dataIndex: 'idcard'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'gender_dictText'
   },
   {
    title: '年龄',
    align:"center",
    dataIndex: 'page'
   },
   {
    title: '出生日期',
    align:"center",
    dataIndex: 'birthdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '居住地址',
    align:"center",
    dataIndex: 'livingaddr'
   },
   {
    title: '发病日期',
    align:"center",
    dataIndex: 'diagdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '民族',
    align:"center",
    dataIndex: 'nation_dictText'
   },
   {
    title: '职业',
    align:"center",
    dataIndex: 'job_dictText'
   },
   {
    title: '文化程度',
    align:"center",
    dataIndex: 'education_dictText'
   },
   {
    title: '受教育年限',
    align:"center",
    dataIndex: 'eduyear'
   },
   {
    title: '婚姻状况',
    align:"center",
    dataIndex: 'marri_dictText'
   },
   {
    title: '有无献血浆史',
    align:"center",
    dataIndex: 'historyone_dictText'
   },
   {
    title: '有无受血史',
    align:"center",
    dataIndex: 'historytwo_dictText'
   },
   {
    title: '有无性病史',
    align:"center",
    dataIndex: 'historythree_dictText'
   },
   {
    title: '有无嫖娼或卖淫史',
    align:"center",
    dataIndex: 'historyfour_dictText'
   },
   {
    title: '除配偶外，有无其他性伴',
    align:"center",
    dataIndex: 'historyfive_dictText'
   },
   {
    title: '是否吸过毒',
    align:"center",
    dataIndex: 'historysix_dictText'
   },
   {
    title: '有无手术史',
    align:"center",
    dataIndex: 'historyseven_dictText'
   },
   {
    title: '是否拔过或修补过牙齿',
    align:"center",
    dataIndex: 'historyeight_dictText'
   },
   {
    title: '有无文身',
    align:"center",
    dataIndex: 'historynine_dictText'
   },
   {
    title: '有无穿耳洞',
    align:"center",
    dataIndex: 'historyten_dictText'
   },
   {
    title: '其他就医行为',
    align:"center",
    dataIndex: 'historyother'
   },
   {
    title: '目前的临床表现',
    align:"center",
    dataIndex: 'symptom1'
   },
   {
    title: '首次出现相关症状的时间',
    align:"center",
    dataIndex: 'symptom2',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '进行性体重下降>10%',
    align:"center",
    dataIndex: 'symptom3_dictText'
   },
   {
    title: '持续或间歇性腹泻至少一个月',
    align:"center",
    dataIndex: 'symptom4_dictText'
   },
   {
    title: '发烧至少一个月',
    align:"center",
    dataIndex: 'symptom5_dictText'
   },
   {
    title: '口腔炎症或溃疡',
    align:"center",
    dataIndex: 'symptom6_dictText'
   },
   {
    title: '持续性全身淋巴结肿大',
    align:"center",
    dataIndex: 'symptom7_dictText'
   },
   {
    title: '持续咳嗽一个月以上',
    align:"center",
    dataIndex: 'symptom8_dictText'
   },
   {
    title: '单纯疱疹或带状疱疹慢性反复发作',
    align:"center",
    dataIndex: 'symptom9_dictText'
   },
   {
    title: '全身瘙痒性皮疹',
    align:"center",
    dataIndex: 'symptom10_dictText'
   },
   {
    title: '妇女浸润性宫颈癌',
    align:"center",
    dataIndex: 'symptom11_dictText'
   },
   {
    title: '反应迟钝或痴呆',
    align:"center",
    dataIndex: 'symptom12_dictText'
   },
   {
    title: '运动神经功能障碍',
    align:"center",
    dataIndex: 'symptom13_dictText'
   },
   {
    title: '反复感染',
    align:"center",
    dataIndex: 'symptom14_dictText'
   },
   {
    title: '儿童发育迟缓',
    align:"center",
    dataIndex: 'symptom15_dictText'
   },
   {
    title: '结核',
    align:"center",
    dataIndex: 'symptom16_dictText'
   },
   {
    title: '体格检查情况',
    align:"center",
    dataIndex: 'bodycheckresult'
   },
   {
    title: '配偶或性伴基本情况',
    align:"center",
    dataIndex: 'blbaseinfo'
   },
   {
    title: '配偶或性伴暴露因素情况',
    align:"center",
    dataIndex: 'blexposeinfo'
   },
   {
    title: '子女情况',
    align:"center",
    dataIndex: 'childinfo'
   },
   {
    title: '其他补充情况',
    align:"center",
    dataIndex: 'otherinfo'
   },
   {
    title: '调查单位',
    align:"center",
    dataIndex: 'researchunit'
   },
   {
    title: '调查者',
    align:"center",
    dataIndex: 'research'
   },
   {
    title: '审核者',
    align:"center",
    dataIndex: 'auditer'
   },
   {
    title: '调查时间',
    align:"center",
    dataIndex: 'audittime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "国标码",
      field: 'nationnum',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "病例编码",
      field: 'patientnum',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "个案编号",
      field: 'cardnum',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "姓名",
      field: 'pname',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "调查单位",
      field: 'researchunit',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "调查者",
      field: 'research',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '国标码',
    field: 'nationnum',
    component: 'Input',
  },
  {
    label: '病例编码',
    field: 'patientnum',
    component: 'Input',
  },
  {
    label: '个案编号',
    field: 'cardnum',
    component: 'Input',
  },
  {
    label: '首次确认抗体阳性时间',
    field: 'firsttime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'HIV抗体阳性报告编号',
    field: 'reportnum',
    component: 'Input',
  },
  {
    label: '姓名',
    field: 'pname',
    component: 'Input',
  },
  {
    label: '身份证号',
    field: 'idcard',
    component: 'Input',
  },
  {
    label: '性别',
    field: 'gender',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sex",
        type: "radio"
     },
  },
  {
    label: '年龄',
    field: 'page',
    component: 'Input',
  },
  {
    label: '出生日期',
    field: 'birthdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '居住地址',
    field: 'livingaddr',
    component: 'Input',
  },
  {
    label: '发病日期',
    field: 'diagdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '民族',
    field: 'nation',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_nation"
     },
  },
  {
    label: '职业',
    field: 'job',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_job"
     },
  },
  {
    label: '文化程度',
    field: 'education',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_culture"
     },
  },
  {
    label: '受教育年限',
    field: 'eduyear',
    component: 'Input',
  },
  {
    label: '婚姻状况',
    field: 'marri',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_marriage"
     },
  },
  {
    label: '有无献血浆史',
    field: 'historyone',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '有无受血史',
    field: 'historytwo',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '有无性病史',
    field: 'historythree',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '有无嫖娼或卖淫史',
    field: 'historyfour',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '除配偶外，有无其他性伴',
    field: 'historyfive',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '是否吸过毒',
    field: 'historysix',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_shifou",
        type: "radio"
     },
  },
  {
    label: '有无手术史',
    field: 'historyseven',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '是否拔过或修补过牙齿',
    field: 'historyeight',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_shifou",
        type: "radio"
     },
  },
  {
    label: '有无文身',
    field: 'historynine',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '有无穿耳洞',
    field: 'historyten',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '其他就医行为',
    field: 'historyother',
    component: 'InputTextArea',
  },
  {
    label: '目前的临床表现',
    field: 'symptom1',
    component: 'Input',
  },
  {
    label: '首次出现相关症状的时间',
    field: 'symptom2',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '进行性体重下降>10%',
    field: 'symptom3',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '持续或间歇性腹泻至少一个月',
    field: 'symptom4',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '发烧至少一个月',
    field: 'symptom5',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '口腔炎症或溃疡',
    field: 'symptom6',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '持续性全身淋巴结肿大',
    field: 'symptom7',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '持续咳嗽一个月以上',
    field: 'symptom8',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '单纯疱疹或带状疱疹慢性反复发作',
    field: 'symptom9',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '全身瘙痒性皮疹',
    field: 'symptom10',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '妇女浸润性宫颈癌',
    field: 'symptom11',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '反应迟钝或痴呆',
    field: 'symptom12',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '运动神经功能障碍',
    field: 'symptom13',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '反复感染',
    field: 'symptom14',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '儿童发育迟缓',
    field: 'symptom15',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '结核',
    field: 'symptom16',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '体格检查情况',
    field: 'bodycheckresult',
    component: 'InputTextArea',
  },
  {
    label: '配偶或性伴基本情况',
    field: 'blbaseinfo',
    component: 'InputTextArea',
  },
  {
    label: '配偶或性伴暴露因素情况',
    field: 'blexposeinfo',
    component: 'InputTextArea',
  },
  {
    label: '子女情况',
    field: 'childinfo',
    component: 'InputTextArea',
  },
  {
    label: '其他补充情况',
    field: 'otherinfo',
    component: 'InputTextArea',
  },
  {
    label: '调查单位',
    field: 'researchunit',
    component: 'Input',
  },
  {
    label: '调查者',
    field: 'research',
    component: 'Input',
  },
  {
    label: '审核者',
    field: 'auditer',
    component: 'Input',
  },
  {
    label: '调查时间',
    field: 'audittime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  nationnum: {title: '国标码',order: 0,view: 'text', type: 'string',},
  patientnum: {title: '病例编码',order: 1,view: 'text', type: 'string',},
  cardnum: {title: '个案编号',order: 2,view: 'text', type: 'string',},
  firsttime: {title: '首次确认抗体阳性时间',order: 3,view: 'date', type: 'string',},
  reportnum: {title: 'HIV抗体阳性报告编号',order: 4,view: 'text', type: 'string',},
  pname: {title: '姓名',order: 5,view: 'text', type: 'string',},
  idcard: {title: '身份证号',order: 6,view: 'text', type: 'string',},
  gender: {title: '性别',order: 7,view: 'radio', type: 'string',dictCode: 'aids_sex',},
  page: {title: '年龄',order: 8,view: 'text', type: 'string',},
  birthdate: {title: '出生日期',order: 9,view: 'date', type: 'string',},
  livingaddr: {title: '居住地址',order: 10,view: 'text', type: 'string',},
  diagdate: {title: '发病日期',order: 11,view: 'date', type: 'string',},
  nation: {title: '民族',order: 12,view: 'list', type: 'string',dictCode: 'aids_nation',},
  job: {title: '职业',order: 13,view: 'list', type: 'string',dictCode: 'aids_job',},
  education: {title: '文化程度',order: 14,view: 'list', type: 'string',dictCode: 'aids_mas_dd_culture',},
  eduyear: {title: '受教育年限',order: 15,view: 'text', type: 'string',},
  marri: {title: '婚姻状况',order: 16,view: 'list', type: 'string',dictCode: 'aids_mas_dd_marriage',},
  historyone: {title: '有无献血浆史',order: 17,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  historytwo: {title: '有无受血史',order: 18,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  historythree: {title: '有无性病史',order: 19,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  historyfour: {title: '有无嫖娼或卖淫史',order: 20,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  historyfive: {title: '除配偶外，有无其他性伴',order: 21,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  historysix: {title: '是否吸过毒',order: 22,view: 'radio', type: 'string',dictCode: 'aids_shifou',},
  historyseven: {title: '有无手术史',order: 23,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  historyeight: {title: '是否拔过或修补过牙齿',order: 24,view: 'radio', type: 'string',dictCode: 'aids_shifou',},
  historynine: {title: '有无文身',order: 25,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  historyten: {title: '有无穿耳洞',order: 26,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  historyother: {title: '其他就医行为',order: 27,view: 'textarea', type: 'string',},
  symptom1: {title: '目前的临床表现',order: 28,view: 'text', type: 'string',},
  symptom2: {title: '首次出现相关症状的时间',order: 29,view: 'date', type: 'string',},
  symptom3: {title: '进行性体重下降>10%',order: 30,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom4: {title: '持续或间歇性腹泻至少一个月',order: 31,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom5: {title: '发烧至少一个月',order: 32,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom6: {title: '口腔炎症或溃疡',order: 33,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom7: {title: '持续性全身淋巴结肿大',order: 34,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom8: {title: '持续咳嗽一个月以上',order: 35,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom9: {title: '单纯疱疹或带状疱疹慢性反复发作',order: 36,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom10: {title: '全身瘙痒性皮疹',order: 37,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom11: {title: '妇女浸润性宫颈癌',order: 38,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom12: {title: '反应迟钝或痴呆',order: 39,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom13: {title: '运动神经功能障碍',order: 40,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom14: {title: '反复感染',order: 41,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom15: {title: '儿童发育迟缓',order: 42,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  symptom16: {title: '结核',order: 43,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  bodycheckresult: {title: '体格检查情况',order: 44,view: 'textarea', type: 'string',},
  blbaseinfo: {title: '配偶或性伴基本情况',order: 45,view: 'textarea', type: 'string',},
  blexposeinfo: {title: '配偶或性伴暴露因素情况',order: 46,view: 'textarea', type: 'string',},
  childinfo: {title: '子女情况',order: 47,view: 'textarea', type: 'string',},
  otherinfo: {title: '其他补充情况',order: 48,view: 'textarea', type: 'string',},
  researchunit: {title: '调查单位',order: 49,view: 'text', type: 'string',},
  research: {title: '调查者',order: 50,view: 'text', type: 'string',},
  auditer: {title: '审核者',order: 51,view: 'text', type: 'string',},
  audittime: {title: '调查时间',order: 52,view: 'date', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
