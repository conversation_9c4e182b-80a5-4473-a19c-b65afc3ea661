import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '报告地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: '报告单位',
    align:"center",
    dataIndex: 'orgcode_dictText'
   },
   {
    title: '序列',
    align:"center",
    dataIndex: 'alignment'
   },
   {
    title: '病人治疗编码',
    align:"center",
    dataIndex: 'treatId'
   },
   {
    title: '病人姓名',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '实验室样本编号',
    align:"center",
    dataIndex: 'laboratory'
   },
   {
    title: '采血日期',
    align:"center",
    dataIndex: 'takeTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '本次病毒载量检测结果',
    align:"center",
    dataIndex: 'bcjg'
   },
   {
    title: '收样日期',
    align:"center",
    dataIndex: 'samplingTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '检测单位',
    align:"center",
    dataIndex: 'examineOrgname'
   },
   {
    title: '检测者',
    align:"center",
    dataIndex: 'examineName'
   },
   {
    title: '检测日期',
    align:"center",
    dataIndex: 'examineTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '耐药检测标本类型',
    align:"center",
    dataIndex: 'examineSampltype_dictText'
   },
   {
    title: '耐药检测方法',
    align:"center",
    dataIndex: 'examineType_dictText'
   },
   {
    title: '核苷(酸)类反转录酶抑制剂-是否有结果',
    align:"center",
    dataIndex: 'isreuslt1_dictText'
   },
   {
    title: '拉米夫定(3TC)',
    align:"center",
    dataIndex: 'tc3_dictText'
   },
   {
    title: '阿巴卡韦(ABC)',
    align:"center",
    dataIndex: 'abc_dictText'
   },
   {
    title: '齐多夫定',
    align:"center",
    dataIndex: 'azt_dictText'
   },
   {
    title: '司他夫定(D4T)',
    align:"center",
    dataIndex: 'd4t_dictText'
   },
   {
    title: '去羟肌苷(DDI)',
    align:"center",
    dataIndex: 'ddi_dictText'
   },
   {
    title: '恩曲他滨(FTC)',
    align:"center",
    dataIndex: 'ftc_dictText'
   },
   {
    title: '替诺福韦(TDF)',
    align:"center",
    dataIndex: 'tdf_dictText'
   },
   {
    title: '其他药物',
    align:"center",
    dataIndex: 'drugOthen1'
   },
   {
    title: '核苷(酸)类反转录酶抑制剂主要耐药位点',
    align:"center",
    dataIndex: 'hgs_dictText'
   },
   {
    title: '非核苷类反转录酶抑制剂-是否有结果',
    align:"center",
    dataIndex: 'isresult2_dictText'
   },
   {
    title: '依非韦伦(EFV)',
    align:"center",
    dataIndex: 'efv_dictText'
   },
   {
    title: '依曲韦林(ETR)',
    align:"center",
    dataIndex: 'etr_dictText'
   },
   {
    title: '奈韦拉平(NVP)',
    align:"center",
    dataIndex: 'nvp_dictText'
   },
   {
    title: '利匹韦林(RPV)',
    align:"center",
    dataIndex: 'rpv_dictText'
   },
   {
    title: '其他药物',
    align:"center",
    dataIndex: 'drugOthen2'
   },
   {
    title: '非核苷类反转录酶抑制剂主要耐药位点',
    align:"center",
    dataIndex: 'fhgs_dictText'
   },
   {
    title: '蛋白酶类抑制剂-是否有结果',
    align:"center",
    dataIndex: 'isresult3_dictText'
   },
   {
    title: '阿扎那韦+利托那韦(ATV/r)',
    align:"center",
    dataIndex: 'atvr_dictText'
   },
   {
    title: '达芦那韦+利托那韦(DRV/r)',
    align:"center",
    dataIndex: 'drvr_dictText'
   },
   {
    title: '福沙那韦+利托那韦(FPV/r)',
    align:"center",
    dataIndex: 'fpvr_dictText'
   },
   {
    title: '茚地那韦+利托那韦(IDV/)',
    align:"center",
    dataIndex: 'idvr_dictText'
   },
   {
    title: '洛匹那韦+利托那韦(LPV/r)',
    align:"center",
    dataIndex: 'lpvr_dictText'
   },
   {
    title: '奈非那韦(NFV)',
    align:"center",
    dataIndex: 'nfv_dictText'
   },
   {
    title: '沙奎那韦+利托那韦(SQV/r)',
    align:"center",
    dataIndex: 'sqvr_dictText'
   },
   {
    title: 'Tipranavir+利托那韦(TPV/)',
    align:"center",
    dataIndex: 'tpvr_dictText'
   },
   {
    title: '其他药物',
    align:"center",
    dataIndex: 'drugOthen3'
   },
   {
    title: '蛋白酶类抑制剂相关耐药位点',
    align:"center",
    dataIndex: 'dbm_dictText'
   },
   {
    title: '整合酶抑制剂-是否有结果',
    align:"center",
    dataIndex: 'isresult4_dictText'
   },
   {
    title: 'Bictegravir (BIC)',
    align:"center",
    dataIndex: 'bic_dictText'
   },
   {
    title: '多替拉韦(DTG)',
    align:"center",
    dataIndex: 'dtg_dictText'
   },
   {
    title: '艾维雷伟(EVG)',
    align:"center",
    dataIndex: 'evg_dictText'
   },
   {
    title: '拉替拉韦(RAL)',
    align:"center",
    dataIndex: 'ral_dictText'
   },
   {
    title: '整合酶抑制剂相关耐药位点',
    align:"center",
    dataIndex: 'zhm_dictText'
   },
   {
    title: '融合抑制剂(FI)和进入抑制剂(EI)-是否有结果',
    align:"center",
    dataIndex: 'isresult5_dictText'
   },
   {
    title: '恩夫韦地(T20)',
    align:"center",
    dataIndex: 't20_dictText'
   },
   {
    title: '艾博卫泰',
    align:"center",
    dataIndex: 'abwt_dictText'
   },
   {
    title: '融合抑制剂(FI)和进入抑制剂(EI)相关耐药位点',
    align:"center",
    dataIndex: 'fiei_dictText'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
      label: "报告地区",
      field: 'zonecode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.orgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'orgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "报告机构",
      field: 'orgcode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
	{
      label: "序列",
      field: 'alignment',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "病人治疗编码",
      field: 'treatId',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "病人姓名",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
     {
      label: "检测日期",
      field: "examineTime",
      component: 'RangePicker',
      componentProps: {
        valueType: 'Date',
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
      label: "报告地区",
      field: 'zonecode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.orgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'orgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "报告机构",
      field: 'orgcode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
    label: '序列',
    field: 'alignment',
    component: 'Input',
  },
  {
    label: '病人治疗编码',
    field: 'treatId',
    component: 'Input',
  },
  {
    label: '病人姓名',
    field: 'name',
    component: 'Input',
  },
  {
    label: '实验室样本编号',
    field: 'laboratory',
    component: 'Input',
  },
  {
    label: '采血日期',
    field: 'takeTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '本次病毒载量检测结果',
    field: 'bcjg',
    component: 'Input',
  },
  {
    label: '收样日期',
    field: 'samplingTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '检测单位',
    field: 'examineOrgname',
    component: 'Input',
  },
  {
    label: '检测者',
    field: 'examineName',
    component: 'Input',
  },
  {
    label: '检测日期',
    field: 'examineTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '耐药检测标本类型',
    field: 'examineSampltype',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_examine_sampltype",
        type: "radio"
     },
  },
  {
    label: '耐药检测方法',
    field: 'examineType',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_examine_type",
        type: "radio"
     },
  },
  {
    label: '核苷(酸)类反转录酶抑制剂-是否有结果',
    field: 'isreuslt1',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_naiyao_result",
        type: "radio"
     },
  },
  {
    label: '拉米夫定(3TC)',
    field: 'tc3',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '阿巴卡韦(ABC)',
    field: 'abc',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '齐多夫定',
    field: 'azt',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '司他夫定(D4T)',
    field: 'd4t',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '去羟肌苷(DDI)',
    field: 'ddi',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '恩曲他滨(FTC)',
    field: 'ftc',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '替诺福韦(TDF)',
    field: 'tdf',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '其他药物',
    field: 'drugOthen1',
    component: 'Input',
  },
  {
    label: '核苷(酸)类反转录酶抑制剂主要耐药位点',
    field: 'hgs',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_naiyao_hgs"
     },
  },
  {
    label: '非核苷类反转录酶抑制剂-是否有结果',
    field: 'isresult2',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_naiyao_result",
        type: "radio"
     },
  },
  {
    label: '依非韦伦(EFV)',
    field: 'efv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '依曲韦林(ETR)',
    field: 'etr',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '奈韦拉平(NVP)',
    field: 'nvp',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '利匹韦林(RPV)',
    field: 'rpv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '其他药物',
    field: 'drugOthen2',
    component: 'Input',
  },
  {
    label: '非核苷类反转录酶抑制剂主要耐药位点',
    field: 'fhgs',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_naiyao_fhgs"
     },
  },
  {
    label: '蛋白酶类抑制剂-是否有结果',
    field: 'isresult3',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_naiyao_result",
        type: "radio"
     },
  },
  {
    label: '阿扎那韦+利托那韦(ATV/r)',
    field: 'atvr',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '达芦那韦+利托那韦(DRV/r)',
    field: 'drvr',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '福沙那韦+利托那韦(FPV/r)',
    field: 'fpvr',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '茚地那韦+利托那韦(IDV/)',
    field: 'idvr',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '洛匹那韦+利托那韦(LPV/r)',
    field: 'lpvr',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '奈非那韦(NFV)',
    field: 'nfv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '沙奎那韦+利托那韦(SQV/r)',
    field: 'sqvr',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: 'Tipranavir+利托那韦(TPV/)',
    field: 'tpvr',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '其他药物',
    field: 'drugOthen3',
    component: 'Input',
  },
  {
    label: '蛋白酶类抑制剂相关耐药位点',
    field: 'dbm',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_naiyao_dbm"
     },
  },
  {
    label: '整合酶抑制剂-是否有结果',
    field: 'isresult4',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_naiyao_result",
        type: "radio"
     },
  },
  {
    label: 'Bictegravir (BIC)',
    field: 'bic',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '多替拉韦(DTG)',
    field: 'dtg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '艾维雷伟(EVG)',
    field: 'evg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '拉替拉韦(RAL)',
    field: 'ral',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '整合酶抑制剂相关耐药位点',
    field: 'zhm',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_naiyao_zhm"
     },
  },
  {
    label: '融合抑制剂(FI)和进入抑制剂(EI)-是否有结果',
    field: 'isresult5',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_naiyao_result",
        type: "radio"
     },
  },
  {
    label: '恩夫韦地(T20)',
    field: 't20',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '艾博卫泰',
    field: 'abwt',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_value_result",
        type: "radio"
     },
  },
  {
    label: '融合抑制剂(FI)和进入抑制剂(EI)相关耐药位点',
    field: 'fiei',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_naiyao_fiei"
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  zonecode: {title: '报告地区',order: 0,view: 'text', type: 'string',},
  orgcode: {title: '报告单位',order: 1,view: 'text', type: 'string',},
  alignment: {title: '序列',order: 2,view: 'text', type: 'string',},
  treatId: {title: '病人治疗编码',order: 3,view: 'text', type: 'string',},
  name: {title: '病人姓名',order: 4,view: 'text', type: 'string',},
  laboratory: {title: '实验室样本编号',order: 5,view: 'text', type: 'string',},
  takeTime: {title: '采血日期',order: 6,view: 'date', type: 'string',},
  bcjg: {title: '本次病毒载量检测结果',order: 7,view: 'text', type: 'string',},
  samplingTime: {title: '收样日期',order: 8,view: 'date', type: 'string',},
  examineOrgname: {title: '检测单位',order: 9,view: 'text', type: 'string',},
  examineName: {title: '检测者',order: 10,view: 'text', type: 'string',},
  examineTime: {title: '检测日期',order: 11,view: 'date', type: 'string',},
  examineSampltype: {title: '耐药检测标本类型',order: 12,view: 'radio', type: 'string',dictCode: 'aids_examine_sampltype',},
  examineType: {title: '耐药检测方法',order: 13,view: 'radio', type: 'string',dictCode: 'aids_examine_type',},
  isreuslt1: {title: '核苷(酸)类反转录酶抑制剂-是否有结果',order: 14,view: 'radio', type: 'string',dictCode: 'aids_naiyao_result',},
  tc3: {title: '拉米夫定(3TC)',order: 15,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  abc: {title: '阿巴卡韦(ABC)',order: 16,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  azt: {title: '齐多夫定',order: 17,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  d4t: {title: '司他夫定(D4T)',order: 18,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  ddi: {title: '去羟肌苷(DDI)',order: 19,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  ftc: {title: '恩曲他滨(FTC)',order: 20,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  tdf: {title: '替诺福韦(TDF)',order: 21,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  drugOthen1: {title: '其他药物',order: 22,view: 'text', type: 'string',},
  hgs: {title: '核苷(酸)类反转录酶抑制剂主要耐药位点',order: 23,view: 'checkbox', type: 'string',dictCode: 'aids_naiyao_hgs',},
  isresult2: {title: '非核苷类反转录酶抑制剂-是否有结果',order: 24,view: 'radio', type: 'string',dictCode: 'aids_naiyao_result',},
  efv: {title: '依非韦伦(EFV)',order: 25,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  etr: {title: '依曲韦林(ETR)',order: 26,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  nvp: {title: '奈韦拉平(NVP)',order: 27,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  rpv: {title: '利匹韦林(RPV)',order: 28,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  drugOthen2: {title: '其他药物',order: 29,view: 'text', type: 'string',},
  fhgs: {title: '非核苷类反转录酶抑制剂主要耐药位点',order: 30,view: 'checkbox', type: 'string',dictCode: 'aids_naiyao_fhgs',},
  isresult3: {title: '蛋白酶类抑制剂-是否有结果',order: 31,view: 'radio', type: 'string',dictCode: 'aids_naiyao_result',},
  atvr: {title: '阿扎那韦+利托那韦(ATV/r)',order: 32,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  drvr: {title: '达芦那韦+利托那韦(DRV/r)',order: 33,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  fpvr: {title: '福沙那韦+利托那韦(FPV/r)',order: 34,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  idvr: {title: '茚地那韦+利托那韦(IDV/)',order: 35,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  lpvr: {title: '洛匹那韦+利托那韦(LPV/r)',order: 36,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  nfv: {title: '奈非那韦(NFV)',order: 37,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  sqvr: {title: '沙奎那韦+利托那韦(SQV/r)',order: 38,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  tpvr: {title: 'Tipranavir+利托那韦(TPV/)',order: 39,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  drugOthen3: {title: '其他药物',order: 40,view: 'text', type: 'string',},
  dbm: {title: '蛋白酶类抑制剂相关耐药位点',order: 41,view: 'checkbox', type: 'string',dictCode: 'aids_naiyao_dbm',},
  isresult4: {title: '整合酶抑制剂-是否有结果',order: 42,view: 'radio', type: 'string',dictCode: 'aids_naiyao_result',},
  bic: {title: 'Bictegravir (BIC)',order: 43,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  dtg: {title: '多替拉韦(DTG)',order: 44,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  evg: {title: '艾维雷伟(EVG)',order: 45,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  ral: {title: '拉替拉韦(RAL)',order: 46,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  zhm: {title: '整合酶抑制剂相关耐药位点',order: 47,view: 'checkbox', type: 'string',dictCode: 'aids_naiyao_zhm',},
  isresult5: {title: '融合抑制剂(FI)和进入抑制剂(EI)-是否有结果',order: 48,view: 'radio', type: 'string',dictCode: 'aids_naiyao_result',},
  t20: {title: '恩夫韦地(T20)',order: 49,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  abwt: {title: '艾博卫泰',order: 50,view: 'radio', type: 'string',dictCode: 'aids_value_result',},
  fiei: {title: '融合抑制剂(FI)和进入抑制剂(EI)相关耐药位点',order: 51,view: 'checkbox', type: 'string',dictCode: 'aids_naiyao_fiei',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
