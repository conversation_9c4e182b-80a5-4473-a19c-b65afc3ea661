import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '模板名称',
    align:"center",
    dataIndex: 'templateName'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remark'
   },
   {
    title: '报表类型',
    align:"center",
    dataIndex: 'reportType'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "模板名称",
      field: 'templateName',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '模板名称',
    field: 'templateName',
    component: 'Input',
  },
  {
    label: 'FreeMark地址',
    field: 'freemarkAddr',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '添加地区',
    field: 'addZone',
    component: 'Input',
  },
  {
    label: '添加机构',
    field: 'addOrg',
    component: 'Input',
  },
  {
    label: '添加科室',
    field: 'addDep',
    component: 'Input',
  },
  {
    label: '添加人',
    field: 'addUser',
    component: 'Input',
  },
  {
    label: '添加时间',
    field: 'addTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '修改地区',
    field: 'modyZone',
    component: 'Input',
  },
  {
    label: '修改机构',
    field: 'modyOrg',
    component: 'Input',
  },
  {
    label: '修改科室',
    field: 'modyDep',
    component: 'Input',
  },
  {
    label: '修改人',
    field: 'modyUser',
    component: 'Input',
  },
  {
    label: '修改时间',
    field: 'modyTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '是否有效',
    field: 'state',
    component: 'Input',
  },
  {
    label: '数据交换-数据来源',
    field: 'dataSource',
    component: 'Input',
  },
  {
    label: '数据交换-更新时间',
    field: 'dataModyTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '对应类名',
    field: 'className',
    component: 'Input',
  },
  {
    label: '报表类型',
    field: 'reportType',
    component: 'Input',
  },
  {
    label: '支持市县级',
    field: 'allowcity',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  templateName: {title: '模板名称',order: 0,view: 'text', type: 'string',},
  remark: {title: '备注',order: 2,view: 'text', type: 'string',},
  reportType: {title: '报表类型',order: 17,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}