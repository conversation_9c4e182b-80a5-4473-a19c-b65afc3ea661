<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-row>
						<a-col :span="12">
							<a-form-item label="患者姓名" v-bind="validateInfos.patientname">
								<a-input v-model:value="formData.patientname" placeholder="请输入患者姓名"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="患者年龄" v-bind="validateInfos.patientage">
								<a-input v-model:value="formData.patientage" placeholder="请输入患者年龄"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="患者性别" v-bind="validateInfos.sex">
								<j-dict-select-tag v-model:value="formData.sex" dictCode="aids_sex" placeholder="请选择患者性别"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="病历类型" v-bind="validateInfos.medicalrecordtype">
								<j-dict-select-tag v-model:value="formData.medicalrecordtype" dictCode="RECORD_TYPE" placeholder="请选择病历类型"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="诊断结果" v-bind="validateInfos.diagnosisresult">
								<a-input v-model:value="formData.diagnosisresult" placeholder="请输入诊断结果"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="监测时间" v-bind="validateInfos.monitoringtime">
								<a-date-picker placeholder="请选择监测时间"  v-model:value="formData.monitoringtime" value-format="YYYY-MM-DD"  style="width: 100%"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="监测指标" v-bind="validateInfos.monitoringindicator">
								<a-input v-model:value="formData.monitoringindicator" placeholder="请输入监测指标"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="监测值" v-bind="validateInfos.monitoringvalue">
								<a-input v-model:value="formData.monitoringvalue" placeholder="请输入监测值"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="医生姓名" v-bind="validateInfos.doctorname">
								<a-input v-model:value="formData.doctorname" placeholder="请输入医生姓名"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="报告生成权限" v-bind="validateInfos.npermission">
								<a-input v-model:value="formData.npermission" placeholder="请输入报告生成权限"  allow-clear ></a-input>
							</a-form-item>
						</a-col>

            <!-- 附件区域，根据generation值显示或隐藏 -->
            <a-col :span="12" v-if="showAttachment">
              <a-form-item label="报告生成" v-bind="validateInfos.file">
                <j-upload v-model:value="formData.file"   ></j-upload>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../CenterMedical.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  

  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({})},
    formBpm: { type: Boolean, default: true }
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    patientname: '',   
    patientage: '',   
    sex: '',   
    medicalrecordtype: '',   
    diagnosisresult: '',   
    monitoringtime: '',   
    monitoringindicator: '',   
    monitoringvalue: '',   
    doctorname: '',   
    npermission: '',
    file: '',   
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
    // 控制附件区域显示的变量
  const showAttachment = ref<boolean>(false);



  //表单验证
  const validatorRules = reactive({
    patientname: [{ required: true, message: '请输入患者姓名!'},],
    patientage: [{ required: true, message: '请输入患者年龄!'},],
    sex: [{ required: true, message: '请输入患者性别!'},],
    medicalrecordtype: [{ required: true, message: '请输入病历类型!'},],
    diagnosisresult: [{ required: true, message: '请输入诊断结果!'},],
    monitoringtime: [{ required: true, message: '请输入监测时间!'},],
    monitoringindicator: [{ required: true, message: '请输入监测指标!'},],
    monitoringvalue: [{ required: true, message: '请输入监测值!'},],
    doctorname: [{ required: true, message: '请输入医生姓名!'},],
    npermission: [{ required: true, message: '请输入报告生成权限!'},],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(()=>{
    if(props.formBpm === true){
      if(props.formData.disabled === false){
        return false;
      }else{
        return true;
      }
    }
    return props.formDisabled;
  });

  
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if(record.hasOwnProperty(key)){
          tmpData[key] = record[key]
        }
      })
      //赋值
      Object.assign(formData, tmpData);
      // 根据generation值设置附件区域显示状态
     if(record.generation === undefined|| record.generation === "1"){
        showAttachment.value = false;
      } else if(record.generation === '2'){
        showAttachment.value = true;
      }

    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    
    // 触发表单验证
    await validate();
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //model 存的是新增的值无id，
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据 JSON 格式
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          //执行成功调用父组件的
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }


  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
