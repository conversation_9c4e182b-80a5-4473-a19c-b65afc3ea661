import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '模板名称',
    align:"center",
    dataIndex: 'templateName'
   },
   {
    title: '添加人',
    align:"center",
    dataIndex: 'addUser'
   },
   {
    title: '添加时间',
    align:"center",
    dataIndex: 'addTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '报表类型',
    align:"center",
    dataIndex: 'reportType'
   },
  {
    title: '审核状态',
    align:"center",
    dataIndex: 'auditState_dictText',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"AUDIT_STATE"
    },
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "模板名称",
      field: 'templateName',
      component: 'Input',
      //colProps: {span: 6},
 	},
  {
    label: "审核状态",
    field: 'auditState',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"AUDIT_STATE"
    },
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '模板名称',
    field: 'templateName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入模板名称!'},
          ];
     },
  },
  {
    label: '状态',
    field: 'flag',
    component: 'Input',
  },
  {
    label: '添加地区',
    field: 'addZone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加地区!'},
          ];
     },
  },
  {
    label: '添加机构',
    field: 'addOrg',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加机构!'},
          ];
     },
  },
  {
    label: '添加科室',
    field: 'addDep',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加科室!'},
          ];
     },
  },
  {
    label: '添加人',
    field: 'addUser',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加人!'},
          ];
     },
  },
  {
    label: '添加时间',
    field: 'addTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加时间!'},
          ];
     },
  },
  {
    label: '修改地区',
    field: 'modyZone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改地区!'},
          ];
     },
  },
  {
    label: '修改机构',
    field: 'modyOrg',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改机构!'},
          ];
     },
  },
  {
    label: '修改科室',
    field: 'modyDep',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改科室!'},
          ];
     },
  },
  {
    label: '修改人',
    field: 'modyUser',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改人!'},
          ];
     },
  },
  {
    label: '修改时间',
    field: 'modyTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改时间!'},
          ];
     },
  },
  {
    label: '是否有效',
    field: 'state',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入是否有效!'},
          ];
     },
  },
  {
    label: '数据交换-数据来源',
    field: 'dataSource',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入数据交换-数据来源!'},
          ];
     },
  },
  {
    label: '数据交换-更新时间',
    field: 'dataModyTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入数据交换-更新时间!'},
          ];
     },
  },
  {
    label: '备注',
    field: 'remark',
    component: 'Input',
  },
  {
    label: '报表类型',
    field: 'reportType',
    component: 'Input',
  },
  {
    label: '审核状态',
    field: 'auditState',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode:"AUDIT_STATE"
    },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  templateName: {title: '模板名称',order: 0,view: 'text', type: 'string',},
  addUser: {title: '添加人',order: 5,view: 'text', type: 'string',},
  addTime: {title: '添加时间',order: 6,view: 'date', type: 'string',},
  reportType: {title: '报表类型',order: 16,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
