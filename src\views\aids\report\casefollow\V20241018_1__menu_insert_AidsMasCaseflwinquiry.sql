-- 注意：该页面对应的前台目录为views/reportManage文件夹下
-- 如果你想更改到其他目录，请修改sql中component字段对应的值


INSERT INTO sys_permission(id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external) 
VALUES ('2024101812289470320', NULL, '报告卡随访表', '/reportManage/aidsMasCaseflwinquiryList', 'reportManage/AidsMasCaseflwinquiryList', NULL, NULL, 0, NULL, '1', 0.00, 0, NULL, 1, int_to_bool(0), 0, 0, 0, NULL, '1', 0, 0, 'admin', '2024-10-18 12:28:32', NULL, NULL, 0);

-- 权限控制sql
-- 新增
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101812289470321', '2024101812289470320', '添加报告卡随访表', NULL, NULL, 0, NULL, NULL, 2, 'reportManage:aids_mas_caseflwinquiry:add', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-18 12:28:32', NULL, NULL, 0, 0, '1', 0);
-- 编辑
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101812289470322', '2024101812289470320', '编辑报告卡随访表', NULL, NULL, 0, NULL, NULL, 2, 'reportManage:aids_mas_caseflwinquiry:edit', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-18 12:28:32', NULL, NULL, 0, 0, '1', 0);
-- 删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101812289470323', '2024101812289470320', '删除报告卡随访表', NULL, NULL, 0, NULL, NULL, 2, 'reportManage:aids_mas_caseflwinquiry:delete', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-18 12:28:32', NULL, NULL, 0, 0, '1', 0);
-- 批量删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101812289470324', '2024101812289470320', '批量删除报告卡随访表', NULL, NULL, 0, NULL, NULL, 2, 'reportManage:aids_mas_caseflwinquiry:deleteBatch', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-18 12:28:32', NULL, NULL, 0, 0, '1', 0);
-- 导出excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101812289470325', '2024101812289470320', '导出excel_报告卡随访表', NULL, NULL, 0, NULL, NULL, 2, 'reportManage:aids_mas_caseflwinquiry:exportXls', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-18 12:28:32', NULL, NULL, 0, 0, '1', 0);
-- 导入excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101812289470326', '2024101812289470320', '导入excel_报告卡随访表', NULL, NULL, 0, NULL, NULL, 2, 'reportManage:aids_mas_caseflwinquiry:importExcel', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-18 12:28:32', NULL, NULL, 0, 0, '1', 0);