import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '患者姓名',
    align: "center",
    dataIndex: 'patientname'
  },
  {
    title: '性别',
    align: "center",
    dataIndex: 'gender_dictText'
  },
  {
    title: '年龄',
    align: "center",
    dataIndex: 'age'
  },
  {
    title: '居住地',
    align: "center",
    dataIndex: 'residence_dictText'
  },
  {
    title: '症状描述',
    align: "center",
    dataIndex: 'symptoms'
  },
  {
    title: '症状出现日期',
    align: "center",
    dataIndex: 'symptomonsetdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '暴露史',
    align: "center",
    dataIndex: 'exposurehistory'
  },
  {
    title: '初步诊断',
    align: "center",
    dataIndex: 'preliminarydiagnosis'
  },
  {
    title: '检测方法',
    align: "center",
    dataIndex: 'testmethod'
  },
  {
    title: '检测结果',
    align: "center",
    dataIndex: 'testresult'
  },
  {
    title: '机构代码',
    align: "center",
    dataIndex: 'reportingorgid'
  },
  {
    title: '生成人姓名',
    align: "center",
    dataIndex: 'reportername'
  },
  {
    title: '报告状态',
    align: "center",
    dataIndex: 'state_dictText'
  },
  {
    title: '报告生成日期',
    align: "center",
    dataIndex: 'reportdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
    {
    title: '状态',
    align: "center",
    dataIndex: 'states_dictText'
  },
    {
    title: '附件',
    align: "center",
    dataIndex: 'file',
  },
    {
    title: '批量报告生成',
    align: "center",
    dataIndex: 'generation_dictText'
  },
];

// 高级查询数据
export const superQuerySchema = {
  patientname: {title: '患者姓名',order: 0,view: 'text', type: 'string',},
  gender: {title: '性别',order: 1,view: 'list', type: 'string',dictCode: 'aids_sex',},
  age: {title: '年龄',order: 2,view: 'text', type: 'string',},
  residence: {title: '居住地',order: 3,view: 'pca', type: 'string',},
  symptoms: {title: '症状描述',order: 4,view: 'text', type: 'string',},
  symptomonsetdate: {title: '症状出现日期',order: 5,view: 'date', type: 'string',},
  exposurehistory: {title: '暴露史',order: 6,view: 'text', type: 'string',},
  preliminarydiagnosis: {title: '初步诊断',order: 7,view: 'text', type: 'string',},
  testmethod: {title: '检测方法',order: 8,view: 'text', type: 'string',},
  testresult: {title: '检测结果',order: 9,view: 'text', type: 'string',},
  reportingorgid: {title: '机构代码',order: 10,view: 'text', type: 'string',},
  reportername: {title: '生成人姓名',order: 11,view: 'text', type: 'string',},
  state: {title: '报告状态',order: 12,view: 'list', type: 'string',dictCode: 'BG_STATE',},
  reportdate: {title: '报告生成日期',order: 13,view: 'date', type: 'string',},
  states: {title: '状态',order: 14,view: 'list', type: 'string',dictCode: 'STATES',},
  file: {title: '附件',order: 11,view: 'file', type: 'string',},
  generation: {title: '批量报告生成',order: 12,view: 'list', type: 'string',dictCode: 'RATION',},
};
