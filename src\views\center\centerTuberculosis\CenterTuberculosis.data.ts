import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '患者姓名',
    align: "center",
    dataIndex: 'patientname'
  },
  {
    title: '性别',
    align: "center",
    dataIndex: 'gender_dictText'
  },
  {
    title: '出生日期',
    align: "center",
    dataIndex: 'birthdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '联系电话',
    align: "center",
    dataIndex: 'contactnumber'
  },
  {
    title: '诊断日期',
    align: "center",
    dataIndex: 'diagnosisdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '结核类型',
    align: "center",
    dataIndex: 'tuberculosistype_dictText'
  },
  {
    title: '检测方法',
    align: "center",
    dataIndex: 'testmethod'
  },
  {
    title: '检测结果',
    align: "center",
    dataIndex: 'testresult'
  },
  {
    title: '检测日期',
    align: "center",
    dataIndex: 'testdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '药物敏感性结果',
    align: "center",
    dataIndex: 'drugsensitivity'
  },
  {
    title: '治疗阶段',
    align: "center",
    dataIndex: 'treatmentphase'
  },
  {
    title: '机构代码',
    align: "center",
    dataIndex: 'reportingorgid'
  },
  {
    title: '生成人姓名',
    align: "center",
    dataIndex: 'reportername'
  },
  {
    title: '报告状态',
    align: "center",
    dataIndex: 'state_dictText'
  },
  {
    title: '报告生成日期',
    align: "center",
    dataIndex: 'reportdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
      {
    title: '状态',
    align: "center",
    dataIndex: 'states_dictText'
  },
    {
    title: '附件',
    align: "center",
    dataIndex: 'file',
  },
    {
    title: '批量报告生成',
    align: "center",
    dataIndex: 'generation_dictText'
  },
];

// 高级查询数据
export const superQuerySchema = {
  patientname: {title: '患者姓名',order: 0,view: 'text', type: 'string',},
  gender: {title: '性别',order: 1,view: 'list', type: 'string',dictCode: 'aids_sex',},
  birthdate: {title: '出生日期',order: 2,view: 'date', type: 'string',},
  contactnumber: {title: '联系电话',order: 3,view: 'text', type: 'string',},
  diagnosisdate: {title: '诊断日期',order: 4,view: 'date', type: 'string',},
  tuberculosistype: {title: '结核类型',order: 5,view: 'list', type: 'string',dictCode: 'JH_TYPE',},
  testmethod: {title: '检测方法',order: 6,view: 'text', type: 'string',},
  testresult: {title: '检测结果',order: 7,view: 'text', type: 'string',},
  testdate: {title: '检测日期',order: 8,view: 'date', type: 'string',},
  drugsensitivity: {title: '药物敏感性结果',order: 9,view: 'text', type: 'string',},
  treatmentphase: {title: '治疗阶段',order: 10,view: 'text', type: 'string',},
  reportingorgid: {title: '机构代码',order: 11,view: 'text', type: 'string',},
  reportername: {title: '生成人姓名',order: 12,view: 'text', type: 'string',},
  state: {title: '报告状态',order: 13,view: 'list', type: 'string',dictCode: 'BG_STATE',},
  reportdate: {title: '报告生成日期',order: 14,view: 'date', type: 'string',},
  states: {title: '状态',order: 15,view: 'list', type: 'string',dictCode: 'STATES',},
  file: {title: '附件',order: 15,view: 'file', type: 'string',},
  generation: {title: '批量报告生成',order: 16,view: 'list', type: 'string',dictCode: 'RATION',},
};
