// 单点登录核心类
import { getToken } from '/@/utils/auth';
import { getUrlParam } from '/@/utils';
import { useGlobSetting } from '/@/hooks/setting';
import { validateCasLogin } from '/@/api/sys/user';
import { useUserStore } from '/@/store/modules/user';
import { oauthLogin } from '/@/api/common/api';
const globSetting = useGlobSetting();
const openSso = globSetting.openSso;
const authorizeCode = globSetting.authorizeCode;
const casBaseUrl = globSetting.casBaseUrl;
export function useSso() {
  //update-begin---author:wangshuai---date:2024-01-03---for:【QQYUN-7805】SSO登录强制用http #957---
  let locationUrl = document.location.protocol +"//" + window.location.host + '/';
  //update-end---author:wangshuai---date:2024-01-03---for:【QQYUN-7805】SSO登录强制用http #957---

  /**
   * cas单点登录
   */
  /* async function ssoLogin() {
    if (openSso == 'true') {
      let token = getToken();
      let ticket = getUrlParam('ticket');
      if (!token) {
        if (ticket) {
          await validateCasLogin({
            ticket: ticket,
            service: locationUrl,
          }).then((res) => {
            const userStore = useUserStore();
            userStore.setToken(res.token);
            return userStore.afterLoginAction(true, {});
          });
        } else {
          window.location.href = globSetting.casBaseUrl + '/login?service=' + encodeURIComponent(locationUrl);
        }
      }
    }
  } */


  /**
   * Oauth2单点登录
   */
  async function ssoLogin() {
    if (openSso == 'true') {
      let urlSearchParams = new URLSearchParams(window.location.search)
      let code = urlSearchParams.get('code');
      //如果没有拿到授权码，就打开统一登录页面
      if (!code) {
        //没登录，就去统一登录页面获取一次性使用的授权码
        window.location.href = authorizeCode
      }else{
        const userStore = useUserStore();
        let token = getToken();
        if(!token){
          //用授权码，向自己后台进行请求登录
          oauthLogin({code:token||code}).then((res) => {
            userStore.setToken(res.token);
            return userStore.afterLoginAction(true, {});
          }).catch(() => {
            console.log("异常发生")
            //授权码可能过期,验证异常，再去统一登录页面获取一次性使用的授权码
            window.location.href=casBaseUrl
          });
        }

      }

    }
  }

  /**
   * 退出登录
   */
  async function ssoLoginOut() {
    window.location.href = globSetting.casBaseUrl + '/logout?service=' + encodeURIComponent(locationUrl);
  }
  return { ssoLogin, ssoLoginOut };
}
