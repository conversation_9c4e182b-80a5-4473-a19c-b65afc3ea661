import { BasicColumn, FormSchema } from '/src/components/Table';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '卡片编号',
    align: 'center',
    dataIndex: 'cardCode',
  },
  {
    title: '报告地区',
    align: 'center',
    dataIndex: 'apanagecode_dictText',
  },
  {
    title: '报告单位',
    align: 'center',
    dataIndex: 'rptorgcode_dictText',
  },
  {
    title: '姓名',
    align: 'center',
    dataIndex: 'patientName',
  },
  {
    title: '性别',
    align: 'center',
    dataIndex: 'sex1_dictText',
  },
  {
    title: '详细地址',
    align: 'center',
    dataIndex: 'addr',
  },
  {
    title: '疾病名称',
    align: 'center',
    dataIndex: 'diseaseId1_dictText',
  },
  {
    title: '病例分类',
    align: 'center',
    dataIndex: 'casetype_dictText',
  },
  {
    title: '填卡医生',
    align: 'center',
    dataIndex: 'inputdoctor',
  },
  {
    title: '填卡日期',
    align: 'center',
    dataIndex: 'filltime',
    customRender: ({ text }) => {
      text = !text ? '' : text.length > 10 ? text.substr(0, 10) : text;
      return text;
    },
  },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '报告地区',
    field: 'apanagecode',
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          //所属部门修改后更新负责部门下拉框数据
          formModel.rptorgcode=''
          updateSchema([
            {
              field: 'rptorgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'rptorgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: '患者姓名',
    field: 'patientName',
    component: 'Input',
    //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '疾病诊断',
    field: 'diseaseId1',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'aids_udp_disaster_type',
    },
  },
  {
    label: '报告地区',
    field: 'apanagecode',
    required: true,
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.rptorgcode=''
          //所属部门修改后更新负责部门下拉框数据
          
          updateSchema([
            {
              field: 'rptorgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'rptorgcode',
    required: true,
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: '卡片ID',
    field: 'cardId',
    component: 'Input',
  },
  {
    label: '报告卡编号',
    field: 'cardCode',
    component: 'Input',
  },
  {
    label: '患者姓名',
    field: 'patientName',
    component: 'Input',
  },
  {
    label: '患儿家长姓名',
    field: 'parentName',
    component: 'Input',
  },
  {
    label: '身份证号',
    field: 'idCard',
    component: 'Input',
  },
  {
    label: '性别',
    field: 'sex1',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'aids_sex',
      type: 'radio',
    },
  },
  {
    label: '出生日期',
    field: 'birthdayDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '患者工作单位',
    field: 'unit',
    component: 'Input',
  },
  {
    label: '联系电话',
    field: 'telp',
    component: 'Input',
  },
  {
    label: '病人属于',
    field: 'areatype',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'aids_dd_areatype',
    },
  },
  {
    label: '现住国标地址',
    field: 'addrcode',
    component:'Zonecode',
    componentProps: ({ formActionType, formModel }) => {
          return {
            allowClear:false,
            onOptionsChange: (values) => {
              const { updateSchema } = formActionType;
                 //所属部门修改后更新负责部门下拉框数据
                 
            },
          };
        },
  },
  {
    label: '现住详细地址',
    field: 'addr',
    component: 'Input',
  },

  {
    label: '患者职业',
    field: 'groupId',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'aids_nultitude',
    },
  },
  {
    label: '发病日期',
    field: 'startDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '诊断日期',
    field: 'diagnosedate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '死亡日期',
    field: 'deaddate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '病例分类',
    field: 'casetype',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'aids_casetype',
    },
  },
  {
    label: '填卡医生',
    field: 'inputdoctor',
    component: 'Input',
  },
  {
    label: '医生填卡日期',
    field: 'filltime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '密切接触者有无相同症状',
    field: 'closecontactssymptomcode',
    component: 'JDictSelectTag',
    componentProps: {
      dictCode: 'aids_yesNo',
      type: 'radio',
    },
  },
  {
    label: '备注',
    field: 'notes',
    component: 'InputTextArea',
  },
  /*{
    label: '具体的其他职业信息',
    field: 'occupation',
    component: 'Input',
  },
  {
    label: '具体的其他疾病信息',
    field: 'otherDisease',
    component: 'Input',
  },
  {
    label: 'foreigncode',
    field: 'foreigncode',
    component: 'Input',
  },
  {
    label: 'baseId',
    field: 'baseId',
    component: 'Input',
  },*/
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false,
  },
];
//子表单数据
export const aidsRptMasculineFormSchema: FormSchema[] = [
  {
    label: '婚姻状况',
    field: 'marriage',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_marriage",
        type: "radio"
     },
  },
  {
    label: '民族',
    field: 'nation',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_nation"
     },
  },
  {
    label: '文化程度',
    field: 'educationback',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_culture"
     },
  },
  {
    label: '户籍所在地属于',
    field: 'areatype',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_dd_areatype"
     },
  },
  {
    label: '户籍所在地',
    field: 'addrcode',
    component:'Zonecode',
    componentProps: ({ formActionType, formModel }) => {
          return {
            allowClear:false,
            onOptionsChange: (values) => {
              const { updateSchema } = formActionType;
                 //所属部门修改后更新负责部门下拉框数据
                 
            },
          };
        },
  },
  {
    label: '户籍详细地址',
    field: 'addr',
    component: 'Input',
  },
  {
    label: '接触史',
    field: 'intouchhis',
    component: 'JSelectMultiple',
    componentProps:{
        dictCode:"aids_mas_dd_touchhis"
     },
  },
  {
    label: '注射毒品史',
    field: 'injectcount',
    component: 'Input',
  },
  {
    label: '非婚异性性接触史',
    field: 'nonwebcount',
    component: 'Input',
  },
  {
    label: '男男性行为史',
    field: 'smcount',
    component: 'Input',
  },
  {
    label: '其他接触史',
    field: 'touothers',
    component: 'Input',
  },  
  {
    label: '性病史',
    field: 'venerealhistory',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_venerealhistory",
        type: "radio"
     },
  },
  {
    label: '最有可能感染途径',
    field: 'infectroute',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_infectionRouteCode",
        type: "radio"
     },
  },
  {
    label: '其他最有可能感染途径',
    field: 'infecothers',
    component: 'Input',
  },
  {
    label: '样本来源',
    field: 'sampleorigin',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_sampsrc",
        type: "radio"
     },
  },
  {
    label: '其他样本来源',
    field: 'srcothers',
    component: 'Input',
  },
  {
    label: '实验室检测结论',
    field: 'labconclusion',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_labconclusion",
        type: "radio"
     },
  },
  {
    label: '确认（替代策略、核酸）检测阳性日期',
    field: 'affirmmasdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '确认（替代策略）检测单位',
    field: 'affirmmasorg',
    component: 'Input',
  },
  /* {
    label: '创建日期',
    field: 'dtCreate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '更新日期',
    field: 'dtUpdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '创建者',
    field: 'useridCreate',
    component: 'Input',
  },
  {
    label: '修改者',
    field: 'useridUpdate',
    component: 'Input',
  },
  {
    label: '采集方式',
    field: 'ddCollectType',
    component: 'Input',
  },
  {
    label: '审核级别',
    field: 'flag',
    component: 'Input',
  },
  {
    label: '主键',
    field: 'id',
    component: 'Input',
  },
  {
    label: '是否已分拣',
    field: 'ddIsSorting',
    component: 'Input',
  },
  {
    label: '系统编号',
    field: 'systemNumber',
    component: 'Input',
  },
  {
    label: 'fieldPkFk',
    field: 'fieldPkFk',
    component: 'Input',
  },
  {
    label: '病人基本信息主键',
    field: 'idPatient',
    component: 'Input',
  },
  {
    label: 'od1',
    field: 'od1',
    component: 'InputNumber',
  },
  {
    label: '转归状态',
    field: 'idLapsetoState',
    component: 'Input',
  },
  {
    label: '配偶/性伴的HIV感染状况',
    field: 'matehiv',
    component: 'Input',
  },
  {
    label: '目前是否接受抗病毒治疗',
    field: 'acceptcure',
    component: 'Input',
  },
  {
    label: '人群类别',
    field: 'throngsort',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'memo',
    component: 'Input',
  },
  {
    label: '死亡时病程阶段',
    field: 'coursedias',
    component: 'Input',
  },
  {
    label: '创建时间',
    field: 'tmCreate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '创建人ID',
    field: 'empidCreate',
    component: 'Input',
  },
  {
    label: '修改时间',
    field: 'tmUpdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '状态',
    field: 'status',
    component: 'Input',
  },
  {
    label: '修改人ID',
    field: 'empidUpdate',
    component: 'Input',
  },
  {
    label: '就诊时临床表现',
    field: 'diagclinshow',
    component: 'Input',
  },
  {
    label: '主要死因其它',
    field: 'others',
    component: 'Input',
  },
  {
    label: '其他样本来源',
    field: 'srcothers',
    component: 'Input',
  },
  {
    label: '其他接触史',
    field: 'touothers',
    component: 'Input',
  },
  {
    label: '医生联系电话',
    field: 'telp',
    component: 'Input',
  },
  {
    label: '是否接受CD4检测',
    field: 'cd4check',
    component: 'Input',
  },
  {
    label: 'CD4检测结果',
    field: 'cd4result',
    component: 'Input',
  },
  {
    label: '其他最有可能感染途径',
    field: 'infecothers',
    component: 'Input',
  },
  {
    label: '就诊时临床表现其它',
    field: 'diagothers',
    component: 'Input',
  },
  {
    label: '主要死因',
    field: 'maindeathreason',
    component: 'Input',
  },
  {
    label: 'CD检测日期',
    field: 'dtCdcheck',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '艾滋病确诊日期',
    field: 'dtDiagnose',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '确认阳性日期',
    field: 'hivaffirmdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '确认单位',
    field: 'hivaffirmorg',
    component: 'Input',
  },
  {
    label: '筛查阳性日期',
    field: 'firfilterdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '筛查方法',
    field: 'filtermethod',
    component: 'Input',
  },
  {
    label: 'OD值',
    field: 'od',
    component: 'InputNumber',
  },
  {
    label: 'CUTOFF值',
    field: 'cutoff',
    component: 'InputNumber',
  },
  {
    label: 'S/CO值',
    field: 'sdivco',
    component: 'InputNumber',
  },
  {
    label: '筛查结果',
    field: 'firfilresut',
    component: 'Input',
  },
  {
    label: 'HIV确认方',
    field: 'hivaffirmmethod',
    component: 'Input',
  },
  {
    label: '确认结果',
    field: 'affirmresult',
    component: 'Input',
  },
  {
    label: '筛查阳性日期1',
    field: 'firfilterdate2',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '筛查方法1',
    field: 'filtermethod2',
    component: 'Input',
  },
  {
    label: 'OD值2',
    field: 'od2',
    component: 'InputNumber',
  },
  {
    label: 'CUTOFF值2',
    field: 'cutoff2',
    component: 'InputNumber',
  },
  {
    label: 'SDIVCO值2',
    field: 'sdivco2',
    component: 'InputNumber',
  },
  {
    label: '筛查结果2',
    field: 'firfilresut2',
    component: 'Input',
  },
  {
    label: '复测单位',
    field: 'reptdetorg',
    component: 'Input',
  },
  {
    label: '复测OD',
    field: 'dupod',
    component: 'InputNumber',
  },
  {
    label: '复测CUTOFF',
    field: 'dupcutoff',
    component: 'InputNumber',
  },
  {
    label: '复测SDIVCO',
    field: 'dupsdivco',
    component: 'InputNumber',
  },
  {
    label: '梅毒疾病类型',
    field: 'luestype',
    component: 'Input',
  },
  {
    label: '生殖道沙眼衣原体感染',
    field: 'sinfect',
    component: 'Input',
  },
  {
    label: '注射毒品史',
    field: 'injectcount',
    component: 'Input',
  },
  {
    label: '男男性行为史',
    field: 'smcount',
    component: 'Input',
  },
  {
    label: '随访执行单位',
    field: 'execuvisitorg',
    component: 'Input',
  },
  {
    label: '随访执行单位联系电话',
    field: 'execuvisitorgtel',
    component: 'Input',
  },
  {
    label: '随访责任人',
    field: 'visitprincipal',
    component: 'Input',
  },
  {
    label: '最后一次随访状态',
    field: 'flwstate',
    component: 'Input',
  },
  {
    label: '最后一次随访时间',
    field: 'lastflwdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: ' 最近一次CD4+检测结果',
    field: 'cd4checkresult',
    component: 'InputNumber',
  },
  {
    label: '最近一次CD4+检测日期',
    field: 'cd4checkdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '最后一次随访ID',
    field: 'lastflwid',
    component: 'Input',
  },
  {
    label: '最近一次CD4 ID',
    field: 'cd4checkid',
    component: 'Input',
  },
  {
    label: '第一次随访状态',
    field: 'firflwstate',
    component: 'Input',
  },
  {
    label: ' 第一次随访时间',
    field: 'firflwdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '第一次随访ID',
    field: 'firflwid',
    component: 'Input',
  },
  {
    label: '美沙酮治疗编号',
    field: 'mstcurecode',
    component: 'Input',
  },
  {
    label: '抗病毒治疗编号',
    field: 'gracurecode',
    component: 'Input',
  },
  {
    label: '记录由HIV转化成AIDS的时间',
    field: 'dtTranstoaids',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '若当前配偶/固定性伴感染状况为阳性,其卡片编号为',
    field: 'matehivmas',
    component: 'Input',
  },
  {
    label: '第一次终审日期',
    field: 'validDate2F',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '本次被诊断为HIV阳性以前是否还做过HIV检测',
    field: 'hivchecked',
    component: 'Input',
  },
  {
    label: '最后一次HIV检测为阴性的时间',
    field: 'tmHivchecktime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '第一次HIV检测为阳性之前的24个月中做过HIV检测的次数',
    field: 'hivcheckedNum',
    component: 'InputNumber',
  },
  {
    label: '第一次HIV检测为阳性的时间',
    field: 'tmHivcheck',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD',
    },
  },
  {
    label: '死因信息收集来源',
    field: 'deathinfo',
    component: 'Input',
  },
  {
    label: '死亡地点',
    field: 'deathaddr',
    component: 'Input',
  },
  {
    label: '其他死因信息收集来源',
    field: 'otherdeathinfo',
    component: 'Input',
  },
  {
    label: '其他死亡地点',
    field: 'otherdeathaddr',
    component: 'Input',
  },
  {
    label: '死因分类列表',
    field: 'deathlist',
    component: 'Input',
  },
  {
    label: '详细死因',
    field: 'listinfo',
    component: 'Input',
  },
  {
    label: '卡片ID',
    field: 'cardId',
    component: 'InputNumber',
  },
  {
    label: '操作标识 0：专病系统地市/省级操作 1：大疫情系统操作   2：专病系统国家平台操作',
    field: 'opflag',
    component: 'InputNumber',
  },
  {
    label: '数据是否已经加密  0或空：未加密，1：已加密',
    field: 'isencrypt',
    component: 'InputNumber',
  },
  {
    label: '密切接触者',
    field: 'contactflag',
    component: 'Input',
  },*/
  // TODO 主键隐藏字段，目前写死为ID
  {
    label: '',
    field: 'id',
    component: 'Input',
    show: false
  },
];
//子表表格配置

// 高级查询数据
export const superQuerySchema = {
  cardId: { title: '卡片ID', order: 0, view: 'text', type: 'string' },
  apanagecode: { title: '报告地区编码', order: 1, view: 'text', type: 'string' },
  patientName: { title: '患者姓名', order: 2, view: 'text', type: 'string' },
  parentName: { title: '患儿家长姓名', order: 3, view: 'text', type: 'string' },
  sex1: { title: '性别', order: 4, view: 'radio', type: 'string', dictCode: 'sex' },
  telp: { title: '联系电话', order: 5, view: 'text', type: 'string' },
  idCard: { title: '身份证号', order: 6, view: 'text', type: 'string' },
  unit: { title: '患者工作单位', order: 7, view: 'text', type: 'string' },
  addr: { title: '现住详细地址', order: 8, view: 'text', type: 'string' },
  addrcode: { title: '现住地址国标', order: 9, view: 'text', type: 'string' },
  areatype: { title: '现住址类型选择', order: 10, view: 'list', type: 'string', dictCode: 'aids_dd_areatype' },
  groupId: { title: '职业,码表personrole', order: 11, view: 'list', type: 'string', dictCode: 'aids_nultitude' },
  startDate: { title: '发病日期', order: 12, view: 'date', type: 'string' },
  diagnosedate: { title: '诊断日期', order: 13, view: 'date', type: 'string' },
  deaddate: { title: '死亡日期', order: 14, view: 'date', type: 'string' },
  casetype: {
    title: '病例分类',
    order: 15,
    view: 'list',
    type: 'string',
    dictCode: 'aids_casetype',
  },
  inputdoctor: { title: '填卡医生', order: 16, view: 'text', type: 'string' },
  diseaseId1: { title: '疾病名称', order: 17, view: 'list', type: 'string', dictCode: 'aids_udp_disaster_type' },
  validDate2: { title: '订正终审时间', order: 18, view: 'date', type: 'string' },
  flag: { title: '审核状态', order: 22, view: 'text', type: 'string' },
  intime: { title: '录入时间', order: 23, view: 'date', type: 'string' },
  rptorgcode: { title: '报告单位', order: 24, view: 'text', type: 'string' },
  occupation: { title: '具体的其他职业信息', order: 25, view: 'text', type: 'string' },
  cardCode: { title: '卡片编号', order: 28, view: 'text', type: 'string' },
  dtCreate: { title: '创建日期', order: 29, view: 'date', type: 'string' },
  //子表高级查询
  aidsRptMasculine: {
    title: 'aids_rpt_masculine',
    view: 'table',
    fields: {
      marriage: {title: '婚姻状况',order: 1,view: 'radio', type: 'string',dictCode: 'aids_mas_dd_marriage',},
              nation: {title: '民族',order: 2,view: 'list', type: 'string',dictCode: 'aids_nation',},
              educationback: {title: '文化程度',order: 3,view: 'list', type: 'string',dictCode: 'aids_mas_dd_culture',},
              sampleorigin: {title: '样本来源',order: 4,view: 'radio', type: 'string',dictCode: 'aids_mas_dd_sampsrc',},
              infectroute: {title: '最有可能感染途径',order: 5,view: 'radio', type: 'string',dictCode: 'aids_infectionRouteCode',},
              areatype: {title: '户籍所在地属于',order: 6,view: 'list', type: 'string',dictCode: 'aids_dd_areatype',},
              addrcode: {title: '户籍所在地',order: 7,view: 'text', type: 'string',},
              intouchhis: {title: '接触史',order: 9,view: 'list_multi', type: 'string',dictCode: 'aids_mas_dd_touchhis',},
              labconclusion: {title: '实验室检测结论',order: 15,view: 'radio', type: 'string',dictCode: 'aids_mas_dd_labconclusion',},
              affirmmasdate: {title: '确认（替代策略、核酸）检测阳性日期',order: 16,view: 'date', type: 'string',},
              affirmmasorg: {title: '确认（替代策略）检测单位',order: 17,view: 'text', type: 'string',},
              venerealhistory: {title: '性病史',order: 18,view: 'radio', type: 'string',dictCode: 'aids_mas_dd_venerealhistory',},
              contactflag: {title: '密切接触者',order: 19,view: 'text', type: 'string',},
    },
  },
};

/**
 * 流程表单调用这个方法获取formSchema
 * @param param
 */
export function getBpmFormSchema(_formData): FormSchema[] {
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
