import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '外键',
    align:"center",
    dataIndex: 'pid'
   },
   {
    title: '入监人员姓名',
    align:"center",
    dataIndex: 'jailPersonName'
   },
   {
    title: '病毒种类',
    align:"center",
    dataIndex: 'virusSpecies'
   },
   {
    title: '入监日期',
    align:"center",
    dataIndex: 'beginTime'
   },
   {
    title: '出监日期',
    align:"center",
    dataIndex: 'endTime'
   },
   {
    title: '监管场所',
    align:"center",
    dataIndex: 'regulatedPremises_dictText'
   },
   {
    title: '诊断状态',
    align:"center",
    dataIndex: 'caseTypeCode_dictText'
   },
   {
    title: '临床严重程度',
    align:"center",
    dataIndex: 'ncvSeverityCode_dictText'
   },
];

//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '外键',
    field: 'pid',
    component: 'Input',
  },
  {
    label: '入监人员姓名',
    field: 'jailPersonName',
    component: 'Input',
  },
  {
    label: '病毒种类',
    field: 'virusSpecies',
    defaultValue: "virus species",
    component: 'Input',
  },
  {
    label: '入监日期',
    field: 'beginTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '出监日期',
    field: 'endTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '监管场所',
    field: 'regulatedPremises',
    defaultValue: "regulated_premises",
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  },
  {
    label: '诊断状态',
    field: 'caseTypeCode',
    defaultValue: "case_type_code",
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  },
  {
    label: '临床严重程度',
    field: 'ncvSeverityCode',
    defaultValue: "ncv_severity_code",
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  pid: {title: '外键',order: 0,view: 'text', type: 'string',},
  jailPersonName: {title: '入监人员姓名',order: 1,view: 'text', type: 'string',},
  virusSpecies: {title: '病毒种类',order: 2,view: 'text', type: 'string',},
  beginTime: {title: '入监日期',order: 3,view: 'datetime', type: 'string',},
  endTime: {title: '出监日期',order: 4,view: 'datetime', type: 'string',},
  regulatedPremises: {title: '监管场所',order: 5,view: 'list', type: 'string',},
  caseTypeCode: {title: '诊断状态',order: 6,view: 'list', type: 'string',},
  ncvSeverityCode: {title: '临床严重程度',order: 7,view: 'list', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
