import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
   title: '检测日期',
   align:"center",
   dataIndex: 'diagnoseTime'
  },
   {
    title: '丙肝抗体检测总数',
    align:"center",
    dataIndex: 'antibobyNum'
   },
   {
    title: '丙肝抗体阳性数量',
    align:"center",
    dataIndex: 'antibodyPositiveNum'
   },
   {
    title: '丙肝抗体阳性率',
    align:"center",
    dataIndex: 'antibodyPositiveRate'
   },
   {
    title: '丙肝核酸检测总数',
    align:"center",
    dataIndex: 'nucleicAcidNum'
   },
   {
    title: '丙肝核酸检测阳性数量',
    align:"center",
    dataIndex: 'nucleicAcidPositiveNum'
   },
   {
    title: '丙肝抗体阳性核酸检测率',
    align:"center",
    dataIndex: 'nucleicAcidPositiveRate'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
      label: "检测日期开始",
      field: 'testTimeBegin',
      component: 'DatePicker',
      colProps: {span: 7},
  } ,
  {
      label: "检测日期结束",
      field: 'testTimeEnd',
      component: 'DatePicker',
      colProps: {span: 7},
  } 
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '丙肝抗体检测总数',
    field: 'antibobyNum',
    component: 'Input',
  },
  {
    label: '丙肝抗体阳性数量',
    field: 'antibodyPositiveNum',
    component: 'Input',
  },
  {
    label: '丙肝抗体阳性率',
    field: 'antibodyPositiveRate',
    component: 'Input',
  },
  {
    label: '丙肝核酸检测总数',
    field: 'nucleicAcidNum',
    component: 'Input',
  },
  {
    label: '丙肝核酸检测阳性数量',
    field: 'nucleicAcidPositiveNum',
    component: 'Input',
  },
  {
    label: '丙肝抗体阳性核酸检测率',
    field: 'nucleicAcidPositiveRate',
    component: 'Input',
  },
];

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
