import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '监测地点',
    align:"center",
    dataIndex: 'monitoringLocation'
   },
   {
    title: '监测日期',
    align:"center",
    dataIndex: 'monitoringDate'
   },
   {
    title: '监测机构',
    align:"center",
    dataIndex: 'monitoringInstitution'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'gender'
   },
   {
    title: '年龄',
    align:"center",
    dataIndex: 'age'
   },
   {
    title: '民族',
    align:"center",
    dataIndex: 'ethnicity'
   },
   {
    title: '职业',
    align:"center",
    dataIndex: 'occupation'
   },
   {
    title: '教育程度',
    align:"center",
    dataIndex: 'educationLevel'
   },
   {
    title: '性行为情况',
    align:"center",
    dataIndex: 'sexualBehavior'
   },
   {
    title: '注射吸毒情况',
    align:"center",
    dataIndex: 'injectionDrugUse'
   },
   {
    title: '性传播途径',
    align:"center",
    dataIndex: 'sexualTransmissionRoute'
   },
   {
    title: '其他风险因素',
    align:"center",
    dataIndex: 'otherRiskFactors'
   },
   {
    title: 'HIV检测结果',
    align:"center",
    dataIndex: 'hivTestResult'
   },
   {
    title: 'CD4细胞计数',
    align:"center",
    dataIndex: 'cd4CellCount'
   },
   {
    title: '是否接受抗病毒治疗',
    align:"center",
    dataIndex: 'receivingAntiretroviralTherapy_dictText'
   },
   {
    title: '合并症',
    align:"center",
    dataIndex: 'comorbidities'
   },
   {
    title: '随访情况',
    align:"center",
    dataIndex: 'followStatus'
   },
   {
    title: '干预措施',
    align:"center",
    dataIndex: 'interventionMeasures'
   },
   {
    title: '结果反馈',
    align:"center",
    dataIndex: 'feedbackOnResults'
   },
   {
    title: '外键',
    align:"center",
    dataIndex: 'pid'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '监测地点',
    field: 'monitoringLocation',
    component: 'Input',
  },
  {
    label: '监测日期',
    field: 'monitoringDate',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '监测机构',
    field: 'monitoringInstitution',
    component: 'Input',
  },
  {
    label: '性别',
    field: 'gender',
    component: 'Input',
  },
  {
    label: '年龄',
    field: 'age',
    component: 'Input',
  },
  {
    label: '民族',
    field: 'ethnicity',
    component: 'Input',
  },
  {
    label: '职业',
    field: 'occupation',
    component: 'Input',
  },
  {
    label: '教育程度',
    field: 'educationLevel',
    component: 'Input',
  },
  {
    label: '性行为情况',
    field: 'sexualBehavior',
    component: 'Input',
  },
  {
    label: '注射吸毒情况',
    field: 'injectionDrugUse',
    component: 'Input',
  },
  {
    label: '性传播途径',
    field: 'sexualTransmissionRoute',
    component: 'Input',
  },
  {
    label: '其他风险因素',
    field: 'otherRiskFactors',
    component: 'Input',
  },
  {
    label: 'HIV检测结果',
    field: 'hivTestResult',
    component: 'Input',
  },
  {
    label: 'CD4细胞计数',
    field: 'cd4CellCount',
    component: 'Input',
  },
  {
    label: '是否接受抗病毒治疗',
    field: 'receivingAntiretroviralTherapy',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"receiving_antiretroviral_therapy"
     },
  },
  {
    label: '合并症',
    field: 'comorbidities',
    component: 'Input',
  },
  {
    label: '随访情况',
    field: 'followStatus',
    component: 'Input',
  },
  {
    label: '干预措施',
    field: 'interventionMeasures',
    component: 'Input',
  },
  {
    label: '结果反馈',
    field: 'feedbackOnResults',
    component: 'Input',
  },
  {
    label: '外键',
    field: 'pid',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  monitoringLocation: {title: '监测地点',order: 0,view: 'text', type: 'string',},
  monitoringDate: {title: '监测日期',order: 1,view: 'datetime', type: 'string',},
  monitoringInstitution: {title: '监测机构',order: 2,view: 'text', type: 'string',},
  gender: {title: '性别',order: 3,view: 'text', type: 'string',},
  age: {title: '年龄',order: 4,view: 'text', type: 'string',},
  ethnicity: {title: '民族',order: 5,view: 'text', type: 'string',},
  occupation: {title: '职业',order: 6,view: 'text', type: 'string',},
  educationLevel: {title: '教育程度',order: 7,view: 'text', type: 'string',},
  sexualBehavior: {title: '性行为情况',order: 8,view: 'text', type: 'string',},
  injectionDrugUse: {title: '注射吸毒情况',order: 9,view: 'text', type: 'string',},
  sexualTransmissionRoute: {title: '性传播途径',order: 10,view: 'text', type: 'string',},
  otherRiskFactors: {title: '其他风险因素',order: 11,view: 'text', type: 'string',},
  hivTestResult: {title: 'HIV检测结果',order: 12,view: 'text', type: 'string',},
  cd4CellCount: {title: 'CD4细胞计数',order: 13,view: 'text', type: 'string',},
  receivingAntiretroviralTherapy: {title: '是否接受抗病毒治疗',order: 14,view: 'list', type: 'string',dictCode: 'receiving_antiretroviral_therapy',},
  comorbidities: {title: '合并症',order: 15,view: 'text', type: 'string',},
  followStatus: {title: '随访情况',order: 16,view: 'text', type: 'string',},
  interventionMeasures: {title: '干预措施',order: 17,view: 'text', type: 'string',},
  feedbackOnResults: {title: '结果反馈',order: 18,view: 'text', type: 'string',},
  pid: {title: '外键',order: 19,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}