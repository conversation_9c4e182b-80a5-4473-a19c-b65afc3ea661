import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '监测机构',
    align:"center",
    dataIndex: 'monitoringInstitution'
   },
   {
    title: '监测日期',
    align:"center",
    dataIndex: 'monitoringDate'
   },
   {
    title: '数据审核人员',
    align:"center",
    dataIndex: 'dataReviewPersonnel'
   },
   {
    title: '审核日期',
    align:"center",
    dataIndex: 'reviewDate'
   },
   {
    title: '记录完整性',
    align:"center",
    dataIndex: 'recordCompleteness'
   },
   {
    title: '数据准确性',
    align:"center",
    dataIndex: 'dataAccuracy'
   },
   {
    title: '数据一致性',
    align:"center",
    dataIndex: 'dataConsistency'
   },
   {
    title: '质量控制指标',
    align:"center",
    dataIndex: 'qualityControl'
   },
   {
    title: '样本收集规范',
    align:"center",
    dataIndex: 'sampleCollection'
   },
   {
    title: '检测方法规范',
    align:"center",
    dataIndex: 'testingMethod'
   },
   {
    title: '结果反馈机制',
    align:"center",
    dataIndex: 'feedbackMechanism'
   },
   {
    title: '培训记录',
    align:"center",
    dataIndex: 'trainingRecords'
   },
   {
    title: '错误纠正措施',
    align:"center",
    dataIndex: 'errorCorrection'
   },
   {
    title: '监测结果分析',
    align:"center",
    dataIndex: 'monitoringResults'
   },
   {
    title: '定期评估',
    align:"center",
    dataIndex: 'regularEvaluation'
   },
   {
    title: '质量控制报告',
    align:"center",
    dataIndex: 'qualityReport'
   },
   {
    title: '改进措施',
    align:"center",
    dataIndex: 'improvementMeasures'
   },
   {
    title: '外键',
    align:"center",
    dataIndex: 'pid'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '监测机构',
    field: 'monitoringInstitution',
    component: 'Input',
  },
  {
    label: '监测日期',
    field: 'monitoringDate',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '数据审核人员',
    field: 'dataReviewPersonnel',
    component: 'Input',
  },
  {
    label: '审核日期',
    field: 'reviewDate',
    component: 'Input',
  },
  {
    label: '记录完整性',
    field: 'recordCompleteness',
    component: 'Input',
  },
  {
    label: '数据准确性',
    field: 'dataAccuracy',
    component: 'Input',
  },
  {
    label: '数据一致性',
    field: 'dataConsistency',
    component: 'Input',
  },
  {
    label: '质量控制指标',
    field: 'qualityControl',
    component: 'Input',
  },
  {
    label: '样本收集规范',
    field: 'sampleCollection',
    component: 'Input',
  },
  {
    label: '检测方法规范',
    field: 'testingMethod',
    component: 'Input',
  },
  {
    label: '结果反馈机制',
    field: 'feedbackMechanism',
    component: 'Input',
  },
  {
    label: '培训记录',
    field: 'trainingRecords',
    component: 'Input',
  },
  {
    label: '错误纠正措施',
    field: 'errorCorrection',
    component: 'Input',
  },
  {
    label: '监测结果分析',
    field: 'monitoringResults',
    component: 'Input',
  },
  {
    label: '定期评估',
    field: 'regularEvaluation',
    component: 'Input',
  },
  {
    label: '质量控制报告',
    field: 'qualityReport',
    component: 'Input',
  },
  {
    label: '改进措施',
    field: 'improvementMeasures',
    component: 'Input',
  },
  {
    label: '外键',
    field: 'pid',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  monitoringInstitution: {title: '监测机构',order: 0,view: 'text', type: 'string',},
  monitoringDate: {title: '监测日期',order: 1,view: 'datetime', type: 'string',},
  dataReviewPersonnel: {title: '数据审核人员',order: 2,view: 'text', type: 'string',},
  reviewDate: {title: '审核日期',order: 3,view: 'text', type: 'string',},
  recordCompleteness: {title: '记录完整性',order: 4,view: 'text', type: 'string',},
  dataAccuracy: {title: '数据准确性',order: 5,view: 'text', type: 'string',},
  dataConsistency: {title: '数据一致性',order: 6,view: 'text', type: 'string',},
  qualityControl: {title: '质量控制指标',order: 7,view: 'text', type: 'string',},
  sampleCollection: {title: '样本收集规范',order: 8,view: 'text', type: 'string',},
  testingMethod: {title: '检测方法规范',order: 9,view: 'text', type: 'string',},
  feedbackMechanism: {title: '结果反馈机制',order: 10,view: 'text', type: 'string',},
  trainingRecords: {title: '培训记录',order: 11,view: 'text', type: 'string',},
  errorCorrection: {title: '错误纠正措施',order: 12,view: 'text', type: 'string',},
  monitoringResults: {title: '监测结果分析',order: 13,view: 'text', type: 'string',},
  regularEvaluation: {title: '定期评估',order: 14,view: 'text', type: 'string',},
  qualityReport: {title: '质量控制报告',order: 15,view: 'text', type: 'string',},
  improvementMeasures: {title: '改进措施',order: 16,view: 'text', type: 'string',},
  pid: {title: '外键',order: 17,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}