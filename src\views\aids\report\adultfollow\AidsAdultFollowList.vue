<template>
  <BasicModal v-bind="$attrs" @register="registerDrawer" title="患者随访信息" width="100%">
    <!--引用表格-->
   <BasicTable @register="registerTable" :rowSelection="rowSelection">
     <!--插槽:table标题-->
      <template #tableTitle>
          <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
          <!-- <a-button  type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
          <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button> -->
          <a-dropdown v-if="selectedRowKeys.length > 0">
              <template #overlay>
                <a-menu>
                  <a-menu-item key="1" @click="batchHandleDelete">
                    <Icon icon="ant-design:delete-outlined"></Icon>
                    删除
                  </a-menu-item>
                </a-menu>
              </template>
              <a-button>批量操作
                <Icon icon="mdi:chevron-down"></Icon>
              </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <!-- <super-query :config="superQueryConfig" @search="handleSuperQuery" /> -->
      </template>
       <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <!--字段回显插槽-->
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <AidsAdultFollowModal @register="registerModal" :fieldPkFk="fieldPkFk" @success="handleSuccess"></AidsAdultFollowModal>
  </BasicModal>
</template>

<script lang="ts" name="zhiliao-aidsAdultFollow" setup>
  import {ref, reactive, computed, unref} from 'vue';
  import {BasicTable, useTable, TableAction} from '/@/components/Table';
  import {BasicModal, useModalInner} from '/@/components/Modal';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage'
  import AidsAdultFollowModal from './components/AidsAdultFollowModal.vue'
  import {columns, searchFormSchema, superQuerySchema} from './AidsAdultFollow.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl} from './AidsAdultFollow.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, {openModal}] = useModal();
  const fieldPkFk =ref('');
  //let searchayy: FormSchema[] = []
  //onst fieldPkFk;
  //const [registerDrawer] = useModalInner(async (data) => {
   // console.log('--');
  //  console.log(data);
   // fieldPkFk.value=data.id;
    //name.value=data.patientName;
   
    //useListPage.ta: [{ fieldPkFk: unref(fieldPkFk.value) }];
    //reload();
  //}); 
  let [registerDrawer] = useModalInner(async (data) => {
    //console.log('--');
     /* console.log(data.fieldPkFk);
      searchayy=[{field:'fieldPkFk',defaultValue:data.fieldPkFk},{
      label: "报告卡ID",
      field: 'fieldPkFk',
      component: 'Input',
      //colProps: {span: 6},
 	},];
      console.log(searchayy);
      console.log(queryParam); */
      fieldPkFk.value=data.fieldPkFk
      //useListPage.tableProps({schemas:searchFormSchema});
      //=[{ fieldPkFk: unref(data.record) }];
      //reload()
  });
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
      tableProps:{
           title: '治疗随访及用药记录',
           api: list,
           columns,
           canResize:false,
           formConfig: {
              //labelWidth: 120,  
              //schemas: searchFormSchema,
              autoSubmitOnEnter:true,
              showAdvancedButton:true,
              fieldMapToNumber: [
              ],
              fieldMapToTime: [
              ],
            },
           actionColumn: {
               width: 120,
               fixed:'right'
            },
            beforeFetch: (params) => {
              console.log(queryParam+'---');
              //params.push([])
              return Object.assign(params,{fieldPkFk:fieldPkFk.value},  queryParam);
            },
      },
       exportConfig: {
            name:"治疗随访及用药记录",
            url: getExportUrl,
            params: queryParam,
          },
          importConfig: {
            url: getImportUrl,
            success: handleSuccess
          },
  })

  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
   /**
    * 新增事件
    */
  function handleAdd() {
     openModal(true, {
       isUpdate: false,
       showFooter: true,
       fieldPkFk:fieldPkFk, 
     });
  }
   /**
    * 编辑事件
    */
  function handleEdit(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: true,
     });
   }
   /**
    * 详情
   */
  function handleDetail(record: Recordable) {
     openModal(true, {
       record,
       isUpdate: true,
       showFooter: false,
     });
   }
   /**
    * 删除事件
    */
  async function handleDelete(record) {
     await deleteOne({id: record.id}, handleSuccess);
   }
   /**
    * 批量删除事件
    */
  async function batchHandleDelete() {
     await batchDelete({ids: selectedRowKeys.value}, handleSuccess);
   }
   /**
    * 成功回调
    */
  function handleSuccess() {
      (selectedRowKeys.value = []) && reload();
   }
   /**
      * 操作栏
      */
  function getTableAction(record){
       return [
         {
           label: '编辑',
           onClick: handleEdit.bind(null, record),
         }
       ]
   }
     /**
        * 下拉操作栏
        */
  function getDropDownAction(record){
       return [
         {
           label: '详情',
           onClick: handleDetail.bind(null, record),
         }, {
           label: '删除',
           popConfirm: {
             title: '是否确认删除',
             confirm: handleDelete.bind(null, record),
             placement: 'topLeft',
           }
         }
       ]
   }


</script>

<style scoped>
  :deep(.ant-picker),:deep(.ant-input-number){
    width: 100%;
  }
  .ant-modal div[aria-hidden="true"] {
  		display: none !important
  }
</style>
