import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '实施地区',
    align:"center",
    dataIndex: 'organizationRegion_dictText'
   },
   {
    title: '实施机构',
    align:"center",
    dataIndex: 'organizationName_dictText'
   },
   {
    title: '组织名称',
    align:"center",
    dataIndex: 'jigou'
   },
   {
    title: '记录表编号',
    align:"center",
    dataIndex: 'recordId'
   },
   {
    title: '活动名称',
    align:"center",
    dataIndex: 'activityName'
   },
   {
    title: '活动年份',
    align:"center",
    dataIndex: 'activityYeas_dictText'
   },
   {
    title: '活动月度',
    align:"center",
    dataIndex: 'activityMonth_dictText'
   },
   {
    title: '干预的目标人群',
    align:"center",
    dataIndex: 'targetPopulation_dictText'
   },
   {
    title: '活动开始时间',
    align:"center",
    dataIndex: 'activityStartTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '干预活动地点（或场所名称）',
    align:"center",
    dataIndex: 'activityPlace'
   },
   {
    title: '场所全年干预频次统计',
    align:"center",
    dataIndex: 'frequencyStatistics'
   },
   {
    title: '本年度第几次干预',
    align:"center",
    dataIndex: 'frequency'
   },
   {
    title: '本月第几次干预',
    align:"center",
    dataIndex: 'yueganyu'
   },
   {
    title: '干预小组人员签名',
    align:"center",
    dataIndex: 'autograph'
   },
   {
    title: '健康教育和咨询',
    align:"center",
    dataIndex: 'healthConsultation_dictText'
   },
   {
    title: '分发健康教育资料',
    align:"center",
    dataIndex: 'healthMaterials_dictText'
   },
   {
    title: '其他分发健康教育资料',
    align:"center",
    dataIndex: 'healthMaterialsone'
   },
   {
    title: '安全套推广',
    align:"center",
    dataIndex: 'condomPromotion_dictText'
   },
   {
    title: '艾滋病自愿咨询检测',
    align:"center",
    dataIndex: 'testing_dictText'
   },
   {
    title: '提供转介服务',
    align:"center",
    dataIndex: 'referralServices_dictText'
   },
   {
    title: '其他提供转介服务',
    align:"center",
    dataIndex: 'referralServicesone'
   },
   {
    title: '美沙酮药物维持治疗',
    align:"center",
    dataIndex: 'treatment_dictText'
   },
   {
    title: '清洁针具提供/交换',
    align:"center",
    dataIndex: 'exchange_dictText'
   },
   {
    title: '其他干预/宣传活动',
    align:"center",
    dataIndex: 'publicityActivities'
   },
   {
    title: '干预总人数',
    align:"center",
    dataIndex: 'interveneSum'
   },
   {
    title: '本月首次干预人数',
    align:"center",
    dataIndex: 'firstIntervention'
   },
   {
    title: '估计数',
    align:"center",
    dataIndex: 'gujishu'
   },
   {
    title: 'hiv检测阳性人数',
    align:"center",
    dataIndex: 'jiancyangsum'
   },
   {
    title: '同伴教育员数',
    align:"center",
    dataIndex: 'peerEducation'
   },
   {
    title: '现场咨询人数',
    align:"center",
    dataIndex: 'siteConsultation'
   },
   {
    title: '本月检测人数',
    align:"center",
    dataIndex: 'monthPeople'
   },
   {
    title: '本年首次检测人数',
    align:"center",
    dataIndex: 'firstPeople'
   },
   {
    title: '安全套（个）',
    align:"center",
    dataIndex: 'condom'
   },
   {
    title: '安全套润滑剂套装（套）',
    align:"center",
    dataIndex: 'condomLubricant'
   },
   {
    title: '润滑剂（支）',
    align:"center",
    dataIndex: 'lubricant'
   },
   {
    title: '小礼品（份）',
    align:"center",
    dataIndex: 'gift'
   },
   {
    title: '转介卡（张）',
    align:"center",
    dataIndex: 'referralCard'
   },
   {
    title: '宣传资料（份）',
    align:"center",
    dataIndex: 'brochure'
   },
   {
    title: '其他物资',
    align:"center",
    dataIndex: 'material'
   },
   {
    title: '性病服务包(个)',
    align:"center",
    dataIndex: 'xingbingbao'
   },
   {
    title: '目标人群提出的问题及解答情况',
    align:"center",
    dataIndex: 'problemAnswers'
   },
   {
    title: '活动结束时间',
    align:"center",
    dataIndex: 'endTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '记录人签字',
    align:"center",
    dataIndex: 'recordSign'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
      label: "实施地区",
      field: 'organizationRegion',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'organizationName',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "实施机构",
      field: 'organizationName',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
	{
      label: "组织名称",
      field: 'jigou',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "记录表编号",
      field: 'recordId',
      component: 'Input',
      //colProps: {span: 6},
 	},
     {
      label: "活动开始时间",
      field: "activityStartTime",
      component: 'RangePicker',
      componentProps: {
        valueType: 'Date',
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
      label: "实施地区",
      field: 'organizationRegion',
      required:true,
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'organizationName',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "实施机构",
      field: 'organizationName',
      required:true,
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
    label: '组织名称',
    field: 'jigou',
    component: 'Input',
  },
  {
    label: '记录表编号',
    field: 'recordId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入记录表编号!'},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '活动名称',
    field: 'activityName',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入活动名称!'},
                 { min: 1, max: 20, message: '长度在 1-20 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '活动年份',
    field: 'activityYeas',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_year_view,code,name"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入活动年份!'},
          ];
     },
  },
  {
    label: '活动月度',
    field: 'activityMonth',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_monthCode"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入活动月度!'},
          ];
     },
  },
  {
    label: '干预的目标人群',
    field: 'targetPopulation',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_target"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入干预的目标人群!'},
          ];
     },
  },
  {
    label: '活动开始时间',
    field: 'activityStartTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入活动开始时间!'},
          ];
     },
  },
  {
    label: '干预活动地点（或场所名称）',
    field: 'activityPlace',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入干预活动地点（或场所名称）!'},
                 { min: 1, max: 20, message: '长度在 1-20 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '场所全年干预频次统计',
    field: 'frequencyStatistics',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入场所全年干预频次统计!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '本年度第几次干预',
    field: 'frequency',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入本年度第几次干预!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '本月第几次干预',
    field: 'yueganyu',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入本月第几次干预!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '干预小组人员签名',
    field: 'autograph',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入干预小组人员签名!'},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '健康教育和咨询',
    field: 'healthConsultation',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_consul"
     },
  },
  {
    label: '分发健康教育资料',
    field: 'healthMaterials',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_rials_mate"
     },
  },
  {
    label: '其他分发健康教育资料',
    field: 'healthMaterialsone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { min: 0, max: 20, message: '长度在 0-20 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '安全套推广',
    field: 'condomPromotion',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_popul_prom"
     },
  },
  {
    label: '艾滋病自愿咨询检测',
    field: 'testing',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_popul_tes"
     },
  },
  {
    label: '提供转介服务',
    field: 'referralServices',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_ices_serv"
     },
  },
  {
    label: '其他提供转介服务',
    field: 'referralServicesone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { min: 0, max: 20, message: '长度在 0-20 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '美沙酮药物维持治疗',
    field: 'treatment',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_tment_trea"
     },
  },
  {
    label: '清洁针具提供/交换',
    field: 'exchange',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_ange_exch"
     },
  },
  {
    label: '其他干预/宣传活动',
    field: 'publicityActivities',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { min: 0, max: 20, message: '长度在 0-20 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '干预总人数',
    field: 'interveneSum',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入干预总人数!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '本月首次干预人数',
    field: 'firstIntervention',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入本月首次干预人数!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '估计数',
    field: 'gujishu',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 0, max: 20, message: '长度在 0-20 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: 'hiv检测阳性人数',
    field: 'jiancyangsum',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入hiv检测阳性人数!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '同伴教育员数',
    field: 'peerEducation',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入同伴教育员数!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '现场咨询人数',
    field: 'siteConsultation',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入现场咨询人数!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '本月检测人数',
    field: 'monthPeople',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入本月检测人数!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '本年首次检测人数',
    field: 'firstPeople',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入本年首次检测人数!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '安全套（个）',
    field: 'condom',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入安全套（个）!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '安全套润滑剂套装（套）',
    field: 'condomLubricant',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入安全套润滑剂套装（套）!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '润滑剂（支）',
    field: 'lubricant',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入润滑剂（支）!'},
                  {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '小礼品（份）',
    field: 'gift',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                { min: 0, max: 10, message: '长度在 0-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '转介卡（张）',
    field: 'referralCard',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                { min: 0, max: 10, message: '长度在 0-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '宣传资料（份）',
    field: 'brochure',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 0, max: 10, message: '长度在 0-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '其他物资',
    field: 'material',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { min: 0, max: 20, message: '长度在 0-20 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '性病服务包(个)',
    field: 'xingbingbao',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 {pattern: /^(?:0|[1-9]\d*)$/,message: "请输入整数或0"},
                 { min: 0, max: 10, message: '长度在 0-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '目标人群提出的问题及解答情况',
    field: 'problemAnswers',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { min: 0, max: 50, message: '长度在 0-50 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '活动结束时间',
    field: 'endTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入活动结束时间!'},
          ];
     },
  },
  {
    label: '记录人签字',
    field: 'recordSign',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入记录人签字!'},
                 { min: 1, max: 10, message: '长度在 0-10 个字符', trigger: 'blur' }
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
 /* organizationRegion: {title: '实施地区',order: 0,view: 'text', type: 'string',}, */
  jigou: {title: '实施机构/组织名称',order: 1,view: 'text', type: 'string',},
  recordId: {title: '记录表编号',order: 2,view: 'text', type: 'string',},
  activityName: {title: '活动名称',order: 3,view: 'text', type: 'string',},
  activityYeas: {title: '活动年份',order: 4,view: 'list', type: 'string',dictTable: "aids_year_view", dictCode: 'name', dictText: 'code',},
  activityMonth: {title: '活动月度',order: 5,view: 'list', type: 'string',dictCode: 'aids_monthCode',},
  targetPopulation: {title: '干预的目标人群',order: 6,view: 'checkbox', type: 'string',dictCode: 'aids_target',},
  activityStartTime: {title: '活动开始时间',order: 7,view: 'date', type: 'string',},
  activityPlace: {title: '干预活动地点（或场所名称）',order: 8,view: 'text', type: 'string',},
  frequencyStatistics: {title: '场所全年干预频次统计',order: 9,view: 'text', type: 'string',},
  frequency: {title: '本年度第几次干预',order: 10,view: 'text', type: 'string',},
  yueganyu: {title: '本月第几次干预',order: 11,view: 'text', type: 'string',},
  autograph: {title: '干预小组人员签名',order: 12,view: 'text', type: 'string',},
  healthConsultation: {title: '健康教育和咨询',order: 13,view: 'checkbox', type: 'string',dictCode: 'aids_consul',},
  healthMaterials: {title: '分发健康教育资料',order: 14,view: 'checkbox', type: 'string',dictCode: 'aids_rials_mate',},
  healthMaterialsone: {title: '其他分发健康教育资料',order: 15,view: 'text', type: 'string',},
  condomPromotion: {title: '安全套推广',order: 16,view: 'checkbox', type: 'string',dictCode: 'aids_popul_prom',},
  testing: {title: '艾滋病自愿咨询检测',order: 17,view: 'checkbox', type: 'string',dictCode: 'aids_popul_tes',},
  referralServices: {title: '提供转介服务',order: 18,view: 'checkbox', type: 'string',dictCode: 'aids_ices_serv',},
  referralServicesone: {title: '其他提供转介服务',order: 19,view: 'text', type: 'string',},
  treatment: {title: '美沙酮药物维持治疗',order: 20,view: 'checkbox', type: 'string',dictCode: 'aids_tment_trea',},
  exchange: {title: '清洁针具提供/交换',order: 21,view: 'checkbox', type: 'string',dictCode: 'aids_ange_exch',},
  publicityActivities: {title: '其他干预/宣传活动',order: 22,view: 'text', type: 'string',},
  interveneSum: {title: '干预总人数',order: 23,view: 'text', type: 'string',},
  firstIntervention: {title: '本月首次干预人数',order: 24,view: 'text', type: 'string',},
  gujishu: {title: '估计数',order: 25,view: 'text', type: 'string',},
  jiancyangsum: {title: 'hiv检测阳性人数',order: 26,view: 'text', type: 'string',},
  peerEducation: {title: '同伴教育员数',order: 27,view: 'text', type: 'string',},
  siteConsultation: {title: '现场咨询人数',order: 28,view: 'text', type: 'string',},
  monthPeople: {title: '本月检测人数',order: 29,view: 'text', type: 'string',},
  firstPeople: {title: '本年首次检测人数',order: 30,view: 'text', type: 'string',},
  condom: {title: '安全套（个）',order: 31,view: 'text', type: 'string',},
  condomLubricant: {title: '安全套润滑剂套装（套）',order: 32,view: 'text', type: 'string',},
  lubricant: {title: '润滑剂（支）',order: 33,view: 'text', type: 'string',},
  gift: {title: '小礼品（份）',order: 34,view: 'text', type: 'string',},
  referralCard: {title: '转介卡（张）',order: 35,view: 'text', type: 'string',},
  brochure: {title: '宣传资料（份）',order: 36,view: 'text', type: 'string',},
  material: {title: '其他物资',order: 37,view: 'text', type: 'string',},
  xingbingbao: {title: '性病服务包(个)',order: 38,view: 'text', type: 'string',},
  problemAnswers: {title: '目标人群提出的问题及解答情况',order: 39,view: 'text', type: 'string',},
  endTime: {title: '活动结束时间',order: 40,view: 'date', type: 'string',},
  recordSign: {title: '记录人签字',order: 41,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
