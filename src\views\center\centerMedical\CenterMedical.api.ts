import { defHttp } from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/centerMedical/centerMedical/list',
  save='/centerMedical/centerMedical/add',
  edit='/centerMedical/centerMedical/edit',
  deleteOne = '/centerMedical/centerMedical/delete',
  deleteBatch = '/centerMedical/centerMedical/deleteBatch',
  importExcel = '/centerMedical/centerMedical/importExcel',
  exportXls = '/centerMedical/centerMedical/exportXls',
  //提交
  pushBotification = '/centerMedical/centerMedical/pushCotification',
  //提交
  pushBotificationsh = '/centerMedical/centerMedical/pushCotificationsh',  
  //撤销
  pushBotificationshb = '/centerMedical/centerMedical/pushCotificationshb',  
  //批量报告生成
  pushBotificationshbc = '/centerMedical/centerMedical/geneRation',  

}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {

      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
}



//提交
export const pushAotification = (id) => defHttp.post({ 
  url: Api.pushBotification, 
  data: { id }  // 使用 data 传递参数
});


//审核
export const pushAotificationsh = (id) => defHttp.post({ 
  url: Api.pushBotificationsh, 
  data: { id }  // 使用 data 传递参数
});



//撤销
export const pushAotificationshb = (id) => defHttp.post({ 
  url: Api.pushBotificationshb, 
  data: { id }  // 使用 data 传递参数
});


// 修改API调用方式，使用params传递参数
export const pushBotificationshbc = (params) => {
  return defHttp.post({
    url: Api.pushBotificationshbc,
    params: params  // 使用params而不是data
  });
};