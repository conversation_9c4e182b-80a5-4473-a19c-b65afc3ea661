import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '患者姓名',
    align:"center",
    dataIndex: 'patientName'
   },
   {
    title: '筛查人员性别',
    align:"center",
    dataIndex: 'sex_dictText'
   },
   {
    title: '身份证件类别',
    align:"center",
    dataIndex: 'cardType_dictText'
   },
   {
    title: '身份证件号码',
    align:"center",
    dataIndex: 'idCard'
   },
   {
    title: '筛查样本类型',
    align:"center",
    dataIndex: 'sampleType_dictText'
   },
   {
    title: '筛查症状',
    align:"center",
    dataIndex: 'symptom_dictText'
   },
   {
    title: '筛查原因',
    align:"center",
    dataIndex: 'siftReason_dictText'
   },
   {
    title: '筛查时间',
    align:"center",
    dataIndex: 'siftDate'
   },
   {
    title: '筛查结果',
    align:"center",
    dataIndex: 'siftResult_dictText'
   }
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '筛查人员姓名',
    field: 'patientName',
    component: 'Input',
  },
  {
    label: '筛查人员性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sex",
        type: "radio"
     },
  },
  {
   label: '身份证件类别',
   field: 'cardType',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_identity_type"
    },
  },
  {
    label: '身份证件号码',
    field: 'idCard',
    component: 'Input',
  },
  {
   label: '筛查样本类型',
   field: 'sampleType',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_xbscyb"
    },
  },
  {
   label: '筛查症状',
   field: 'symptom',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_xbsczz"
    },
  },
  {
   label: '筛查原因',
   field: 'siftReason',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_xbscyy"
    },
  },
  {
    label: '筛查时间',
    field: 'siftDate',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '筛查结果',
    field: 'siftResult',
    component: 'JDictSelectTag',
    componentProps:{
       dictCode:"aids_xbscjg"
    },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  patientName: {title: '筛查人员姓名',order: 0,view: 'text', type: 'string',},
  sex: {title: '筛查人员性别',order: 1,view: 'radio', type: 'string',dictCode: 'aids_sex',},
  cardType: {title: '身份证件类别',order: 2,view: 'list', type: 'string',dictCode: 'aids_identity_type',},
  idCard: {title: '身份证件号码',order: 3,view: 'text', type: 'string',},
  sampleType: {title: '筛查样本类型',order: 4,view: 'list', type: 'string',dictCode: 'aids_xbscyb',},
  symptom: {title: '筛查症状',order: 5,view: 'list', type: 'string',dictCode: 'aids_xbsczz',},
  siftReason: {title: '筛查原因',order: 6,view: 'list', type: 'string',dictCode: 'aids_xbscyy',},
   siftDate: {title: '筛查时间',order: 7,view: 'datetime', type: 'string',},
   siftResult: {title: '筛查结果',order: 8,view: 'list', type: 'string',dictCode: 'aids_xbscjg',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
