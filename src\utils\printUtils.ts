/**
 * 打印HTML内容
 * @param html HTML内容字符串
 */
export function printHtmlContent(html) {
  try {
    const printWindow = window.open('', '_blank');
    if (!printWindow) {
      throw new Error('无法打开打印窗口');
    }

    printWindow.document.write(`
      <!DOCTYPE html>
      <html>
        <head>
          <title>打印文档</title>
          <meta charset="UTF-8">
          <style>
            * {
              box-sizing: border-box;
              margin: 0;
              padding: 0;
            }
            body { 
              font-family: 'Microsoft YaHei', sans-serif;
              padding: 15px;
              color: #333;
              line-height: 1.6;
            }
            .print-container {
              max-width: 100%;
              overflow: hidden;
            }
            .print-header {
              text-align: center;
              margin-bottom: 20px;
              padding-bottom: 15px;
              border-bottom: 2px solid #1890ff;
            }
            .print-title {
              color: #1890ff;
              font-size: 22px;
              margin: 10px 0;
            }
            .print-content {
              margin: 25px 0;
            }
            .print-content img {
              max-width: 100%;
              max-height: 100vh;
              display: block;
              margin: 0 auto;
            }
            .print-footer {
              margin-top: 30px;
              text-align: right;
              font-size: 14px;
              color: #666;
            }
            @media print {
              body { 
                padding: 0;
                margin: 0;
              }
              .no-print {
                display: none !important;
              }
            }
          </style>
        </head>
        <body>
          <div class="print-container">
            ${html}
            <div class="print-footer">
              打印时间: ${new Date().toLocaleString()}
            </div>
          </div>
          <script>
            // 确保所有资源加载完成后再打印
            window.addEventListener('load', () => {
              window.print();
              setTimeout(() => {
                window.close();
              }, 1000);
            });
          <\/script>
        </body>
      </html>
    `);

    printWindow.document.close();
  } catch (error) {
    console.error('打印失败:', error);
    alert('打印失败，请检查浏览器设置或重试。');
  }
}
