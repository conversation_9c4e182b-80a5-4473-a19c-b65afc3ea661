import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '模板编号',
    align: "center",
    dataIndex: 'templatecode'
  },
  {
    title: '模板名称',
    align: "center",
    dataIndex: 'templatename'
  },
  {
    title: '模板类型',
    align: "center",
    dataIndex: 'templatetype_dictText'
  },
  {
    title: '模板状态',
    align: "center",
    dataIndex: 'state_dictText'
  },
  {
    title: '模板描述',
    align: "center",
    dataIndex: 'templatedescription'
  },
  {
    title: '模板版本',
    align: "center",
    dataIndex: 'templateversion'
  },
  {
    title: '模板内容',
    align: "center",
    dataIndex: 'templatecontent'
  },
  {
    title: '附件链接',
    align: "center",
    dataIndex: 'attachmenturls',
  },
];

// 高级查询数据
export const superQuerySchema = {
  templatecode: {title: '模板编号',order: 0,view: 'text', type: 'string',},
  templatename: {title: '模板名称',order: 1,view: 'text', type: 'string',},
  templatetype: {title: '模板类型',order: 2,view: 'list', type: 'string',dictCode: 'TEMPLATE_TYPE',},
  state: {title: '模板状态',order: 3,view: 'list', type: 'string',dictCode: 'TEMPLATE__STATE',},
  templatedescription: {title: '模板描述',order: 4,view: 'text', type: 'string',},
  templateversion: {title: '模板版本',order: 5,view: 'text', type: 'string',},
  templatecontent: {title: '模板内容',order: 6,view: 'text', type: 'string',},
  attachmenturls: {title: '附件链接',order: 7,view: 'umeditor', type: 'string',},
};
