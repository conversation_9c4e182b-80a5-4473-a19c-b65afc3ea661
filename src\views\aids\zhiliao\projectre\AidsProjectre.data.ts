import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '登记地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: '登记机构',
    align:"center",
    dataIndex: 'orgcode_dictText'
   },
   {
    title: '姓名',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'sex_dictText'
   },
   {
    title: '性取向',
    align:"center",
    dataIndex: 'sexual_dictText'
   },
   {
    title: '民族',
    align:"center",
    dataIndex: 'mingzu_dictText'
   },
   {
    title: '出生日期',
    align:"center",
    dataIndex: 'dateofbirth',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '文化程度',
    align:"center",
    dataIndex: 'degreeofedu_dictText'
   },
   {
    title: '婚姻状况',
    align:"center",
    dataIndex: 'maritalsta_dictText'
   },
   {
    title: '联系电话',
    align:"center",
    dataIndex: 'phone'
   },
   {
    title: '人群',
    align:"center",
    dataIndex: 'crowdcode_dictText'
   },
   {
    title: '来源',
    align:"center",
    dataIndex: 'source_dictText'
   },
   {
    title: '年度',
    align:"center",
    dataIndex: 'years_dictText'
   },
   {
    title: '项目名称',
    align:"center",
    dataIndex: 'entryname_dictText'
   },
   {
    title: '指纹类型',
    align:"center",
    dataIndex: 'zwtype_dictText'
   },
   {
    title: '指纹编码',
    align:"center",
    dataIndex: 'zwid'
   },
   {
    title: '采集人',
    align:"center",
    dataIndex: 'caijiname'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
      label: "登记地区",
      field: 'zonecode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.orgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'orgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "登记机构",
      field: 'orgcode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
	{
      label: "姓名",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
     {
      label: "创建日期",
      field: "createTime",
      component: 'RangePicker',
      componentProps: {
          valueType: 'Date',
          showTime:true
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
      label: "登记地区",
      field: 'zonecode',
      required:true,
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.orgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'orgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "登记机构",
      field: 'orgcode',
      required:true,
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
    label: '姓名',
    field: 'name',
    component: 'Input',
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sex",
        type: "radio"
     },
  },
  {
    label: '性取向',
    field: 'sexual',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sexual",
        type: "radio"
     },
  },
  {
    label: '民族',
    field: 'mingzu',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_nation"
     },
  },
  {
    label: '出生日期',
    field: 'dateofbirth',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '文化程度',
    field: 'degreeofedu',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_culture",
        type: "radio"
     },
  },
  {
    label: '婚姻状况',
    field: 'maritalsta',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_marriage",
        type: "radio"
     },
  },
  {
    label: '联系电话',
    field: 'phone',
    component: 'Input',
  },
  {
    label: '人群',
    field: 'crowdcode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_crowdcode",
        type: "radio"
     },
  },
  {
    label: '来源',
    field: 'source',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_zwsource",
        type: "radio"
     },
  },
  {
    label: '年度',
    field: 'years',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_year_view,code,name"
     },
  },
  {
    label: '项目名称',
    field: 'entryname',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_zwentryname",
        type: "radio"
     },
  },
  {
    label: '指纹类型',
    field: 'zwtype',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_zwtype",
        type: "radio"
     },
  },
  {
    label: '指纹编码',
    field: 'zwid',
    component: 'Input',
  },
  {
    label: '采集人',
    field: 'caijiname',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  name: {title: '姓名',order: 3,view: 'text', type: 'string',},
  sex: {title: '性别',order: 4,view: 'radio', type: 'string',dictCode: 'aids_sex',},
  sexual: {title: '性取向',order: 5,view: 'radio', type: 'string',dictCode: 'aids_sexual',},
  mingzu: {title: '民族',order: 6,view: 'list', type: 'string',dictCode: 'aids_nation',},
  dateofbirth: {title: '出生日期',order: 7,view: 'date', type: 'string',},
  degreeofedu: {title: '文化程度',order: 8,view: 'radio', type: 'string',dictCode: 'aids_mas_dd_culture',},
  maritalsta: {title: '婚姻状况',order: 9,view: 'radio', type: 'string',dictCode: 'aids_mas_dd_marriage',},
  phone: {title: '联系电话',order: 10,view: 'text', type: 'string',},
  crowdcode: {title: '人群',order: 11,view: 'radio', type: 'string',dictCode: 'aids_crowdcode',},
  source: {title: '来源',order: 12,view: 'radio', type: 'string',dictCode: 'aids_zwsource',},
  years: {title: '年度',order: 13,view: 'list', type: 'string',dictTable: "aids_year_view", dictCode: 'name', dictText: 'code',},
  entryname: {title: '项目名称',order: 14,view: 'radio', type: 'string',dictCode: 'aids_zwentryname',},
  zwtype: {title: '指纹类型',order: 15,view: 'radio', type: 'string',dictCode: 'aids_zwtype',},
  zwid: {title: '指纹编码',order: 16,view: 'text', type: 'string',},
  caijiname: {title: '采集人',order: 17,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
