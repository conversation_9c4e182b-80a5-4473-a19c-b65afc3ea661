import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '咨询点编码',
    align:"center",
    dataIndex: 'zhixunma'
   },
   {
    title: '个人编码',
    align:"center",
    dataIndex: 'gerenma'
   },
   {
    title: '人群',
    align:"center",
    dataIndex: 'targetPopulation_dictText'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'sex_dictText'
   },
   {
    title: '民族',
    align:"center",
    dataIndex: 'nation_dictText'
   },
   {
    title: '出生日期',
    align:"center",
    dataIndex: 'birthdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '婚姻状况',
    align:"center",
    dataIndex: 'maritalsta_dictText'
   },
   {
    title: '文化程度',
    align:"center",
    dataIndex: 'degred_dictText'
   },
   {
    title: '联系电话',
    align:"center",
    dataIndex: 'phone'
   },
   {
    title: '求询者来源',
    align:"center",
    dataIndex: 'sourceofin_dictText'
   },
   {
    title: '主要求询原因',
    align:"center",
    dataIndex: 'mainreasons_dictText'
   },
   {
    title: '主要求询原因其它',
    align:"center",
    dataIndex: 'qimainreasons'
   },
   {
    title: '既往是否接受过hiv抗体检测',
    align:"center",
    dataIndex: 'pasthiv_dictText'
   },
   {
    title: '本次是否进行hiv抗体筛查检测',
    align:"center",
    dataIndex: 'benchiv_dictText'
   },
   {
    title: '本次筛查检测结果是',
    align:"center",
    dataIndex: 'bencjieg_dictText'
   },
   {
    title: '本次是否进行hiv确认检测',
    align:"center",
    dataIndex: 'bencqhiv_dictText'
   },
   {
    title: '本次hiv确认检测结果',
    align:"center",
    dataIndex: 'bencjhiv_dictText'
   },
   {
    title: '本次是否进行梅毒血清抗体检测',
    align:"center",
    dataIndex: 'benjianche_dictText'
   },
   {
    title: '本次是否提供了检测后咨询',
    align:"center",
    dataIndex: 'benzhixun_dictText'
   },
   {
    title: '检测后咨询日期',
    align:"center",
    dataIndex: 'jianzhitime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '本次咨询/检测后提供如下哪些转介服务',
    align:"center",
    dataIndex: 'benfuwu_dictText'
   },
   {
    title: '转介服务其它',
    align:"center",
    dataIndex: 'qizhuanjie'
   },
   {
    title: '咨询员',
    align:"center",
    dataIndex: 'zhixunyuan'
   },
   {
    title: '填表日期',
    align:"center",
    dataIndex: 'tianbiaotime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remarks'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "咨询点编码",
      field: 'zhixunma',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "个人编码",
      field: 'gerenma',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "人群",
      field: 'targetPopulation',
      component: 'JDictSelectTag',
      componentProps:{
          dictCode:"aids_target"
      },
      //colProps: {span: 6},
 	},
     {
      label: "填表日期",
      field: "tianbiaotime",
      component: 'RangePicker',
      componentProps: {
        valueType: 'Date',
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '咨询点编码',
    field: 'zhixunma',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入咨询点编码!'},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '个人编码',
    field: 'gerenma',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入个人编码!'},
                 { min: 1, max: 10, message: '长度在 1-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '人群',
    field: 'targetPopulation',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_target"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入人群!'},
          ];
     },
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sex",
        type: "radio"
     },
  },
  {
    label: '民族',
    field: 'nation',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_nation"
     },
  },
  {
    label: '出生日期',
    field: 'birthdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '婚姻状况',
    field: 'maritalsta',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_marriage",
        type: "radio"
     },
  },
  {
    label: '文化程度',
    field: 'degred',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_culture",
        type: "radio"
     },
  },
  {
    label: '联系电话',
    field: 'phone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号码!'},
          ];
     },
  },
  {
    label: '求询者来源',
    field: 'sourceofin',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_zyzxly",
        type: "radio"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入求询者来源!'},
          ];
     },
  },
  {
    label: '主要求询原因',
    field: 'mainreasons',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_zyzxqxyy",
        type: "radio"
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入主要求询原因!'},
          ];
     },
  },
  {
    label: '主要求询原因其它',
    field: 'qimainreasons',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { min: 0, max: 50, message: '长度在 0-50 个字符', trigger: 'blur' }
          ];
     },    
  },
  {
    label: '既往是否接受过hiv抗体检测',
    field: 'pasthiv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_hivktjc",
        type: "radio"
     },
  },
  {
    label: '本次是否进行hiv抗体筛查检测',
    field: 'benchiv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '本次筛查检测结果是',
    field: 'bencjieg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_zyzxscjcjg",
        type: "radio"
     },
  },
  {
    label: '本次是否进行hiv确认检测',
    field: 'bencqhiv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '本次hiv确认检测结果',
    field: 'bencjhiv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_hivqrjcjg",
        type: "radio"
     },
  },
  {
    label: '本次是否进行梅毒血清抗体检测',
    field: 'benjianche',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_zyzxmdxqktjc",
        type: "radio"
     },
  },
  {
    label: '本次是否提供了检测后咨询',
    field: 'benzhixun',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_yesNo",
        type: "radio"
     },
  },
  {
    label: '检测后咨询日期',
    field: 'jianzhitime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '本次咨询/检测后提供如下哪些转介服务',
    field: 'benfuwu',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_zyzxzjfw"
     },
  },
  {
    label: '转介服务其它',
    field: 'qizhuanjie',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { min: 0, max: 50, message: '长度在 0-50 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '咨询员',
    field: 'zhixunyuan',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { min: 0, max: 10, message: '长度在 0-10 个字符', trigger: 'blur' }
          ];
     },
  },
  {
    label: '填表日期',
    field: 'tianbiaotime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '备注',
    field: 'remarks',
    component: 'InputTextArea',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: false},
                 { min: 0, max: 50, message: '长度在 0-50 个字符', trigger: 'blur' }
          ];
     },    
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  zhixunma: {title: '咨询点编码',order: 0,view: 'text', type: 'string',},
  gerenma: {title: '个人编码',order: 1,view: 'text', type: 'string',},
  targetPopulation: {title: '人群',order: 2,view: 'checkbox', type: 'string',dictCode: 'aids_target',},
  sex: {title: '性别',order: 3,view: 'radio', type: 'string',dictCode: 'aids_sex',},
  nation: {title: '民族',order: 4,view: 'list', type: 'string',dictCode: 'aids_nation',},
  birthdate: {title: '出生日期',order: 5,view: 'date', type: 'string',},
  maritalsta: {title: '婚姻状况',order: 6,view: 'radio', type: 'string',dictCode: 'aids_mas_dd_marriage',},
  degred: {title: '文化程度',order: 7,view: 'radio', type: 'string',dictCode: 'aids_mas_dd_culture',},
  phone: {title: '联系电话',order: 8,view: 'text', type: 'string',},
  sourceofin: {title: '求询者来源',order: 9,view: 'radio', type: 'string',dictCode: 'aids_zyzxly',},
  mainreasons: {title: '主要求询原因',order: 10,view: 'radio', type: 'string',dictCode: 'aids_zyzxqxyy',},
  qimainreasons: {title: '主要求询原因其它',order: 11,view: 'text', type: 'string',},
  pasthiv: {title: '既往是否接受过hiv抗体检测',order: 12,view: 'radio', type: 'string',dictCode: 'aids_hivktjc',},
  benchiv: {title: '本次是否进行hiv抗体筛查检测',order: 13,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  bencjieg: {title: '本次筛查检测结果是',order: 14,view: 'radio', type: 'string',dictCode: 'aids_zyzxscjcjg',},
  bencqhiv: {title: '本次是否进行hiv确认检测',order: 15,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  bencjhiv: {title: '本次hiv确认检测结果',order: 16,view: 'radio', type: 'string',dictCode: 'aids_hivqrjcjg',},
  benjianche: {title: '本次是否进行梅毒血清抗体检测',order: 17,view: 'radio', type: 'string',dictCode: 'aids_zyzxmdxqktjc',},
  benzhixun: {title: '本次是否提供了检测后咨询',order: 18,view: 'radio', type: 'string',dictCode: 'aids_yesNo',},
  jianzhitime: {title: '检测后咨询日期',order: 19,view: 'date', type: 'string',},
  benfuwu: {title: '本次咨询/检测后提供如下哪些转介服务',order: 20,view: 'checkbox', type: 'string',dictCode: 'aids_zyzxzjfw',},
  qizhuanjie: {title: '转介服务其它',order: 21,view: 'text', type: 'string',},
  zhixunyuan: {title: '咨询员',order: 22,view: 'text', type: 'string',},
  tianbiaotime: {title: '填表日期',order: 23,view: 'date', type: 'string',},
  remarks: {title: '备注',order: 24,view: 'textarea', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
