import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '征集单位名称',
    align:"center",
    dataIndex: 'unitName'
   },
   {
    title: '征集单位编号',
    align:"center",
    dataIndex: 'unitNumber'
   },
   {
    title: '征集单位地址',
    align:"center",
    dataIndex: 'unitAddress'
   },
   {
    title: '负责人姓名',
    align:"center",
    dataIndex: 'personName'
   },
   {
    title: '负责人联系方式',
    align:"center",
    dataIndex: 'personContact'
   },
   {
    title: '征集对象类型',
    align:"center",
    dataIndex: 'targetType'
   },
   {
    title: '征集对象数量',
    align:"center",
    dataIndex: 'targetQuantity'
   },
   {
    title: '征集开始日期',
    align:"center",
    dataIndex: 'startDate'
   },
   {
    title: '征集结束日期',
    align:"center",
    dataIndex: 'endDate'
   },
   {
    title: '征集单位审核状态',
    align:"center",
    dataIndex: 'reviewStatus'
   },
   {
    title: '监测实施方案',
    align:"center",
    dataIndex: 'monitoringPlan'
   },
   {
    title: '数据收集方法',
    align:"center",
    dataIndex: 'collectionMethod'
   },
   {
    title: '质量控制措施',
    align:"center",
    dataIndex: 'controlMeasures'
   },
   {
    title: '备注信息',
    align:"center",
    dataIndex: 'remarks'
   },
   {
    title: '反馈机制',
    align:"center",
    dataIndex: 'feedbackMechanism'
   },
   {
    title: '外键',
    align:"center",
    dataIndex: 'pid'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '征集单位名称',
    field: 'unitName',
    component: 'Input',
  },
  {
    label: '征集单位编号',
    field: 'unitNumber',
    component: 'Input',
  },
  {
    label: '征集单位地址',
    field: 'unitAddress',
    component: 'Input',
  },
  {
    label: '负责人姓名',
    field: 'personName',
    component: 'Input',
  },
  {
    label: '负责人联系方式',
    field: 'personContact',
    component: 'Input',
  },
  {
    label: '征集对象类型',
    field: 'targetType',
    component: 'Input',
  },
  {
    label: '征集对象数量',
    field: 'targetQuantity',
    component: 'Input',
  },
  {
    label: '征集开始日期',
    field: 'startDate',
    component: 'Input',
  },
  {
    label: '征集结束日期',
    field: 'endDate',
    component: 'Input',
  },
  {
    label: '征集单位审核状态',
    field: 'reviewStatus',
    component: 'Input',
  },
  {
    label: '监测实施方案',
    field: 'monitoringPlan',
    component: 'Input',
  },
  {
    label: '数据收集方法',
    field: 'collectionMethod',
    component: 'Input',
  },
  {
    label: '质量控制措施',
    field: 'controlMeasures',
    component: 'Input',
  },
  {
    label: '备注信息',
    field: 'remarks',
    component: 'Input',
  },
  {
    label: '反馈机制',
    field: 'feedbackMechanism',
    component: 'Input',
  },
  {
    label: '外键',
    field: 'pid',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  unitName: {title: '征集单位名称',order: 0,view: 'text', type: 'string',},
  unitNumber: {title: '征集单位编号',order: 1,view: 'text', type: 'string',},
  unitAddress: {title: '征集单位地址',order: 2,view: 'text', type: 'string',},
  personName: {title: '负责人姓名',order: 3,view: 'text', type: 'string',},
  personContact: {title: '负责人联系方式',order: 4,view: 'text', type: 'string',},
  targetType: {title: '征集对象类型',order: 5,view: 'text', type: 'string',},
  targetQuantity: {title: '征集对象数量',order: 6,view: 'text', type: 'string',},
  startDate: {title: '征集开始日期',order: 7,view: 'text', type: 'string',},
  endDate: {title: '征集结束日期',order: 8,view: 'text', type: 'string',},
  reviewStatus: {title: '征集单位审核状态',order: 9,view: 'text', type: 'string',},
  monitoringPlan: {title: '监测实施方案',order: 10,view: 'text', type: 'string',},
  collectionMethod: {title: '数据收集方法',order: 11,view: 'text', type: 'string',},
  controlMeasures: {title: '质量控制措施',order: 12,view: 'text', type: 'string',},
  remarks: {title: '备注信息',order: 13,view: 'text', type: 'string',},
  feedbackMechanism: {title: '反馈机制',order: 14,view: 'text', type: 'string',},
  pid: {title: '外键',order: 15,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}