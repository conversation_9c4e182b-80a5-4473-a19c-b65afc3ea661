<template>
  <div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <!--          <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
                  <a-button  type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
                  <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>-->
        <!--          <a-dropdown v-if="selectedRowKeys.length > 0">
                      <template #overlay>
                        <a-menu>
                          <a-menu-item key="1" @click="batchHandleDelete">
                            <Icon icon="ant-design:delete-outlined"></Icon>
                            删除
                          </a-menu-item>
                        </a-menu>
                      </template>
                      <a-button>批量操作
                        <Icon icon="mdi:chevron-down"></Icon>
                      </a-button>
                </a-dropdown>-->
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" />
      </template>
      <!--字段回显插槽-->
      <template #bodyCell="{ column, record, index, text }">
        <template v-if="column.dataIndex === 'nowdiagnosezonecode'">
          <!--省市区字段回显插槽-->
          {{ getAreaTextByCode(text) }}
        </template>
        <template v-if="column.dataIndex === 'nowmanagezonecode'">
          <!--省市区字段回显插槽-->
          {{ getAreaTextByCode(text) }}
        </template>
        <template v-if="column.dataIndex === 'firstdiagnosezonecode'">
          <!--省市区字段回显插槽-->
          {{ getAreaTextByCode(text) }}
        </template>
        <template v-if="column.dataIndex === 'livingaddresscode'">
          <!--省市区字段回显插槽-->
          {{ getAreaTextByCode(text) }}
        </template>
        <template v-if="column.dataIndex === 'residenceCode'">
          <!--省市区字段回显插槽-->
          {{ getAreaTextByCode(text) }}
        </template>
        <template v-if="column.dataIndex === 'supervisionZoneCode'">
          <!--省市区字段回显插槽-->
          {{ getAreaTextByCode(text) }}
        </template>
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <TbPanoramaModal @register="registerModal" @success="handleSuccess" />
  </div>
</template>

<script lang="ts" name="tbPanorama-tbPanorama" setup>
  import { ref, reactive, computed, unref, onMounted } from 'vue';
  import { BasicTable, useTable, TableAction } from '/src/components/Table';
  import { useListPage } from '/src/hooks/system/useListPage';
  import { useModal } from '/src/components/Modal';
  import TbPanoramaModal from './components/TbPanoramaModal.vue';
  import { columns, searchFormSchema, superQuerySchema } from './TbPanorama.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl } from './TbPanorama.api';
  import { downloadFile } from '/src/utils/common/renderUtils';
  import { useUserStore } from '/src/store/modules/user';
  import { getAreaTextByCode } from '/src/components/Form/src/utils/Area';
  import { useRouter } from 'vue-router';
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  import PanoramaView from '@/views/aids/tbPanorama/PanoramaView.vue'; // 引入 PanoramaView 组件
  //注册model
  const [registerModal, { openModal }] = useModal();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: 'tb_dossier',
      api: list,
      columns,
      canResize: false,
      formConfig: {
        //labelWidth: 120,
        schemas: searchFormSchema,
        autoSubmitOnEnter: true,
        showAdvancedButton: true,
        fieldMapToNumber: [],
        fieldMapToTime: [],
      },
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: 'tb_dossier',
      url: getExportUrl,
      params: queryParam,
    },
    importConfig: {
      url: getImportUrl,
      success: handleSuccess,
    },
  });

  const [registerTable, { reload }, { rowSelection, selectedRowKeys }] = tableContext;

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }

  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    openModal(true, {
      record,
      isUpdate: true,
      showFooter: false,
    });
  }

  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      },
      {
        label: '查看全景',
        onClick: handleView.bind(null, record),
      },
    ];
  }

  // 提前初始化 router
  const router = useRouter();

  // 查看全景
  async function handleView(record: Recordable) {
    console.log('handleView called with record:', record); // 调试输出

    const targetPath = '/aids-panorama-view';

    try {
      // 确保路由未重复添加
      const existingRoutes = router.getRoutes().map((route) => route.path);
      if (!existingRoutes.includes(targetPath)) {
        // 动态导入组件
        const component = await import('@/views/aids/tbPanorama/PanoramaView.vue');

        // 创建临时路由
        const tempRoute = {
          path: targetPath,
          name: 'PanoramaView',
          component: component.default,
        };

        // 添加临时路由
        router.addRoute(tempRoute);
      }

      // 跳转到新页面并传递用户ID
      router.push({
        path: targetPath,
        query: { id: record.id, registrationno: record.registrationno },
      });
    } catch (error) {
      console.error('导航失败:', error);
    }
  }
</script>

<style scoped>
  :deep(.ant-picker),
  :deep(.ant-input-number) {
    width: 100%;
  }
</style>
