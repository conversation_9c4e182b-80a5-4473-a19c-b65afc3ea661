<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :title="title" :width="width" @ok="handleSubmit" :minHeight="height">
    <template #footer>
      <a-button @click="close">取消</a-button>
      <a-button @click="handleSubmit" type="primary">确认</a-button>
    </template>
    <div >
      <table >
        <tbody>
          <tr>
           <td  >姓名</td>   
           <td  >
             <a-input v-model:value="record.name" :disabled="disabled"  />
           </td>
           </tr>
        </tbody>
        
      </table>
    </div>
  </BasicModal>
</template>

<script lang="ts" setup>
  import { ref, computed, unref, reactive } from 'vue';
  import { BasicModal, useModalInner } from '/@/components/Modal';
  import { saveOrUpdate } from '../InfectioncardAids.api';
  import { JCheckbox, JDictSelectTag } from '@/components/Form';
  import { printNb } from '@/hooks/web/usePrintJS';
  // Emits声明
  const emit = defineEmits(['register', 'success']);
  const isUpdate = ref(true);
  const isDetail = ref(false);
  const width = ref(window.innerWidth * 0.8);
  const height = ref(window.innerHeight * 0.75);
  const record = reactive({
    name: '',
    sex: '',
    age: '',
    idCard: '',
    schoolName: '',
    profession: '',
    residenceAddress: '',
    address: '',
    tel: '',
    parentsTel: '',
    parentsNames: '',
    parentsTel1: '',
    parentsName1: '',
    parentsTel2: '',
    diagnosisResult: '',
    antituberculousDate: '',
    sugIncertificate: '',
    followReq: '',
    treatmentStarttimeOne: '',
    treatmentEndtimeOne: '',
    treatmentStarttimeTwo: '',
    treatmentEndtimeTwo: '',
    treatmentStarttimeThree: '',
    treatmentEndtimeThree: '',
    hospitalThree: '',
    hospitalTwo: '',
    hospitalOne: '',
  });

  const disabled = ref(true);

  //表单赋值
  const [registerModal, { setModalProps, closeModal }] = useModalInner(async (data) => {
    isUpdate.value = !!data?.isUpdate;
    isDetail.value = !!data?.showFooter;
    debugger
    disabled.value = !unref(isDetail);
    clearRecord();
    if (unref(isUpdate)) {
      Object.assign(record, data.record);
    }
  });
  // 清空 record 的值
  const clearRecord = () => {
    Object.keys(record).forEach((key) => {
      record[key] = ''; // 清空为默认值，可以根据实际情况设置不同的初始值
    });
  };
  //设置标题
  const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(isDetail) ? '详情' : '编辑'));
  //表单提交事件
  async function handleSubmit() {
    try {
      setModalProps({ confirmLoading: true });
      //提交表单
      await saveOrUpdate(record, isUpdate.value);
      //关闭弹窗
      closeModal();
      //刷新列表
      emit('success');
    } finally {
      setModalProps({ confirmLoading: false });
    }
  }
  function close() {
    //关闭弹窗
    closeModal();
    //刷新列表
    emit('success');
  }
  function filterInput(event) {
    // 只允许输入数字
    const inputValue = event.target.value;
    // 替换非数字字符并限制长度
    event.target.value = inputValue.replace(/\D/g, '').slice(0, 11);
    record.value.parentsTel2 = event.target.value; // 更新模型
  }

  function print() {
    printNb('print-div');
  }
</script>

<style lang="less" scoped>
 
</style>
