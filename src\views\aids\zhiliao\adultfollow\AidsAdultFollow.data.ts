import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
   title: '治疗机构',
   align:"center",
   dataIndex: 'sitecode_dictText'
  },
  {
   title: '病人治疗编码',
   align:"center",
   dataIndex: 'ptid'
  },
  {
   title: '抗病毒治疗号',
   align:"center",
   dataIndex: 'cardcode'
  },
   {
    title: '随访次数',
    align:"center",
    dataIndex: 'visitN'
   },
  {
   title: '随访状态',
   align:"center",
   dataIndex: 'diseaseSequelaeCode_dictText'
  },
   {
    title: '随访日期',
    align:"center",
    dataIndex: 'followupDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '临床处置',
    align:"center",
    dataIndex: 'f8_dictText'
   },
   {
    title: '创建日期',
    align:"center",
    dataIndex: 'createTime'
   },
   {
    title: '修改日期',
    align:"center",
    dataIndex: 'updateTime'
   },





];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "报告卡ID",
      field: 'fieldPkFk',
      component: 'Input',
      //colProps: {span: 6},
 	},
  {
      label: "报告地区",
      field: 'postcode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.sitecode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'sitecode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "治疗机构",
      field: 'sitecode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
	{
      label: "抗病毒治疗号",
      field: 'cardcode',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "病人治疗编码",
      field: 'ptid',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "患者姓名",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "身份证号",
      field: 'identity',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '随访次数',
    field: 'visitN',
    component: 'Input',
  },
 {
      label: "报告地区",
      field: 'postcode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.sitecode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'sitecode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "治疗机构",
      field: 'sitecode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
    label: '抗病毒治疗号',
    field: 'cardcode',
    component: 'Input',
  },
  {
    label: '病人治疗编码',
    field: 'ptid',
    component: 'Input',
  },
  {
    label: '患者姓名',
    field: 'name',
    component: 'Input',
  },
  {
    label: '身份证件类型',
    field: 'identityType',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_identity_type"
     },
  },
  {
    label: '身份证号',
    field: 'identity',
    component: 'Input',
  },
 {
      label: "现住地区",
      field: 'livingaddresscode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    /* formModel.sitecode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'sitecode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]); */
               },
             };
           },
      //colProps: {span: 6},
  },
  {
    label: '现住详细地址',
    field: 'livingaddressdetails',
    component: 'Input',
  },
 {
      label: "户籍地址",
      field: 'domicileaddresscode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    /* formModel.sitecode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'sitecode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]); */
               },
             };
           },
      //colProps: {span: 6},
  },
  {
    label: '户籍详细地址',
    field: 'domicileadrressdetails',
    component: 'Input',
  },
  {
    label: '病人是否死亡或转出或失访',
    field: 'nameStatu',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_report_nois",
        type: "radio"
     },
  },
  {
    label: '随访日期',
    field: 'followupDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '随访内容',
    field: 'followContent',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_followup_content_code",
        type: "radio"
     },
  },
  {
    label: '自上次随访以来，病人是否出现过以下艾滋病相关疾病',
    field: 'diseaseSymptoms',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_report_symptom"
     },
  },
  {
    label: '目前生长发育情况:体重',
    field: 'height',
    component: 'Input',
  },
  {
    label: '最近7天抗病毒药物漏服',
    field: 'drugLeakages',
    component: 'Input',
  },
  {
    label: '目前是否仍在服用复方新诺明',
    field: 'stillDrug',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_report_nois",
        type: "radio"
     },
  },
  {
    label: '临床处置',
    field: 'f8',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_clinical_handle",
        type: "radio"
     },
  },
  {
    label: '下次随访领药日期',
    field: 'f10',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '药品名称1',
    field: 'drug1',
    component: 'Input',
  },
  {
    label: '发药量',
    field: 'medicine1',
    component: 'Input',
  },
  {
    label: '药品名称2',
    field: 'drug2',
    component: 'Input',
  },
  {
    label: '发药量',
    field: 'medicine2',
    component: 'Input',
  },
  {
    label: '药品名称3',
    field: 'drug3',
    component: 'Input',
  },
  {
    label: '发药量',
    field: 'medicine3',
    component: 'Input',
  },
  {
    label: '随访状态',
    field: 'diseaseSequelaeCode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_followup_status"
     },
  },
  {
    label: '录入日期',
    field: 'addTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '录入人员',
    field: 'addUser',
    component: 'Input',
  },
  {
    label: '治疗方案',
    field: 'treatmentShceme',
    component: 'Input',
  },
  {
    label: '医疗付费方式',
    field: 'paymentMethod',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_paying_type",
        type: "radio"
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  createTime: {title: '创建日期',order: 0,view: 'datetime', type: 'string',},
  visitN: {title: '随访次数',order: 2,view: 'text', type: 'string',},
  cardcode: {title: '抗病毒治疗号',order: 5,view: 'text', type: 'string',},
  ptid: {title: '病人治疗编码',order: 6,view: 'text', type: 'string',},
  followupDate: {title: '随访日期',order: 15,view: 'date', type: 'string',},
  f8: {title: '临床处置',order: 21,view: 'list', type: 'string',dictCode: 'aids_clinical_handle',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
