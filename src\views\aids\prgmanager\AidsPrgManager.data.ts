import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'BED检测ODn值',
    align:"center",
    dataIndex: 'bedodn'
   },
   {
    title: '添加人',
    align:"center",
    dataIndex: 'adduser'
   },
   {
    title: '调查日期',
    align:"center",
    dataIndex: 'a05',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '孕早期梅毒检测-阳性',
    align:"center",
    dataIndex: 'b03bp'
   },
   {
    title: '审核人',
    align:"center",
    dataIndex: 'auduser'
   },
   {
    title: '锁定状态',
    align:"center",
    dataIndex: 'sdstate'
   },
   {
    title: 'BED检测',
    align:"center",
    dataIndex: 'bedjc'
   },
   {
    title: '孕产期梅毒检测产妇数',
    align:"center",
    dataIndex: 'b03a'
   },
   {
    title: '添加地区',
    align:"center",
    dataIndex: 'addzone'
   },
   {
    title: '亲和力检测结果',
    align:"center",
    dataIndex: 'qhjg'
   },
   {
    title: '孕早期艾滋病检测产妇数',
    align:"center",
    dataIndex: 'a03bn'
   },
   {
    title: '修改人',
    align:"center",
    dataIndex: 'modyuser'
   },
   {
    title: '孕早期艾滋病检测-阳性',
    align:"center",
    dataIndex: 'a03bp'
   },
   {
    title: '申请修改时间',
    align:"center",
    dataIndex: 'sqedittime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'A02哨点类型',
    align:"center",
    dataIndex: 'a02'
   },
   {
    title: 'HIV检测孕妇数',
    align:"center",
    dataIndex: 'a02an'
   },
   {
    title: '亲和力检测ODn值',
    align:"center",
    dataIndex: 'qhodn'
   },
   {
    title: '是否有效',
    align:"center",
    dataIndex: 'state'
   },
   {
    title: '调查员签字',
    align:"center",
    dataIndex: 'inquiryname'
   },
   {
    title: '删除时间',
    align:"center",
    dataIndex: 'deletetime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'HIV检测阳性-男',
    align:"center",
    dataIndex: 'a01bm'
   },
   {
    title: '申请修改状态',
    align:"center",
    dataIndex: 'sqstate'
   },
   {
    title: '亲和力检测',
    align:"center",
    dataIndex: 'qhjc'
   },
   {
    title: 'HIV检测孕妇-阳性',
    align:"center",
    dataIndex: 'a02ap'
   },
   {
    title: '孕早期梅毒检测产妇数',
    align:"center",
    dataIndex: 'b03bn'
   },
   {
    title: '修改地区',
    align:"center",
    dataIndex: 'modyzone'
   },
   {
    title: '仅产时艾滋病检测-阳性',
    align:"center",
    dataIndex: 'a03cp'
   },
   {
    title: 'HIV检测人数-女',
    align:"center",
    dataIndex: 'a01af'
   },
   {
    title: 'BED检测结果',
    align:"center",
    dataIndex: 'bedjg'
   },
   {
    title: '哨点负责单位',
    align:"center",
    dataIndex: 'orgcode_dictText'
   },
   {
    title: '仅产时艾滋病检测产妇数',
    align:"center",
    dataIndex: 'a03cn'
   },
   {
    title: '添加科室',
    align:"center",
    dataIndex: 'adddep'
   },
   {
    title: '梅毒检测孕妇-阳性',
    align:"center",
    dataIndex: 'b02ap'
   },
   {
    title: '督导员签字',
    align:"center",
    dataIndex: 'custodian'
   },
   {
    title: 'HIV检测阳性-女',
    align:"center",
    dataIndex: 'a01bf'
   },
   {
    title: '修改时间',
    align:"center",
    dataIndex: 'modytime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '数据交换-数据来源',
    align:"center",
    dataIndex: 'datasource'
   },
   {
    title: '未做新发感染检测原因',
    align:"center",
    dataIndex: 'weixf'
   },
   {
    title: '监测对象征集单位',
    align:"center",
    dataIndex: 'potorgcode'
   },
   {
    title: '修改科室',
    align:"center",
    dataIndex: 'modydep'
   },
   {
    title: '报告地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: 'A03哨点所在地区行政编码',
    align:"center",
    dataIndex: 'a03'
   },
   {
    title: '审核状态',
    align:"center",
    dataIndex: 'audstate'
   },
   {
    title: '哨点所在地区',
    align:"center",
    dataIndex: 'potzonecode_dictText'
   },
   {
    title: '仅产时梅毒检测产妇数',
    align:"center",
    dataIndex: 'b03cn'
   },
   {
    title: '填表日期',
    align:"center",
    dataIndex: 'filltime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '锁定时间',
    align:"center",
    dataIndex: 'sdtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'HIV检测人数-男',
    align:"center",
    dataIndex: 'a01am'
   },
   {
    title: '仅产时梅毒检测-阳性',
    align:"center",
    dataIndex: 'b03cp'
   },
   {
    title: '添加机构',
    align:"center",
    dataIndex: 'addorg'
   },
   {
    title: '梅毒检测孕妇数',
    align:"center",
    dataIndex: 'b02an'
   },
   {
    title: '哨点识别码',
    align:"center",
    dataIndex: 'a01'
   },
   {
    title: '修改机构',
    align:"center",
    dataIndex: 'modyorg'
   },
   {
    title: '数据交换-更新时间',
    align:"center",
    dataIndex: 'datamodytime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '外键',
    align:"center",
    dataIndex: 'pid'
   },
   {
    title: '删除人',
    align:"center",
    dataIndex: 'deleteuser'
   },
   {
    title: '添加时间',
    align:"center",
    dataIndex: 'addtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '审核时间',
    align:"center",
    dataIndex: 'audtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '孕产期HIV检测产妇数',
    align:"center",
    dataIndex: 'a03a'
   },
   {
    title: '病例报告系统卡片ID',
    align:"center",
    dataIndex: 'bingliid'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '报告地区',
    field: 'apanagecode',
    component: 'Zonecode',
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.rptorgcode=''
          //所属部门修改后更新负责部门下拉框数据
          updateSchema([
            {
              field: 'rptorgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'rptorgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: 'BED检测ODn值',
    field: 'bedodn',
    component: 'Input',
  },
  {
    label: '添加人',
    field: 'adduser',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加人!'},
          ];
     },
  },
  {
    label: '调查日期',
    field: 'a05',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '孕早期梅毒检测-阳性',
    field: 'b03bp',
    component: 'Input',
  },
  {
    label: '审核人',
    field: 'auduser',
    component: 'Input',
  },
  {
    label: '锁定状态',
    field: 'sdstate',
    component: 'Input',
  },
  {
    label: 'BED检测',
    field: 'bedjc',
    component: 'Input',
  },
  {
    label: '孕产期梅毒检测产妇数',
    field: 'b03a',
    component: 'Input',
  },
  {
    label: '添加地区',
    field: 'addzone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加地区!'},
          ];
     },
  },
  {
    label: '亲和力检测结果',
    field: 'qhjg',
    component: 'Input',
  },
  {
    label: '孕早期艾滋病检测产妇数',
    field: 'a03bn',
    component: 'Input',
  },
  {
    label: '修改人',
    field: 'modyuser',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改人!'},
          ];
     },
  },
  {
    label: '孕早期艾滋病检测-阳性',
    field: 'a03bp',
    component: 'Input',
  },
  {
    label: '申请修改时间',
    field: 'sqedittime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'A02哨点类型',
    field: 'a02',
    component: 'Input',
  },
  {
    label: 'HIV检测孕妇数',
    field: 'a02an',
    component: 'Input',
  },
  {
    label: '亲和力检测ODn值',
    field: 'qhodn',
    component: 'Input',
  },
  {
    label: '是否有效',
    field: 'state',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入是否有效!'},
          ];
     },
  },
  {
    label: '调查员签字',
    field: 'inquiryname',
    component: 'Input',
  },
  {
    label: '删除时间',
    field: 'deletetime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'HIV检测阳性-男',
    field: 'a01bm',
    component: 'Input',
  },
  {
    label: '申请修改状态',
    field: 'sqstate',
    component: 'Input',
  },
  {
    label: '亲和力检测',
    field: 'qhjc',
    component: 'Input',
  },
  {
    label: 'HIV检测孕妇-阳性',
    field: 'a02ap',
    component: 'Input',
  },
  {
    label: '孕早期梅毒检测产妇数',
    field: 'b03bn',
    component: 'Input',
  },
  {
    label: '修改地区',
    field: 'modyzone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改地区!'},
          ];
     },
  },
  {
    label: '仅产时艾滋病检测-阳性',
    field: 'a03cp',
    component: 'Input',
  },
  {
    label: 'HIV检测人数-女',
    field: 'a01af',
    component: 'Input',
  },
  {
    label: 'BED检测结果',
    field: 'bedjg',
    component: 'Input',
  },
  {
    label: '哨点负责单位',
    field: 'orgcode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  },
  {
    label: '仅产时艾滋病检测产妇数',
    field: 'a03cn',
    component: 'Input',
  },
  {
    label: '添加科室',
    field: 'adddep',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加科室!'},
          ];
     },
  },
  {
    label: '梅毒检测孕妇-阳性',
    field: 'b02ap',
    component: 'Input',
  },
  {
    label: '督导员签字',
    field: 'custodian',
    component: 'Input',
  },
  {
    label: 'HIV检测阳性-女',
    field: 'a01bf',
    component: 'Input',
  },
  {
    label: '修改时间',
    field: 'modytime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改时间!'},
          ];
     },
  },
  {
    label: '数据交换-数据来源',
    field: 'datasource',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入数据交换-数据来源!'},
          ];
     },
  },
  {
    label: '未做新发感染检测原因',
    field: 'weixf',
    component: 'Input',
  },
  {
    label: '监测对象征集单位',
    field: 'potorgcode',
    component: 'Input',
  },
  {
    label: '修改科室',
    field: 'modydep',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改科室!'},
          ];
     },
  },
  {
    label: '报告地区',
    field: 'zonecode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入报告地区!'},
          ];
     },
  },
  {
    label: 'A03哨点所在地区行政编码',
    field: 'a03',
    component: 'Input',
  },
  {
    label: '审核状态',
    field: 'audstate',
    component: 'Input',
  },
  {
    label: '哨点所在地区',
    field: 'potzonecode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: '仅产时梅毒检测产妇数',
    field: 'b03cn',
    component: 'Input',
  },
  {
    label: '填表日期',
    field: 'filltime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '锁定时间',
    field: 'sdtime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'HIV检测人数-男',
    field: 'a01am',
    component: 'Input',
  },
  {
    label: '仅产时梅毒检测-阳性',
    field: 'b03cp',
    component: 'Input',
  },
  {
    label: '添加机构',
    field: 'addorg',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加机构!'},
          ];
     },
  },
  {
    label: '梅毒检测孕妇数',
    field: 'b02an',
    component: 'Input',
  },
  {
    label: '哨点识别码',
    field: 'a01',
    component: 'Input',
  },
  {
    label: '修改机构',
    field: 'modyorg',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改机构!'},
          ];
     },
  },
  {
    label: '数据交换-更新时间',
    field: 'datamodytime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入数据交换-更新时间!'},
          ];
     },
  },
  {
    label: '外键',
    field: 'pid',
    component: 'Input',
  },
  {
    label: '删除人',
    field: 'deleteuser',
    component: 'Input',
  },
  {
    label: '添加时间',
    field: 'addtime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加时间!'},
          ];
     },
  },
  {
    label: '审核时间',
    field: 'audtime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '孕产期HIV检测产妇数',
    field: 'a03a',
    component: 'Input',
  },
  {
    label: '病例报告系统卡片ID',
    field: 'bingliid',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  bedodn: {title: 'BED检测ODn值',order: 0,view: 'text', type: 'string',},
  adduser: {title: '添加人',order: 1,view: 'text', type: 'string',},
  a05: {title: '调查日期',order: 2,view: 'date', type: 'string',},
  b03bp: {title: '孕早期梅毒检测-阳性',order: 3,view: 'text', type: 'string',},
  auduser: {title: '审核人',order: 4,view: 'text', type: 'string',},
  sdstate: {title: '锁定状态',order: 5,view: 'text', type: 'string',},
  bedjc: {title: 'BED检测',order: 6,view: 'text', type: 'string',},
  b03a: {title: '孕产期梅毒检测产妇数',order: 7,view: 'text', type: 'string',},
  addzone: {title: '添加地区',order: 8,view: 'text', type: 'string',},
  qhjg: {title: '亲和力检测结果',order: 9,view: 'text', type: 'string',},
  a03bn: {title: '孕早期艾滋病检测产妇数',order: 10,view: 'text', type: 'string',},
  modyuser: {title: '修改人',order: 11,view: 'text', type: 'string',},
  a03bp: {title: '孕早期艾滋病检测-阳性',order: 12,view: 'text', type: 'string',},
  sqedittime: {title: '申请修改时间',order: 13,view: 'date', type: 'string',},
  a02: {title: 'A02哨点类型',order: 14,view: 'text', type: 'string',},
  a02an: {title: 'HIV检测孕妇数',order: 15,view: 'text', type: 'string',},
  qhodn: {title: '亲和力检测ODn值',order: 16,view: 'text', type: 'string',},
  state: {title: '是否有效',order: 17,view: 'text', type: 'string',},
  inquiryname: {title: '调查员签字',order: 18,view: 'text', type: 'string',},
  deletetime: {title: '删除时间',order: 19,view: 'date', type: 'string',},
  a01bm: {title: 'HIV检测阳性-男',order: 20,view: 'text', type: 'string',},
  sqstate: {title: '申请修改状态',order: 21,view: 'text', type: 'string',},
  qhjc: {title: '亲和力检测',order: 22,view: 'text', type: 'string',},
  a02ap: {title: 'HIV检测孕妇-阳性',order: 23,view: 'text', type: 'string',},
  b03bn: {title: '孕早期梅毒检测产妇数',order: 24,view: 'text', type: 'string',},
  modyzone: {title: '修改地区',order: 25,view: 'text', type: 'string',},
  a03cp: {title: '仅产时艾滋病检测-阳性',order: 26,view: 'text', type: 'string',},
  a01af: {title: 'HIV检测人数-女',order: 27,view: 'text', type: 'string',},
  bedjg: {title: 'BED检测结果',order: 28,view: 'text', type: 'string',},
  orgcode: {title: '哨点负责单位',order: 29,view: 'list', type: 'string',},
  a03cn: {title: '仅产时艾滋病检测产妇数',order: 30,view: 'text', type: 'string',},
  adddep: {title: '添加科室',order: 31,view: 'text', type: 'string',},
  b02ap: {title: '梅毒检测孕妇-阳性',order: 32,view: 'text', type: 'string',},
  custodian: {title: '督导员签字',order: 33,view: 'text', type: 'string',},
  a01bf: {title: 'HIV检测阳性-女',order: 34,view: 'text', type: 'string',},
  modytime: {title: '修改时间',order: 35,view: 'date', type: 'string',},
  datasource: {title: '数据交换-数据来源',order: 36,view: 'text', type: 'string',},
  weixf: {title: '未做新发感染检测原因',order: 37,view: 'text', type: 'string',},
  potorgcode: {title: '监测对象征集单位',order: 38,view: 'text', type: 'string',},
  modydep: {title: '修改科室',order: 39,view: 'text', type: 'string',},
  zonecode: {title: '报告地区',order: 40,view: 'list', type: 'string',},
  a03: {title: 'A03哨点所在地区行政编码',order: 41,view: 'text', type: 'string',},
  audstate: {title: '审核状态',order: 42,view: 'text', type: 'string',},
  potzonecode: {title: '哨点所在地区',order: 43,view: 'list', type: 'string',},
  b03cn: {title: '仅产时梅毒检测产妇数',order: 44,view: 'text', type: 'string',},
  filltime: {title: '填表日期',order: 45,view: 'date', type: 'string',},
  sdtime: {title: '锁定时间',order: 46,view: 'date', type: 'string',},
  a01am: {title: 'HIV检测人数-男',order: 47,view: 'text', type: 'string',},
  b03cp: {title: '仅产时梅毒检测-阳性',order: 48,view: 'text', type: 'string',},
  addorg: {title: '添加机构',order: 49,view: 'text', type: 'string',},
  b02an: {title: '梅毒检测孕妇数',order: 50,view: 'text', type: 'string',},
  a01: {title: '哨点识别码',order: 51,view: 'text', type: 'string',},
  modyorg: {title: '修改机构',order: 52,view: 'text', type: 'string',},
  datamodytime: {title: '数据交换-更新时间',order: 53,view: 'date', type: 'string',},
  pid: {title: '外键',order: 54,view: 'text', type: 'string',},
  deleteuser: {title: '删除人',order: 55,view: 'text', type: 'string',},
  addtime: {title: '添加时间',order: 56,view: 'date', type: 'string',},
  audtime: {title: '审核时间',order: 57,view: 'date', type: 'string',},
  a03a: {title: '孕产期HIV检测产妇数',order: 58,view: 'text', type: 'string',},
  bingliid: {title: '病例报告系统卡片ID',order: 59,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
