-- 注意：该页面对应的前台目录为views/zhiliao文件夹下
-- 如果你想更改到其他目录，请修改sql中component字段对应的值


INSERT INTO sys_permission(id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external) 
VALUES ('2024101706159470390', NULL, '基本情况评估及用药记录', '/zhiliao/aidsAdultInfosList', 'zhiliao/AidsAdultInfosList', NULL, NULL, 0, NULL, '1', 0.00, 0, NULL, 1, int_to_bool(0), 0, 0, 0, NULL, '1', 0, 0, 'admin', '2024-10-17 18:15:39', NULL, NULL, 0);

-- 权限控制sql
-- 新增
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101706159470391', '2024101706159470390', '添加基本情况评估及用药记录', NULL, NULL, 0, NULL, NULL, 2, 'zhiliao:aids_adult_infos:add', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-17 18:15:39', NULL, NULL, 0, 0, '1', 0);
-- 编辑
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101706159470392', '2024101706159470390', '编辑基本情况评估及用药记录', NULL, NULL, 0, NULL, NULL, 2, 'zhiliao:aids_adult_infos:edit', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-17 18:15:39', NULL, NULL, 0, 0, '1', 0);
-- 删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101706159470393', '2024101706159470390', '删除基本情况评估及用药记录', NULL, NULL, 0, NULL, NULL, 2, 'zhiliao:aids_adult_infos:delete', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-17 18:15:39', NULL, NULL, 0, 0, '1', 0);
-- 批量删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101706159470394', '2024101706159470390', '批量删除基本情况评估及用药记录', NULL, NULL, 0, NULL, NULL, 2, 'zhiliao:aids_adult_infos:deleteBatch', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-17 18:15:39', NULL, NULL, 0, 0, '1', 0);
-- 导出excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101706159470395', '2024101706159470390', '导出excel_基本情况评估及用药记录', NULL, NULL, 0, NULL, NULL, 2, 'zhiliao:aids_adult_infos:exportXls', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-17 18:15:39', NULL, NULL, 0, 0, '1', 0);
-- 导入excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101706159470396', '2024101706159470390', '导入excel_基本情况评估及用药记录', NULL, NULL, 0, NULL, NULL, 2, 'zhiliao:aids_adult_infos:importExcel', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-17 18:15:39', NULL, NULL, 0, 0, '1', 0);