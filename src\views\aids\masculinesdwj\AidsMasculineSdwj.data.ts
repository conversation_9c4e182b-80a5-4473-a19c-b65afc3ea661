import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
    {
     title: '病例报告系统卡片ID',
     align:"center",
     dataIndex: 'bingliid'
    },
    {
     title: '条形码编号',
     align:"center",
     dataIndex: 'tiaobianhao'
    },
   {
    title: 'T04梅毒结果判断',
    align:"center",
    dataIndex: 't04_dictText'
   },
   {
    title: 'BED检测ODn值',
    align:"center",
    dataIndex: 'bedodn_dictText'
   },
   {
    title: 'I03同伴教育：',
    align:"center",
    dataIndex: 'drgd_dictText'
   },
   {
    title: '上报国家时间',
    align:"center",
    dataIndex: 'chianTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'D04最近六个月，你与同性发生肛交性行为时使用安全套的频率如何？',
    align:"center",
    dataIndex: 'srty_dictText'
   },
   {
    title: 'B06文化程度',
    align:"center",
    dataIndex: 'jqe_dictText'
   },
   {
    title: 'H01最近一年，你是否曾被诊断患过性病？',
    align:"center",
    dataIndex: 'dh_dictText'
   },
   {
    title: '网络问卷编号',
    align:"center",
    dataIndex: 'wangbianhao'
   },
   {
    title: 'j02自己知道检测结果',
    align:"center",
    dataIndex: 'j02_dictText'
   },
   {
    title: 'C04感染其他性病会增加感染艾滋病的风险吗？',
    align:"center",
    dataIndex: 'sfd_dictText'
   },
   {
    title: '国家返回错误信息',
    align:"center",
    dataIndex: 'messagedesc'
   },
   {
    title: 'J03你的最近一次检测是在什么时间？',
    align:"center",
    dataIndex: 'sfes',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '锁定状态 0=未锁定 1=已锁定',
    align:"center",
    dataIndex: 'suozhuangtai'
   },
   {
    title: 'C07发生高危行为后（共用针具吸毒/不安全性行为等），应主动寻求艾滋病检测与咨询吗',
    align:"center",
    dataIndex: 'we_dictText'
   },
   {
    title: 'B03其他户籍所在地3',
    align:"center",
    dataIndex: 'feq3_dictText'
   },
   {
    title: 'BED检测',
    align:"center",
    dataIndex: 'bedjc_dictText'
   },
   {
    title: 'D01最近六个月，你与同性发生过肛交性行为吗？',
    align:"center",
    dataIndex: 'uy_dictText'
   },
   {
    title: 'editzonecode',
    align:"center",
    dataIndex: 'editzonecode_dictText'
   },
   {
    title: '哨点所在地区',
    align:"center",
    dataIndex: 'eqwe_dictText'
   },
   {
    title: 'F02最近六个月，你与异性发生性行为时使用安全套的频率如何？',
    align:"center",
    dataIndex: 'vdfv_dictText'
   },
/*   {
    title: 'uuid',
    align:"center",
    dataIndex: 'uuid'
   }, */
   {
    title: '申请状态0=未申请 1=已申请',
    align:"center",
    dataIndex: 'shenzhuangtai_dictText'
   },
   {
    title: 'B08其他你最主要找男性性伴的场所/方式',
    align:"center",
    dataIndex: 'lqe2_dictText'
   },
   {
    title: '亲和力检测结果',
    align:"center",
    dataIndex: 'qhjg_dictText'
   },
   {
    title: '监测对象征集单位',
    align:"center",
    dataIndex: 'rqw'
   },
   {
    title: 'T02b如果是，最早确证检测为阳性的时间是：',
    align:"center",
    dataIndex: 'esf',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'deltime',
    align:"center",
    dataIndex: 'deltime'
   },
   {
    title: 'F03最近六个月，你最近一次与异性发生性行为时使用安全套了吗？',
    align:"center",
    dataIndex: 'xgrs_dictText'
   },
   {
    title: 'A03哨点所在地区行政编码',
    align:"center",
    dataIndex: 'yeq_dictText'
   },
   {
    title: 'E02最近六个月，你与同性发生商业性行为时使用安全套的频率如何？',
    align:"center",
    dataIndex: 'rdgd_dictText'
   },
   {
    title: 'C05坚持正确使用安全套可以减少感染和传播艾滋病的风险吗',
    align:"center",
    dataIndex: 'dsf_dictText'
   },
   {
    title: 'E03最近六个月，你最近一次与同性发生商业性行为时使用安全套了吗？',
    align:"center",
    dataIndex: 'rgdg_dictText'
   },
   {
    title: 'A06其他样本来源',
    align:"center",
    dataIndex: 'oeq2_dictText'
   },
   {
    title: '亲和力检测ODn值',
    align:"center",
    dataIndex: 'qhodn_dictText'
   },
   {
    title: 'B01出生年',
    align:"center",
    dataIndex: 'peq',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '报告地区',
    align:"center",
    dataIndex: 'qqwe_dictText'
   },
   {
    title: '状态 1=新增和修改的（未审核），0=已删除的，3=已审核',
    align:"center",
    dataIndex: 'state_dictText'
   },
   {
    title: 'G02你注射过毒品吗？',
    align:"center",
    dataIndex: 'stghddv_dictText'
   },
   {
    title: 'editorgcode',
    align:"center",
    dataIndex: 'editorgcode_dictText'
   },
   {
    title: 'A02哨点类型',
    align:"center",
    dataIndex: 'twe_dictText'
   },
   {
    title: 'B01a年龄',
    align:"center",
    dataIndex: 'aswq_dictText'
   },
   {
    title: 'H02最近一年，你曾被诊断患过何种性病？（可多选）',
    align:"center",
    dataIndex: 'drg_dictText'
   },
   {
    title: '督导员签字',
    align:"center",
    dataIndex: 'sefs'
   },
   {
    title: 'T04a梅毒ELISA初筛结果：',
    align:"center",
    dataIndex: 'gdrgd_dictText'
   },
   {
    title: '亲和力检测',
    align:"center",
    dataIndex: 'qhjc'
   },
   {
    title: 'C09 你认为自己感染上艾滋病病毒的风险有多高？',
    align:"center",
    dataIndex: 'c09_dictText'
   },
   {
    title: 'T03HIV抗体检测结果',
    align:"center",
    dataIndex: 't03_dictText'
   },
   {
    title: 'B01出生年',
    align:"center",
    dataIndex: 'peq1'
   },
   {
    title: 'editexecutor',
    align:"center",
    dataIndex: 'editexecutor'
   },
   {
    title: 'D02最近一周，你与同性发生过多少次肛交性行为？',
    align:"center",
    dataIndex: 'fse_dictText'
   },
   {
    title: 'addorgcode',
    align:"center",
    dataIndex: 'addorgcode'
   },
   {
    title: '哨点负责单位',
    align:"center",
    dataIndex: 'wqwe_dictText'
   },
   {
    title: '上报国家人',
    align:"center",
    dataIndex: 'chianUser'
   },
   {
    title: '审核时间',
    align:"center",
    dataIndex: 'shengdata'
   },
   {
    title: 'BED检测结果',
    align:"center",
    dataIndex: 'bedjg_dictText'
   },
   {
    title: 'A05调查日期',
    align:"center",
    dataIndex: 'iqe',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'F01最近六个月，你与异性发生过性行为吗？',
    align:"center",
    dataIndex: 'grdg_dictText'
   },
   {
    title: 'T01本次调查是否采血？',
    align:"center",
    dataIndex: 'fsrs_dictText'
   },
   {
    title: 'B02婚姻状况',
    align:"center",
    dataIndex: 'sqw_dictText'
   },
   {
    title: 'addzonecode',
    align:"center",
    dataIndex: 'addzonecode'
   },
   {
    title: 'T05aHCV第一次ELISA初筛：',
    align:"center",
    dataIndex: 'sfese_dictText'
   },
   {
    title: 'edittime',
    align:"center",
    dataIndex: 'edittime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'C06使用新型毒品（如冰毒、摇头丸、K粉等）会增加感染艾滋病的风险吗',
    align:"center",
    dataIndex: 'wer_dictText'
   },
   {
    title: 'G01你吸毒吗？',
    align:"center",
    dataIndex: 'sgds_dictText'
   },
   {
    title: 'T05bHCV第二次ELISA复检：',
    align:"center",
    dataIndex: 'fsehy_dictText'
   },
   {
    title: '调查员签字',
    align:"center",
    dataIndex: 'esfeg'
   },
   {
    title: '填表日期',
    align:"center",
    dataIndex: 'fsef',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'T03bHIV第二次ELISA复检：',
    align:"center",
    dataIndex: 'gdr_dictText'
   },
   {
    title: 'HIV病毒载量（单位：拷贝数/ml，t03为0时不填。2024年改造新加字段。）',
    align:"center",
    dataIndex: 'viralLoad'
   },
   {
    title: 'B07你认为自己的性取向是属于下列哪种情况？',
    align:"center",
    dataIndex: 'keq_dictText'
   },
   {
    title: 'I01安全套宣传和发放/艾滋病咨询与检测 ：',
    align:"center",
    dataIndex: 'drge_dictText'
   },
   {
    title: '添加执行人',
    align:"center",
    dataIndex: 'addexecutor'
   },
   {
    title: '未做新发感染检测原因',
    align:"center",
    dataIndex: 'weixf'
   },
   {
    title: 'D03最近六个月，你最近一次与同性发生肛交性行为时使用安全套了吗？',
    align:"center",
    dataIndex: 'nyg_dictText'
   },
   {
    title: 'E01最近六个月，你与同性发生过商业性行为吗？',
    align:"center",
    dataIndex: 'dgd_dictText'
   },
   {
    title: 'J04你的最近一次检测结果是：',
    align:"center",
    dataIndex: 'j04_dictText'
   },
   {
    title: 'C08故意传播艾滋病需要承担法律责任吗',
    align:"center",
    dataIndex: 'fs_dictText'
   },
   {
    title: 'G03你与别人共用过针具吗？',
    align:"center",
    dataIndex: 'xdv_dictText'
   },
   {
    title: '上报国家返回一位标识，用来操作删除',
    align:"center",
    dataIndex: 'returnVlues'
   },
   {
    title: 'B03户籍所在地',
    align:"center",
    dataIndex: 'feq_dictText'
   },
   {
    title: 'G04最近六个月注射毒品时，你与别人共用针具的频率如何？',
    align:"center",
    dataIndex: 'dgr_dictText'
   },
   {
    title: 'T05HCV抗体检测结果',
    align:"center",
    dataIndex: 't05_dictText'
   },
   {
    title: '其他H02最近一年，你曾被诊断患过何种性病？（可多选）',
    align:"center",
    dataIndex: 'drg2_dictText'
   },
   {
    title: '上报国家状态',
    align:"center",
    dataIndex: 'messageFlag'
   },
   {
    title: 'I02社区药物维持治疗 /清洁针具提供/交换：',
    align:"center",
    dataIndex: 'vbtdnb_dictText'
   },
   {
    title: 'T02a是否是既往检测HIV抗体阳性：',
    align:"center",
    dataIndex: 'efs_dictText'
   },
   {
    title: 'B03其他户籍所在地2',
    align:"center",
    dataIndex: 'feq2_dictText'
   },
   {
    title: 'T03aHIV第一次ELISA初筛：',
    align:"center",
    dataIndex: 'drghy_dictText'
   },
   {
    title: 'B05在本地居住时间',
    align:"center",
    dataIndex: 'hw_dictText'
   },
   {
    title: '审核人',
    align:"center",
    dataIndex: 'shenghe'
   },
   {
    title: 'B04民族',
    align:"center",
    dataIndex: 'geq_dictText'
   },
   {
    title: 'C02男男同性性行为人群是我国目前受艾滋病危害最严重的人群吗',
    align:"center",
    dataIndex: 'asd_dictText'
   },
   {
    title: 'A04问卷编号',
    align:"center",
    dataIndex: 'uqw_dictText'
   },
   {
    title: 'C01艾滋病是一种不可治愈的严重传染病吗？',
    align:"center",
    dataIndex: 'awq_dictText'
   },
   {
    title: 'delexecutor',
    align:"center",
    dataIndex: 'delexecutor'
   },
   {
    title: 'A06样本来源',
    align:"center",
    dataIndex: 'oeq_dictText'
   },
   {
    title: 'T04b梅毒RPR/TRUST复检结果：',
    align:"center",
    dataIndex: 'dffsre_dictText'
   },
   {
    title: '添加时间',
    align:"center",
    dataIndex: 'addtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'T03cHIV抗体确认试验结果：',
    align:"center",
    dataIndex: 'gtfd_dictText'
   },
   {
    title: 'j01最近一年做过HIV检测',
    align:"center",
    dataIndex: 'j01_dictText'
   },
   {
    title: 'B08你最主要找男性性伴的场所/方式',
    align:"center",
    dataIndex: 'lqe_dictText'
   },
   {
    title: 'C03通过外表可以判断一个人是否感染了艾滋病吗',
    align:"center",
    dataIndex: 'qew_dictText'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
/* 	{
      label: "T04梅毒结果判断",
      field: 't04',
      component: 'JDictSelectTag',
      componentProps:{
      },
      //colProps: {span: 6},
 	},
	{
      label: "哨点所在地区",
      field: 'eqwe',
      component: 'JDictSelectTag',
      componentProps:{
      },
      //colProps: {span: 6},
 	},
	{
      label: "报告地区",
      field: 'qqwe',
      component: 'JDictSelectTag',
      componentProps:{
      },
      //colProps: {span: 6},
 	},
	{
      label: "哨点负责单位",
      field: 'wqwe',
      component: 'JDictSelectTag',
      componentProps:{
      },
      //colProps: {span: 6},
 	},
	{
      label: "T05bHCV第二次ELISA复检：",
      field: 'fsehy',
      component: 'JDictSelectTag',
      componentProps:{
      },
      //colProps: {span: 6},
 	},
	{
      label: "T05HCV抗体检测结果",
      field: 't05',
      component: 'JDictSelectTag',
      componentProps:{
      },
      //colProps: {span: 6},
 	},
	{
      label: "T03aHIV第一次ELISA初筛：",
      field: 'drghy',
      component: 'JDictSelectTag',
      componentProps:{
      },
      //colProps: {span: 6},
 	},
	{
      label: "条形码编号",
      field: 'tiaobianhao',
      component: 'Input',
      //colProps: {span: 6},
 	}, */
  {
    label: '报告地区',
    field: 'apanagecode',
    component: 'Zonecode',
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.rptorgcode=''
          //所属部门修改后更新负责部门下拉框数据
          updateSchema([
            {
              field: 'rptorgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'rptorgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '病例报告系统卡片ID',
    field: 'bingliid',
    component: 'Input',
  },
  {
    label: '条形码编号',
    field: 'tiaobianhao',
    component: 'Input',
  },
  {
    label: 'T04梅毒结果判断',
    field: 't04',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'BED检测ODn值',
    field: 'bedodn',
    component: 'JDictSelectTag',
    component: 'Input',
  },
  {
    label: 'I03同伴教育：',
    field: 'drgd',
    component: 'JDictSelectTag',
    component: 'Input',
  },
  {
    label: '上报国家时间',
    field: 'chianTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'D04最近六个月，你与同性发生肛交性行为时使用安全套的频率如何？',
    field: 'srty',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques13",
        type: "radio"
     },
     ellipsis: true,
  },
  {
    label: 'B06文化程度',
    field: 'jqe',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques17",
        type: "radio"
     },
  },
  {
    label: 'H01最近一年，你是否曾被诊断患过性病？',
    field: 'dh',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '网络问卷编号',
    field: 'wangbianhao',
    component: 'Input',
  },
  {
    label: 'j02自己知道检测结果',
    field: 'j02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'C04感染其他性病会增加感染艾滋病的风险吗？',
    field: 'sfd',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques4",
        type: "radio"
     },
  },
  {
    label: '国家返回错误信息',
    field: 'messagedesc',
    component: 'Input',
  },
  {
    label: 'J03你的最近一次检测是在什么时间？',
    field: 'sfes',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '锁定状态 0=未锁定 1=已锁定',
    field: 'suozhuangtai',
    component: 'Input',
  },
  {
    label: 'C07发生高危行为后（共用针具吸毒/不安全性行为等），应主动寻求艾滋病检测与咨询吗',
    field: 'we',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques7",
        type: "radio"
     },
  },
  {
    label: 'B03其他户籍所在地3',
    field: 'feq3',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques12"
     },
  },
  {
    label: 'BED检测',
    field: 'bedjc',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'D01最近六个月，你与同性发生过肛交性行为吗？',
    field: 'uy',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  /* {
    label: 'editzonecode',
    field: 'editzonecode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  }, */
  {
    label: '哨点所在地区',
    field: 'eqwe',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  },
  {
    label: 'F02最近六个月，你与异性发生性行为时使用安全套的频率如何？',
    field: 'vdfv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques13",
        type: "radio"
     },
  },
/*  {
    label: 'uuid',
    field: 'uuid',
    component: 'Input',
  }, */
  {
    label: '申请状态0=未申请 1=已申请',
    field: 'shenzhuangtai',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"",
        type: "radio"
     },
  },
  {
    label: 'B08其他你最主要找男性性伴的场所/方式',
    field: 'lqe2',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques18",
        type: "radio"
     },
  },
  {
    label: '亲和力检测结果',
    field: 'qhjg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '监测对象征集单位',
    field: 'rqw',
    component: 'Input',
  },
  {
    label: 'T02b如果是，最早确证检测为阳性的时间是：',
    field: 'esf',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'deltime',
    field: 'deltime',
    component: 'Input',
  },
  {
    label: 'F03最近六个月，你最近一次与异性发生性行为时使用安全套了吗？',
    field: 'xgrs',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'A03哨点所在地区行政编码',
    field: 'yeq',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"zonecode",
        type: "radio"
     },
  },
  {
    label: 'E02最近六个月，你与同性发生商业性行为时使用安全套的频率如何？',
    field: 'rdgd',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques13",
        type: "radio"
     },
  },
  {
    label: 'C05坚持正确使用安全套可以减少感染和传播艾滋病的风险吗',
    field: 'dsf',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'E03最近六个月，你最近一次与同性发生商业性行为时使用安全套了吗？',
    field: 'rgdg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'A06其他样本来源',
    field: 'oeq2',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques18",
        type: "radio"
     },
  },
  {
    label: '亲和力检测ODn值',
    field: 'qhodn',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'B01出生年',
    field: 'peq',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '报告地区',
    field: 'qqwe',
    component: 'Zonecode',
    componentProps: ({ formActionType, formModel }) => {
       return {
         onOptionsChange: (values) => {
           const { updateSchema } = formActionType;
           formModel.rptorgcode=''
           //所属部门修改后更新负责部门下拉框数据
           updateSchema([
             {
               field: 'rptorgcode',
               componentProps: { zonecode:values,value:''},
             },
           ]);
         },
       };
     },
  },
  /* {
    label: '状态 1=新增和修改的（未审核），0=已删除的，3=已审核',
    field: 'state',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"",
        type: "radio"
     },
  }, */
  {
    label: 'G02你注射过毒品吗？',
    field: 'stghddv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  /* {
    label: 'editorgcode',
    field: 'editorgcode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  }, */
  {
    label: 'A02哨点类型',
    field: 'twe',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sentinel_type",
        type: "radio"
     },
  },
  {
    label: 'B01a年龄',
    field: 'aswq',
    component: 'Input',
  },
  /* {
    label: 'H02最近一年，你曾被诊断患过何种性病？（可多选）',
    field: 'drg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"",
        type: "radio"
     },
  }, */
  {
    label: '督导员签字',
    field: 'sefs',
    component: 'Input',
  },
  {
    label: 'T04a梅毒ELISA初筛结果：',
    field: 'gdrgd',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '亲和力检测',
    field: 'qhjc',
    component: 'Input',
  },
  {
    label: 'C09 你认为自己感染上艾滋病病毒的风险有多高？',
    field: 'c09',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques14",
        type: "radio"
     },
  },
  {
    label: 'T03HIV抗体检测结果',
    field: 't03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'B01出生年',
    field: 'peq1',
    component: 'Input',
  },
  /* {
    label: 'editexecutor',
    field: 'editexecutor',
    component: 'Input',
  }, */
  {
    label: 'D02最近一周，你与同性发生过多少次肛交性行为？',
    field: 'fse',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques15",
        type: "radio"
     },
  },
  /* {
    label: 'addorgcode',
    field: 'addorgcode',
    component: 'Input',
  }, */
  {
    label: '哨点负责单位',
    field: 'wqwe',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: '上报国家人',
    field: 'chianUser',
    component: 'Input',
  },
  {
    label: '审核时间',
    field: 'shengdata',
    component: 'Input',
  },
  {
    label: 'BED检测结果',
    field: 'bedjg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'A05调查日期',
    field: 'iqe',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'F01最近六个月，你与异性发生过性行为吗？',
    field: 'grdg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'T01本次调查是否采血？',
    field: 'fsrs',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'B02婚姻状况',
    field: 'sqw',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques16",
        type: "radio"
     },
  },
  {
    label: 'addzonecode',
    field: 'addzonecode',
    component: 'Input',
  },
  {
    label: 'T05aHCV第一次ELISA初筛：',
    field: 'sfese',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  /* {
    label: 'edittime',
    field: 'edittime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  }, */
  {
    label: 'C06使用新型毒品（如冰毒、摇头丸、K粉等）会增加感染艾滋病的风险吗',
    field: 'wer',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'G01你吸毒吗？',
    field: 'sgds',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'T05bHCV第二次ELISA复检：',
    field: 'fsehy',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '调查员签字',
    field: 'esfeg',
    component: 'Input',
  },
  {
    label: '填表日期',
    field: 'fsef',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'T03bHIV第二次ELISA复检：',
    field: 'gdr',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'HIV病毒载量（单位：拷贝数/ml，t03为0时不填。2024年改造新加字段。）',
    field: 'viralLoad',
    component: 'Input',
  },
  {
    label: 'B07你认为自己的性取向是属于下列哪种情况？',
    field: 'keq',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques19",
        type: "radio"
     },
  },
  {
    label: 'I01安全套宣传和发放/艾滋病咨询与检测 ：',
    field: 'drge',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '添加执行人',
    field: 'addexecutor',
    component: 'Input',
  },
  {
    label: '未做新发感染检测原因',
    field: 'weixf',
    component: 'Input',
  },
  {
    label: 'D03最近六个月，你最近一次与同性发生肛交性行为时使用安全套了吗？',
    field: 'nyg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques19",
        type: "radio"
     },
  },
  {
    label: 'E01最近六个月，你与同性发生过商业性行为吗？',
    field: 'dgd',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques19",
        type: "radio"
     },
  },
  {
    label: 'J04你的最近一次检测结果是：',
    field: 'j04',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'C08故意传播艾滋病需要承担法律责任吗',
    field: 'fs',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'G03你与别人共用过针具吗？',
    field: 'xdv',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '上报国家返回一位标识，用来操作删除',
    field: 'returnVlues',
    component: 'Input',
  },
  {
    label: 'B03户籍所在地',
    field: 'feq',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques12"
     },
  },
  {
    label: 'G04最近六个月注射毒品时，你与别人共用针具的频率如何？',
    field: 'dgr',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques13",
        type: "radio"
     },
  },
  {
    label: 'T05HCV抗体检测结果',
    field: 't05',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '其他H02最近一年，你曾被诊断患过何种性病？（可多选）',
    field: 'drg2',
    component: 'JSelectMultiple',
    componentProps:{
        dictCode:"aids_ques20",
        /* type: "radio" */
     },
     //下拉多选
     mode:'multiple',
  },
  {
    label: '上报国家状态',
    field: 'messageFlag',
    component: 'Input',
  },
  {
    label: 'I02社区药物维持治疗 /清洁针具提供/交换：',
    field: 'vbtdnb',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'T02a是否是既往检测HIV抗体阳性：',
    field: 'efs',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques13",
        type: "radio"
     },
  },
  {
    label: 'B03其他户籍所在地2',
    field: 'feq2',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques12",
        type: "radio"
     },
  },
  {
    label: 'T03aHIV第一次ELISA初筛：',
    field: 'drghy',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  /* {
    label: 'B05在本地居住时间',
    field: 'hw',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"",
        type: "radio"
     },
  }, */
  {
    label: '审核人',
    field: 'shenghe',
    component: 'Input',
  },
  {
    label: 'B04民族',
    field: 'geq',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_nation",
        type: "radio"
     },
  },
  {
    label: 'C02男男同性性行为人群是我国目前受艾滋病危害最严重的人群吗',
    field: 'asd',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'A04问卷编号',
    field: 'uqw',
    component: 'Input',
  },
  {
    label: 'C01艾滋病是一种不可治愈的严重传染病吗？',
    field: 'awq',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  /* {
    label: 'delexecutor',
    field: 'delexecutor',
    component: 'Input',
  }, */
  {
    label: 'A06样本来源',
    field: 'oeq',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques18",
        type: "radio"
     },
  },
  {
    label: 'T04b梅毒RPR/TRUST复检结果：',
    field: 'dffsre',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '添加时间',
    field: 'addtime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'T03cHIV抗体确认试验结果：',
    field: 'gtfd',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'j01最近一年做过HIV检测',
    field: 'j01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'B08你最主要找男性性伴的场所/方式',
    field: 'lqe',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques18",
        type: "radio"
     },
  },
  {
    label: 'C03通过外表可以判断一个人是否感染了艾滋病吗',
    field: 'qew',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  t04: {title: 'T04梅毒结果判断',order: 0,view: 'radio', type: 'string',},
  bedodn: {title: 'BED检测ODn值',order: 1,view: 'radio', type: 'string',},
  drgd: {title: 'I03同伴教育：',order: 2,view: 'radio', type: 'string',},
  chianTime: {title: '上报国家时间',order: 3,view: 'date', type: 'string',},
  srty: {title: 'D04最近六个月，你与同性发生肛交性行为时使用安全套的频率如何？',order: 4,view: 'radio', type: 'string',},
  jqe: {title: 'B06文化程度',order: 5,view: 'radio', type: 'string',},
  dh: {title: 'H01最近一年，你是否曾被诊断患过性病？',order: 6,view: 'radio', type: 'string',},
  wangbianhao: {title: '网络问卷编号',order: 7,view: 'text', type: 'string',},
  j02: {title: 'j02自己知道检测结果',order: 8,view: 'radio', type: 'string',},
  sfd: {title: 'C04感染其他性病会增加感染艾滋病的风险吗？',order: 9,view: 'radio', type: 'string',},
  messagedesc: {title: '国家返回错误信息',order: 10,view: 'text', type: 'string',},
  sfes: {title: 'J03你的最近一次检测是在什么时间？',order: 11,view: 'date', type: 'string',},
  suozhuangtai: {title: '锁定状态 0=未锁定 1=已锁定',order: 12,view: 'text', type: 'string',},
  we: {title: 'C07发生高危行为后（共用针具吸毒/不安全性行为等），应主动寻求艾滋病检测与咨询吗',order: 13,view: 'radio', type: 'string',},
  feq3: {title: 'B03其他户籍所在地3',order: 14,view: 'list', type: 'string',},
  bedjc: {title: 'BED检测',order: 15,view: 'radio', type: 'string',},
  uy: {title: 'D01最近六个月，你与同性发生过肛交性行为吗？',order: 16,view: 'radio', type: 'string',},
  editzonecode: {title: 'editzonecode',order: 17,view: 'list', type: 'string',},
  eqwe: {title: '哨点所在地区',order: 18,view: 'list', type: 'string',},
  vdfv: {title: 'F02最近六个月，你与异性发生性行为时使用安全套的频率如何？',order: 19,view: 'radio', type: 'string',},
  uuid: {title: 'uuid',order: 20,view: 'text', type: 'string',},
  shenzhuangtai: {title: '申请状态0=未申请 1=已申请',order: 21,view: 'radio', type: 'string',},
  lqe2: {title: 'B08其他你最主要找男性性伴的场所/方式',order: 22,view: 'radio', type: 'string',},
  qhjg: {title: '亲和力检测结果',order: 23,view: 'radio', type: 'string',},
  rqw: {title: '监测对象征集单位',order: 24,view: 'text', type: 'string',},
  esf: {title: 'T02b如果是，最早确证检测为阳性的时间是：',order: 25,view: 'date', type: 'string',},
  deltime: {title: 'deltime',order: 26,view: 'text', type: 'string',},
  xgrs: {title: 'F03最近六个月，你最近一次与异性发生性行为时使用安全套了吗？',order: 27,view: 'text', type: 'string',},
  yeq: {title: 'A03哨点所在地区行政编码',order: 28,view: 'radio', type: 'string',},
  rdgd: {title: 'E02最近六个月，你与同性发生商业性行为时使用安全套的频率如何？',order: 29,view: 'radio', type: 'string',},
  dsf: {title: 'C05坚持正确使用安全套可以减少感染和传播艾滋病的风险吗',order: 30,view: 'radio', type: 'string',},
  rgdg: {title: 'E03最近六个月，你最近一次与同性发生商业性行为时使用安全套了吗？',order: 31,view: 'radio', type: 'string',},
  oeq2: {title: 'A06其他样本来源',order: 32,view: 'radio', type: 'string',},
  qhodn: {title: '亲和力检测ODn值',order: 33,view: 'radio', type: 'string',},
  peq: {title: 'B01出生年',order: 34,view: 'date', type: 'string',},
  qqwe: {title: '报告地区',order: 35,view: 'list', type: 'string',},
  state: {title: '状态 1=新增和修改的（未审核），0=已删除的，3=已审核',order: 36,view: 'radio', type: 'string',},
  stghddv: {title: 'G02你注射过毒品吗？',order: 37,view: 'radio', type: 'string',},
  editorgcode: {title: 'editorgcode',order: 38,view: 'list', type: 'string',},
  twe: {title: 'A02哨点类型',order: 39,view: 'radio', type: 'string',},
  aswq: {title: 'B01a年龄',order: 40,view: 'radio', type: 'string',},
  drg: {title: 'H02最近一年，你曾被诊断患过何种性病？（可多选）',order: 41,view: 'radio', type: 'string',},
  sefs: {title: '督导员签字',order: 42,view: 'text', type: 'string',},
  gdrgd: {title: 'T04a梅毒ELISA初筛结果：',order: 43,view: 'radio', type: 'string',},
  qhjc: {title: '亲和力检测',order: 44,view: 'text', type: 'string',},
  c09: {title: 'C09 你认为自己感染上艾滋病病毒的风险有多高？',order: 45,view: 'radio', type: 'string',},
  t03: {title: 'T03HIV抗体检测结果',order: 46,view: 'radio', type: 'string',},
  peq1: {title: 'B01出生年',order: 47,view: 'text', type: 'string',},
  editexecutor: {title: 'editexecutor',order: 48,view: 'text', type: 'string',},
  fse: {title: 'D02最近一周，你与同性发生过多少次肛交性行为？',order: 49,view: 'radio', type: 'string',},
  addorgcode: {title: 'addorgcode',order: 50,view: 'text', type: 'string',},
  wqwe: {title: '哨点负责单位',order: 51,view: 'list', type: 'string',},
  chianUser: {title: '上报国家人',order: 52,view: 'text', type: 'string',},
  shengdata: {title: '审核时间',order: 53,view: 'text', type: 'string',},
  bedjg: {title: 'BED检测结果',order: 54,view: 'radio', type: 'string',},
  iqe: {title: 'A05调查日期',order: 55,view: 'date', type: 'string',},
  grdg: {title: 'F01最近六个月，你与异性发生过性行为吗？',order: 56,view: 'radio', type: 'string',},
  fsrs: {title: 'T01本次调查是否采血？',order: 57,view: 'radio', type: 'string',},
  sqw: {title: 'B02婚姻状况',order: 58,view: 'radio', type: 'string',},
  addzonecode: {title: 'addzonecode',order: 59,view: 'text', type: 'string',},
  sfese: {title: 'T05aHCV第一次ELISA初筛：',order: 60,view: 'radio', type: 'string',},
  edittime: {title: 'edittime',order: 61,view: 'date', type: 'string',},
  wer: {title: 'C06使用新型毒品（如冰毒、摇头丸、K粉等）会增加感染艾滋病的风险吗',order: 62,view: 'radio', type: 'string',},
  sgds: {title: 'G01你吸毒吗？',order: 63,view: 'radio', type: 'string',},
  fsehy: {title: 'T05bHCV第二次ELISA复检：',order: 64,view: 'radio', type: 'string',},
  esfeg: {title: '调查员签字',order: 65,view: 'text', type: 'string',},
  fsef: {title: '填表日期',order: 66,view: 'date', type: 'string',},
  gdr: {title: 'T03bHIV第二次ELISA复检：',order: 67,view: 'radio', type: 'string',},
  viralLoad: {title: 'HIV病毒载量（单位：拷贝数/ml，t03为0时不填。2024年改造新加字段。）',order: 68,view: 'text', type: 'string',},
  keq: {title: 'B07你认为自己的性取向是属于下列哪种情况？',order: 69,view: 'radio', type: 'string',},
  drge: {title: 'I01安全套宣传和发放/艾滋病咨询与检测 ：',order: 70,view: 'radio', type: 'string',},
  addexecutor: {title: '添加执行人',order: 71,view: 'text', type: 'string',},
  weixf: {title: '未做新发感染检测原因',order: 72,view: 'text', type: 'string',},
  nyg: {title: 'D03最近六个月，你最近一次与同性发生肛交性行为时使用安全套了吗？',order: 73,view: 'radio', type: 'string',},
  dgd: {title: 'E01最近六个月，你与同性发生过商业性行为吗？',order: 74,view: 'radio', type: 'string',},
  j04: {title: 'J04你的最近一次检测结果是：',order: 75,view: 'radio', type: 'string',},
  fs: {title: 'C08故意传播艾滋病需要承担法律责任吗',order: 76,view: 'radio', type: 'string',},
  xdv: {title: 'G03你与别人共用过针具吗？',order: 77,view: 'radio', type: 'string',},
  returnVlues: {title: '上报国家返回一位标识，用来操作删除',order: 78,view: 'text', type: 'string',},
  feq: {title: 'B03户籍所在地',order: 79,view: 'list', type: 'string',},
  dgr: {title: 'G04最近六个月注射毒品时，你与别人共用针具的频率如何？',order: 80,view: 'radio', type: 'string',},
  t05: {title: 'T05HCV抗体检测结果',order: 81,view: 'radio', type: 'string',},
  drg2: {title: '其他H02最近一年，你曾被诊断患过何种性病？（可多选）',order: 82,view: 'radio', type: 'string',},
  messageFlag: {title: '上报国家状态',order: 83,view: 'text', type: 'string',},
  vbtdnb: {title: 'I02社区药物维持治疗 /清洁针具提供/交换：',order: 84,view: 'radio', type: 'string',},
  efs: {title: 'T02a是否是既往检测HIV抗体阳性：',order: 85,view: 'radio', type: 'string',},
  feq2: {title: 'B03其他户籍所在地2',order: 86,view: 'radio', type: 'string',},
  drghy: {title: 'T03aHIV第一次ELISA初筛：',order: 87,view: 'radio', type: 'string',},
  hw: {title: 'B05在本地居住时间',order: 88,view: 'radio', type: 'string',},
  shenghe: {title: '审核人',order: 89,view: 'text', type: 'string',},
  geq: {title: 'B04民族',order: 90,view: 'radio', type: 'string',},
  asd: {title: 'C02男男同性性行为人群是我国目前受艾滋病危害最严重的人群吗',order: 91,view: 'radio', type: 'string',},
  uqw: {title: 'A04问卷编号',order: 92,view: 'radio', type: 'string',},
  awq: {title: 'C01艾滋病是一种不可治愈的严重传染病吗？',order: 93,view: 'radio', type: 'string',},
  delexecutor: {title: 'delexecutor',order: 94,view: 'text', type: 'string',},
  oeq: {title: 'A06样本来源',order: 95,view: 'radio', type: 'string',},
  dffsre: {title: 'T04b梅毒RPR/TRUST复检结果：',order: 96,view: 'radio', type: 'string',},
  addtime: {title: '添加时间',order: 97,view: 'date', type: 'string',},
  gtfd: {title: 'T03cHIV抗体确认试验结果：',order: 98,view: 'radio', type: 'string',},
  j01: {title: 'j01最近一年做过HIV检测',order: 99,view: 'radio', type: 'string',},
  lqe: {title: 'B08你最主要找男性性伴的场所/方式',order: 100,view: 'radio', type: 'string',},
  qew: {title: 'C03通过外表可以判断一个人是否感染了艾滋病吗',order: 101,view: 'radio', type: 'string',},
  tiaobianhao: {title: '条形码编号',order: 102,view: 'text', type: 'string',},
  bingliid: {title: '病例报告系统卡片ID',order: 103,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
