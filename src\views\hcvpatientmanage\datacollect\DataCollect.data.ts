import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import {JVxeTypes,JVxeColumn} from '/@/components/jeecg/JVxeTable/types'
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '患者姓名',
    align:"center",
    dataIndex: 'patientName'
   },
   {
    title: '身份证件类别',
    align:"center",
    dataIndex: 'cardType_dictText'
   },
   {
    title: '身份证件号码',
    align:"center",
    dataIndex: 'idCard'
   },
   {
    title: '就诊日期时间',
    align:"center",
    dataIndex: 'serialNumber'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '患者姓名',
    field: 'patientName',
    component: 'Input',
  },
  {
   label: '身份证件类别',
   field: 'cardType',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_identity_type"
    },
  },
  {
    label: '身份证号',
    field: 'idCard',
    component: 'Input',
  },
  {
    label: '就诊日期时间',
    field: 'serialNumber',
    component: 'DatePicker',
    componentProps: {
       showTime:true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];
//子表单数据
//子表表格配置
export const inspectExamColumns: JVxeColumn[] = [
    {
      title: '检查项目',
      key: 'examinationItemCode',
      type: JVxeTypes.select,
      options:[],
      dictCode:"",
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '检查结果',
      key: 'examinationResult',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '检查日期',
      key: 'timestamp',
      type: JVxeTypes.datetime,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
  ]
export const medicationInfoColumns: JVxeColumn[] = [
    {
      title: '通用药物代码',
      key: 'drugCode',
      type: JVxeTypes.select,
      options:[],
      dictCode:"",
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '药物使用次剂量',
      key: 'drugDoseCode',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '每日用药次数',
      key: 'drugFrequency',
      type: JVxeTypes.input,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '服药方法代码',
      key: 'drugDosageRouteCode',
      type: JVxeTypes.select,
      options:[],
      dictCode:"",
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '开始服药日期',
      key: 'drugBeginDate',
      type: JVxeTypes.datetime,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
    {
      title: '结束服药日期',
      key: 'drugEndDate',
      type: JVxeTypes.datetime,
      width:"200px",
      placeholder: '请输入${title}',
      defaultValue:'',
    },
  ]


// 高级查询数据
export const superQuerySchema = {
  patientName: {title: '患者姓名',order: 0,view: 'text', type: 'string',},
  cardType: {title: '身份证件类别',order: 1,view: 'list', type: 'string',dictCode: 'aids_identity_type',},
  idCard: {title: '身份证件号码',order: 2,view: 'text', type: 'string',},
  serialNumber: {title: '就诊日期时间',order: 3,view: 'datetime', type: 'string',},
  //子表高级查询
  inspectExam: {
    title: '检验检查表',
    view: 'table',
    fields: {
        fk: {title: '外键',order: 0,view: 'text', type: 'string',},
        examinationItemCode: {title: '检查项目',order: 1,view: 'list', type: 'string',},
        examinationResultCode: {title: '检查结果',order: 2,view: 'text', type: 'string',},
        timestamp: {title: '检查日期',order: 3,view: 'datetime', type: 'string',},
    }
  },
  medicationInfo: {
    title: '用药信息表',
    view: 'table',
    fields: {
        drugCode: {title: '通用药物代码',order: 0,view: 'list', type: 'string',},
        drugDoseCode: {title: '药物使用次剂量',order: 1,view: 'text', type: 'string',},
        drugFrequency: {title: '每日用药次数',order: 2,view: 'text', type: 'string',},
        drugDosageRouteCode: {title: '服药方法代码',order: 3,view: 'list', type: 'string',},
        drugBeginDate: {title: '开始服药日期',order: 4,view: 'datetime', type: 'string',},
        drugEndDate: {title: '结束服药日期',order: 5,view: 'datetime', type: 'string',},
        fk: {title: '外键',order: 6,view: 'text', type: 'string',},
    }
  },
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
// 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
