import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '报告卡id',
    align:"center",
    dataIndex: 'fieldPkFk'
   },
   {
    title: '随访状态（1随访2失访3查无此人）',
    align:"center",
    dataIndex: 'curfollstat'
   },
   {
    title: '随访次数',
    align:"center",
    dataIndex: 'flwno'
   },
   {
    title: '随访责任人',
    align:"center",
    dataIndex: 'inquirer'
   },
   {
    title: '随访执行单位',
    align:"center",
    dataIndex: 'inquirorg'
   },
   {
    title: '随访日期',
    align:"center",
    dataIndex: 'dtInquiry',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'memo'
   },
   {
    title: '活产( )个',
    align:"center",
    dataIndex: 'livebearing'
   },
   {
    title: '死产( )个',
    align:"center",
    dataIndex: 'deadbearing'
   },
   {
    title: '当前配偶/固定性伴的hiv感染状况',
    align:"center",
    dataIndex: 'matehiv'
   },
   {
    title: '子女hiv感染状况',
    align:"center",
    dataIndex: 'hchildstatus'
   },
   {
    title: '是否接受cd4检测',
    align:"center",
    dataIndex: 'cd4check'
   },
   {
    title: 'cd4检测结果',
    align:"center",
    dataIndex: 'cd4result'
   },
   {
    title: 'cd4检测日期',
    align:"center",
    dataIndex: 'dtCdcheck',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '上一次性调查到现在是否共用过注射器注射毒品',
    align:"center",
    dataIndex: 'iflasjinect'
   },
   {
    title: '上一次性调查到现在是否每次都用安全套',
    align:"center",
    dataIndex: 'iflascodm'
   },
   {
    title: '是否已死亡',
    align:"center",
    dataIndex: 'ifdead'
   },
   {
    title: '病程阶段',
    align:"center",
    dataIndex: 'coursedias'
   },
   {
    title: '艾滋病确诊日期',
    align:"center",
    dataIndex: 'dtDiagnose',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '现在是否为同伴教育员',
    align:"center",
    dataIndex: 'ifaccomTeacher'
   },
   {
    title: '过去3个月，是否每次发生性行为都用安全套',
    align:"center",
    dataIndex: 'ifcodmPthrmon'
   },
   {
    title: '最近3个月多少人与您有过性行为',
    align:"center",
    dataIndex: 'sexcntLthrmon'
   },
   {
    title: '过去3个月，是否共用过注射器注射毒品',
    align:"center",
    dataIndex: 'ifinjectPthrmon'
   },
   {
    title: '在最近3个月有多少人与您共用过注射器',
    align:"center",
    dataIndex: 'injectcntLthmon'
   },
   {
    title: '过去3个月，是否参加针具交换',
    align:"center",
    dataIndex: 'neeexhPthrmon'
   },
   {
    title: '在最近3个月交出针具/支',
    align:"center",
    dataIndex: 'surrendNeedle'
   },
   {
    title: ' 在最近3个月换回针具/支',
    align:"center",
    dataIndex: 'fetchNeedle'
   },
   {
    title: '过去6个月您或您的家庭是否获得支持',
    align:"center",
    dataIndex: 'flackConsult'
   },
   {
    title: '获得安全套/个',
    align:"center",
    dataIndex: 'condomNum'
   },
   {
    title: '获得宣传材料/份',
    align:"center",
    dataIndex: 'fetckmeterNum'
   },
   {
    title: '是否药物提供',
    align:"center",
    dataIndex: 'ifofferdrug'
   },
   {
    title: '是否关怀救助',
    align:"center",
    dataIndex: 'ifsalvate'
   },
   {
    title: '过去6个月是否接受过结核病检查',
    align:"center",
    dataIndex: 'iftbcheck'
   },
   {
    title: '结核病检查结果',
    align:"center",
    dataIndex: 'tbcheckResult'
   },
   {
    title: '抗病毒治疗编号',
    align:"center",
    dataIndex: 'acceptcureNo'
   },
   {
    title: '目前是否接受社区美沙酮维持治疗',
    align:"center",
    dataIndex: 'ifmstcrue'
   },
   {
    title: '社区美沙酮维持治疗编号',
    align:"center",
    dataIndex: 'mstcrueNo'
   },
   {
    title: '自上次随访以来，做过cd4+检测次数',
    align:"center",
    dataIndex: 'cd4checkTimes'
   },
   {
    title: 'idOther',
    align:"center",
    dataIndex: 'idOther'
   },
   {
    title: '目前是否接受抗病毒治疗',
    align:"center",
    dataIndex: 'acceptcure'
   },
   {
    title: '当前是否羁押',
    align:"center",
    dataIndex: 'detain'
   },
   {
    title: '失访原因',
    align:"center",
    dataIndex: 'lose'
   },
   {
    title: '若当前配偶/固定性伴感染状况为阳性,其卡片编号为',
    align:"center",
    dataIndex: 'matehivmas'
   },
   {
    title: '子女数',
    align:"center",
    dataIndex: 'childnum'
   },
   {
    title: '子女阳性',
    align:"center",
    dataIndex: 'childmas'
   },
   {
    title: '子女阴性',
    align:"center",
    dataIndex: 'childneg'
   },
   {
    title: '子女检测结果不确定',
    align:"center",
    dataIndex: 'childun'
   },
   {
    title: '子女未査/不详',
    align:"center",
    dataIndex: 'childnon'
   },
   {
    title: '若为育龄妇女目前为',
    align:"center",
    dataIndex: 'reproductive'
   },
   {
    title: '在孕期、产时、产后是否为预防母婴传播服用抗病毒治疗药物',
    align:"center",
    dataIndex: 'medicine'
   },
   {
    title: '若已检测，检测日期',
    align:"center",
    dataIndex: 'matehivcheck',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '咳嗽、咳痰持续2周以上',
    align:"center",
    dataIndex: 'tbstate1'
   },
   {
    title: 'cd4检测单位',
    align:"center",
    dataIndex: 'cd4checkorg'
   },
   {
    title: 'cd4检测地区',
    align:"center",
    dataIndex: 'cd4checkzone'
   },
   {
    title: '反复咳出的痰中带血',
    align:"center",
    dataIndex: 'tbstate2'
   },
   {
    title: '反复发热持续2周以上',
    align:"center",
    dataIndex: 'tbstate3'
   },
   {
    title: '夜间经常出汗',
    align:"center",
    dataIndex: 'tbstate4'
   },
   {
    title: '无法解释的体重明显下降',
    align:"center",
    dataIndex: 'tbstate5'
   },
   {
    title: '经常容易疲劳或呼吸短促',
    align:"center",
    dataIndex: 'tbstate6'
   },
   {
    title: '淋巴结肿大',
    align:"center",
    dataIndex: 'tbstate7'
   },
   {
    title: '备注有问题--待改',
    align:"center",
    dataIndex: 'questionpage'
   },
   {
    title: '现住址类型',
    align:"center",
    dataIndex: 'areatype'
   },
   {
    title: '现住地址国标',
    align:"center",
    dataIndex: 'addrcode'
   },
   {
    title: '详细地址',
    align:"center",
    dataIndex: 'addr'
   },
   {
    title: '报告地区',
    align:"center",
    dataIndex: 'apanagecode'
   },
   {
    title: '报告机构编码',
    align:"center",
    dataIndex: 'rptorgcode'
   },
   {
    title: '过去3个月，是否每次与配偶/固定性伴发生性行为时都用安全套',
    align:"center",
    dataIndex: 'condom'
   },
   {
    title: '自上次随访以来配偶/固定性伴变化情况',
    align:"center",
    dataIndex: 'zscpbh'
   },
   {
    title: 'flowingout',
    align:"center",
    dataIndex: 'flowingout'
   },
   {
    title: 'outaddress',
    align:"center",
    dataIndex: 'outaddress'
   },
   {
    title: 'dowork',
    align:"center",
    dataIndex: 'dowork'
   },
   {
    title: 'outtime',
    align:"center",
    dataIndex: 'outtime'
   },
   {
    title: 'gocities',
    align:"center",
    dataIndex: 'gocities'
   },
   {
    title: 'ifgoout',
    align:"center",
    dataIndex: 'ifgoout'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '报告卡id',
    field: 'fieldPkFk',
    component: 'Input',
  },
  {
    label: '随访状态（1随访2失访3查无此人）',
    field: 'curfollstat',
    component: 'Input',
  },
  {
    label: '随访次数',
    field: 'flwno',
    component: 'InputNumber',
  },
  {
    label: '随访责任人',
    field: 'inquirer',
    component: 'Input',
  },
  {
    label: '随访执行单位',
    field: 'inquirorg',
    component: 'Input',
  },
  {
    label: '随访日期',
    field: 'dtInquiry',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '备注',
    field: 'memo',
    component: 'Input',
  },
  {
    label: '活产( )个',
    field: 'livebearing',
    component: 'Input',
  },
  {
    label: '死产( )个',
    field: 'deadbearing',
    component: 'Input',
  },
  {
    label: '当前配偶/固定性伴的hiv感染状况',
    field: 'matehiv',
    component: 'Input',
  },
  {
    label: '子女hiv感染状况',
    field: 'hchildstatus',
    component: 'Input',
  },
  {
    label: '是否接受cd4检测',
    field: 'cd4check',
    component: 'Input',
  },
  {
    label: 'cd4检测结果',
    field: 'cd4result',
    component: 'Input',
  },
  {
    label: 'cd4检测日期',
    field: 'dtCdcheck',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '上一次性调查到现在是否共用过注射器注射毒品',
    field: 'iflasjinect',
    component: 'Input',
  },
  {
    label: '上一次性调查到现在是否每次都用安全套',
    field: 'iflascodm',
    component: 'Input',
  },
  {
    label: '是否已死亡',
    field: 'ifdead',
    component: 'Input',
  },
  {
    label: '病程阶段',
    field: 'coursedias',
    component: 'Input',
  },
  {
    label: '艾滋病确诊日期',
    field: 'dtDiagnose',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '现在是否为同伴教育员',
    field: 'ifaccomTeacher',
    component: 'Input',
  },
  {
    label: '过去3个月，是否每次发生性行为都用安全套',
    field: 'ifcodmPthrmon',
    component: 'Input',
  },
  {
    label: '最近3个月多少人与您有过性行为',
    field: 'sexcntLthrmon',
    component: 'InputNumber',
  },
  {
    label: '过去3个月，是否共用过注射器注射毒品',
    field: 'ifinjectPthrmon',
    component: 'Input',
  },
  {
    label: '在最近3个月有多少人与您共用过注射器',
    field: 'injectcntLthmon',
    component: 'InputNumber',
  },
  {
    label: '过去3个月，是否参加针具交换',
    field: 'neeexhPthrmon',
    component: 'Input',
  },
  {
    label: '在最近3个月交出针具/支',
    field: 'surrendNeedle',
    component: 'InputNumber',
  },
  {
    label: ' 在最近3个月换回针具/支',
    field: 'fetchNeedle',
    component: 'InputNumber',
  },
  {
    label: '过去6个月您或您的家庭是否获得支持',
    field: 'flackConsult',
    component: 'Input',
  },
  {
    label: '获得安全套/个',
    field: 'condomNum',
    component: 'InputNumber',
  },
  {
    label: '获得宣传材料/份',
    field: 'fetckmeterNum',
    component: 'InputNumber',
  },
  {
    label: '是否药物提供',
    field: 'ifofferdrug',
    component: 'Input',
  },
  {
    label: '是否关怀救助',
    field: 'ifsalvate',
    component: 'Input',
  },
  {
    label: '过去6个月是否接受过结核病检查',
    field: 'iftbcheck',
    component: 'Input',
  },
  {
    label: '结核病检查结果',
    field: 'tbcheckResult',
    component: 'Input',
  },
  {
    label: '抗病毒治疗编号',
    field: 'acceptcureNo',
    component: 'Input',
  },
  {
    label: '目前是否接受社区美沙酮维持治疗',
    field: 'ifmstcrue',
    component: 'Input',
  },
  {
    label: '社区美沙酮维持治疗编号',
    field: 'mstcrueNo',
    component: 'Input',
  },
  {
    label: '自上次随访以来，做过cd4+检测次数',
    field: 'cd4checkTimes',
    component: 'InputNumber',
  },
  {
    label: 'idOther',
    field: 'idOther',
    component: 'Input',
  },
  {
    label: '目前是否接受抗病毒治疗',
    field: 'acceptcure',
    component: 'Input',
  },
  {
    label: '当前是否羁押',
    field: 'detain',
    component: 'Input',
  },
  {
    label: '失访原因',
    field: 'lose',
    component: 'Input',
  },
  {
    label: '若当前配偶/固定性伴感染状况为阳性,其卡片编号为',
    field: 'matehivmas',
    component: 'Input',
  },
  {
    label: '子女数',
    field: 'childnum',
    component: 'InputNumber',
  },
  {
    label: '子女阳性',
    field: 'childmas',
    component: 'InputNumber',
  },
  {
    label: '子女阴性',
    field: 'childneg',
    component: 'InputNumber',
  },
  {
    label: '子女检测结果不确定',
    field: 'childun',
    component: 'InputNumber',
  },
  {
    label: '子女未査/不详',
    field: 'childnon',
    component: 'InputNumber',
  },
  {
    label: '若为育龄妇女目前为',
    field: 'reproductive',
    component: 'Input',
  },
  {
    label: '在孕期、产时、产后是否为预防母婴传播服用抗病毒治疗药物',
    field: 'medicine',
    component: 'Input',
  },
  {
    label: '若已检测，检测日期',
    field: 'matehivcheck',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '咳嗽、咳痰持续2周以上',
    field: 'tbstate1',
    component: 'Input',
  },
  {
    label: 'cd4检测单位',
    field: 'cd4checkorg',
    component: 'Input',
  },
  {
    label: 'cd4检测地区',
    field: 'cd4checkzone',
    component: 'Input',
  },
  {
    label: '反复咳出的痰中带血',
    field: 'tbstate2',
    component: 'Input',
  },
  {
    label: '反复发热持续2周以上',
    field: 'tbstate3',
    component: 'Input',
  },
  {
    label: '夜间经常出汗',
    field: 'tbstate4',
    component: 'Input',
  },
  {
    label: '无法解释的体重明显下降',
    field: 'tbstate5',
    component: 'Input',
  },
  {
    label: '经常容易疲劳或呼吸短促',
    field: 'tbstate6',
    component: 'Input',
  },
  {
    label: '淋巴结肿大',
    field: 'tbstate7',
    component: 'Input',
  },
  {
    label: '备注有问题--待改',
    field: 'questionpage',
    component: 'Input',
  },
  {
    label: '现住址类型',
    field: 'areatype',
    component: 'Input',
  },
  {
    label: '现住地址国标',
    field: 'addrcode',
    component: 'Input',
  },
  {
    label: '详细地址',
    field: 'addr',
    component: 'Input',
  },
  {
    label: '报告地区',
    field: 'apanagecode',
    component: 'Input',
  },
  {
    label: '报告机构编码',
    field: 'rptorgcode',
    component: 'Input',
  },
  {
    label: '过去3个月，是否每次与配偶/固定性伴发生性行为时都用安全套',
    field: 'condom',
    component: 'Input',
  },
  {
    label: '自上次随访以来配偶/固定性伴变化情况',
    field: 'zscpbh',
    component: 'Input',
  },
  {
    label: 'flowingout',
    field: 'flowingout',
    component: 'Input',
  },
  {
    label: 'outaddress',
    field: 'outaddress',
    component: 'Input',
  },
  {
    label: 'dowork',
    field: 'dowork',
    component: 'Input',
  },
  {
    label: 'outtime',
    field: 'outtime',
    component: 'Input',
  },
  {
    label: 'gocities',
    field: 'gocities',
    component: 'Input',
  },
  {
    label: 'ifgoout',
    field: 'ifgoout',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  fieldPkFk: {title: '报告卡id',order: 0,view: 'text', type: 'string',},
  curfollstat: {title: '随访状态（1随访2失访3查无此人）',order: 1,view: 'text', type: 'string',},
  flwno: {title: '随访次数',order: 2,view: 'number', type: 'number',},
  inquirer: {title: '随访责任人',order: 3,view: 'text', type: 'string',},
  inquirorg: {title: '随访执行单位',order: 4,view: 'text', type: 'string',},
  dtInquiry: {title: '随访日期',order: 5,view: 'date', type: 'string',},
  memo: {title: '备注',order: 6,view: 'text', type: 'string',},
  livebearing: {title: '活产( )个',order: 7,view: 'text', type: 'string',},
  deadbearing: {title: '死产( )个',order: 8,view: 'text', type: 'string',},
  matehiv: {title: '当前配偶/固定性伴的hiv感染状况',order: 9,view: 'text', type: 'string',},
  hchildstatus: {title: '子女hiv感染状况',order: 10,view: 'text', type: 'string',},
  cd4check: {title: '是否接受cd4检测',order: 11,view: 'text', type: 'string',},
  cd4result: {title: 'cd4检测结果',order: 12,view: 'text', type: 'string',},
  dtCdcheck: {title: 'cd4检测日期',order: 13,view: 'date', type: 'string',},
  iflasjinect: {title: '上一次性调查到现在是否共用过注射器注射毒品',order: 14,view: 'text', type: 'string',},
  iflascodm: {title: '上一次性调查到现在是否每次都用安全套',order: 15,view: 'text', type: 'string',},
  ifdead: {title: '是否已死亡',order: 16,view: 'text', type: 'string',},
  coursedias: {title: '病程阶段',order: 17,view: 'text', type: 'string',},
  dtDiagnose: {title: '艾滋病确诊日期',order: 18,view: 'date', type: 'string',},
  ifaccomTeacher: {title: '现在是否为同伴教育员',order: 19,view: 'text', type: 'string',},
  ifcodmPthrmon: {title: '过去3个月，是否每次发生性行为都用安全套',order: 20,view: 'text', type: 'string',},
  sexcntLthrmon: {title: '最近3个月多少人与您有过性行为',order: 21,view: 'number', type: 'number',},
  ifinjectPthrmon: {title: '过去3个月，是否共用过注射器注射毒品',order: 22,view: 'text', type: 'string',},
  injectcntLthmon: {title: '在最近3个月有多少人与您共用过注射器',order: 23,view: 'number', type: 'number',},
  neeexhPthrmon: {title: '过去3个月，是否参加针具交换',order: 24,view: 'text', type: 'string',},
  surrendNeedle: {title: '在最近3个月交出针具/支',order: 25,view: 'number', type: 'number',},
  fetchNeedle: {title: ' 在最近3个月换回针具/支',order: 26,view: 'number', type: 'number',},
  flackConsult: {title: '过去6个月您或您的家庭是否获得支持',order: 27,view: 'text', type: 'string',},
  condomNum: {title: '获得安全套/个',order: 28,view: 'number', type: 'number',},
  fetckmeterNum: {title: '获得宣传材料/份',order: 29,view: 'number', type: 'number',},
  ifofferdrug: {title: '是否药物提供',order: 30,view: 'text', type: 'string',},
  ifsalvate: {title: '是否关怀救助',order: 31,view: 'text', type: 'string',},
  iftbcheck: {title: '过去6个月是否接受过结核病检查',order: 32,view: 'text', type: 'string',},
  tbcheckResult: {title: '结核病检查结果',order: 33,view: 'text', type: 'string',},
  acceptcureNo: {title: '抗病毒治疗编号',order: 34,view: 'text', type: 'string',},
  ifmstcrue: {title: '目前是否接受社区美沙酮维持治疗',order: 35,view: 'text', type: 'string',},
  mstcrueNo: {title: '社区美沙酮维持治疗编号',order: 36,view: 'text', type: 'string',},
  cd4checkTimes: {title: '自上次随访以来，做过cd4+检测次数',order: 37,view: 'number', type: 'number',},
  idOther: {title: 'idOther',order: 38,view: 'text', type: 'string',},
  acceptcure: {title: '目前是否接受抗病毒治疗',order: 39,view: 'text', type: 'string',},
  detain: {title: '当前是否羁押',order: 40,view: 'text', type: 'string',},
  lose: {title: '失访原因',order: 41,view: 'text', type: 'string',},
  matehivmas: {title: '若当前配偶/固定性伴感染状况为阳性,其卡片编号为',order: 42,view: 'text', type: 'string',},
  childnum: {title: '子女数',order: 43,view: 'number', type: 'number',},
  childmas: {title: '子女阳性',order: 44,view: 'number', type: 'number',},
  childneg: {title: '子女阴性',order: 45,view: 'number', type: 'number',},
  childun: {title: '子女检测结果不确定',order: 46,view: 'number', type: 'number',},
  childnon: {title: '子女未査/不详',order: 47,view: 'number', type: 'number',},
  reproductive: {title: '若为育龄妇女目前为',order: 48,view: 'text', type: 'string',},
  medicine: {title: '在孕期、产时、产后是否为预防母婴传播服用抗病毒治疗药物',order: 49,view: 'text', type: 'string',},
  matehivcheck: {title: '若已检测，检测日期',order: 50,view: 'date', type: 'string',},
  tbstate1: {title: '咳嗽、咳痰持续2周以上',order: 51,view: 'text', type: 'string',},
  cd4checkorg: {title: 'cd4检测单位',order: 52,view: 'text', type: 'string',},
  cd4checkzone: {title: 'cd4检测地区',order: 53,view: 'text', type: 'string',},
  tbstate2: {title: '反复咳出的痰中带血',order: 54,view: 'text', type: 'string',},
  tbstate3: {title: '反复发热持续2周以上',order: 55,view: 'text', type: 'string',},
  tbstate4: {title: '夜间经常出汗',order: 56,view: 'text', type: 'string',},
  tbstate5: {title: '无法解释的体重明显下降',order: 57,view: 'text', type: 'string',},
  tbstate6: {title: '经常容易疲劳或呼吸短促',order: 58,view: 'text', type: 'string',},
  tbstate7: {title: '淋巴结肿大',order: 59,view: 'text', type: 'string',},
  questionpage: {title: '备注有问题--待改',order: 60,view: 'text', type: 'string',},
  areatype: {title: '现住址类型',order: 61,view: 'text', type: 'string',},
  addrcode: {title: '现住地址国标',order: 62,view: 'text', type: 'string',},
  addr: {title: '详细地址',order: 63,view: 'text', type: 'string',},
  apanagecode: {title: '报告地区',order: 64,view: 'text', type: 'string',},
  rptorgcode: {title: '报告机构编码',order: 65,view: 'text', type: 'string',},
  condom: {title: '过去3个月，是否每次与配偶/固定性伴发生性行为时都用安全套',order: 66,view: 'text', type: 'string',},
  zscpbh: {title: '自上次随访以来配偶/固定性伴变化情况',order: 67,view: 'text', type: 'string',},
  flowingout: {title: 'flowingout',order: 68,view: 'text', type: 'string',},
  outaddress: {title: 'outaddress',order: 69,view: 'text', type: 'string',},
  dowork: {title: 'dowork',order: 70,view: 'text', type: 'string',},
  outtime: {title: 'outtime',order: 71,view: 'text', type: 'string',},
  gocities: {title: 'gocities',order: 72,view: 'text', type: 'string',},
  ifgoout: {title: 'ifgoout',order: 73,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}