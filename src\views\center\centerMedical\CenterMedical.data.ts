import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '患者姓名',
    align: "center",
    dataIndex: 'patientname'
  },
  {
    title: '患者年龄',
    align: "center",
    dataIndex: 'patientage'
  },
  {
    title: '患者性别',
    align: "center",
    dataIndex: 'sex_dictText'
  },
  {
    title: '病历类型',
    align: "center",
    dataIndex: 'medicalrecordtype_dictText'
  },
  {
    title: '诊断结果',
    align: "center",
    dataIndex: 'diagnosisresult'
  },
  {
    title: '监测时间',
    align: "center",
    dataIndex: 'monitoringtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '监测指标',
    align: "center",
    dataIndex: 'monitoringindicator'
  },
  {
    title: '监测值',
    align: "center",
    dataIndex: 'monitoringvalue'
  },
  {
    title: '医生姓名',
    align: "center",
    dataIndex: 'doctorname'
  },
  {
    title: '报告生成权限',
    align: "center",
    dataIndex: 'npermission'
  },
  {
    title: '状态',
    align: "center",
    dataIndex: 'states_dictText'
  },
  {
    title: '附件',
    align: "center",
    dataIndex: 'file',
  },
    {
    title: '批量报告生成',
    align: "center",
    dataIndex: 'generation_dictText'
  },
];

// 高级查询数据
export const superQuerySchema = {
  patientname: {title: '患者姓名',order: 0,view: 'text', type: 'string',},
  patientage: {title: '患者年龄',order: 1,view: 'text', type: 'string',},
  sex: {title: '患者性别',order: 2,view: 'list', type: 'string',dictCode: 'aids_sex',},
  medicalrecordtype: {title: '病历类型',order: 3,view: 'list', type: 'string',dictCode: 'RECORD_TYPE',},
  diagnosisresult: {title: '诊断结果',order: 4,view: 'text', type: 'string',},
  monitoringtime: {title: '监测时间',order: 5,view: 'date', type: 'string',},
  monitoringindicator: {title: '监测指标',order: 6,view: 'text', type: 'string',},
  monitoringvalue: {title: '监测值',order: 7,view: 'text', type: 'string',},
  doctorname: {title: '医生姓名',order: 8,view: 'text', type: 'string',},
  npermission: {title: '报告生成权限',order: 9,view: 'text', type: 'string',},
  states: {title: '状态',order: 10,view: 'list', type: 'string',dictCode: 'STATES',},
  file: {title: '附件',order: 11,view: 'file', type: 'string',},
  generation: {title: '批量报告生成',order: 12,view: 'list', type: 'string',dictCode: 'RATION',},
};
