import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '监测点名称',
    align:"center",
    dataIndex: 'sentinelName'
   },
   {
    title: '监测点编号',
    align:"center",
    dataIndex: 'sentinelNumber'
   },
   {
    title: '监测时间',
    align:"center",
    dataIndex: 'monitoringTime'
   },
   {
    title: '收集样本数量',
    align:"center",
    dataIndex: 'sampleQuantity'
   },
   {
    title: 'HIV阳性样本数量',
    align:"center",
    dataIndex: 'hivQuantity'
   },
   {
    title: 'HIV阳性率',
    align:"center",
    dataIndex: 'hivRate'
   },
   {
    title: '监测对象类型',
    align:"center",
    dataIndex: 'targetType'
   },
   {
    title: '数据收集方法',
    align:"center",
    dataIndex: 'collectionMethod'
   },
   {
    title: '统计分析日期',
    align:"center",
    dataIndex: 'analysisDate'
   },
   {
    title: '统计分析结果',
    align:"center",
    dataIndex: 'analysisResults'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'remarks'
   },
   {
    title: '监测地点',
    align:"center",
    dataIndex: 'monitoringLocation'
   },
   {
    title: '负责人姓名',
    align:"center",
    dataIndex: 'personName'
   },
   {
    title: '负责人联系方式',
    align:"center",
    dataIndex: 'personContact'
   },
   {
    title: '数据质量控制',
    align:"center",
    dataIndex: 'qualityControl'
   },
   {
    title: '反馈机制',
    align:"center",
    dataIndex: 'feedbackMechanism'
   },
   {
    title: '外键',
    align:"center",
    dataIndex: 'pid'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '监测点名称',
    field: 'sentinelName',
    component: 'Input',
  },
  {
    label: '监测点编号',
    field: 'sentinelNumber',
    component: 'Input',
  },
  {
    label: '监测时间',
    field: 'monitoringTime',
    component: 'Input',
  },
  {
    label: '收集样本数量',
    field: 'sampleQuantity',
    component: 'Input',
  },
  {
    label: 'HIV阳性样本数量',
    field: 'hivQuantity',
    component: 'Input',
  },
  {
    label: 'HIV阳性率',
    field: 'hivRate',
    component: 'Input',
  },
  {
    label: '监测对象类型',
    field: 'targetType',
    component: 'Input',
  },
  {
    label: '数据收集方法',
    field: 'collectionMethod',
    component: 'Input',
  },
  {
    label: '统计分析日期',
    field: 'analysisDate',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '统计分析结果',
    field: 'analysisResults',
    component: 'Input',
  },
  {
    label: '备注',
    field: 'remarks',
    component: 'Input',
  },
  {
    label: '监测地点',
    field: 'monitoringLocation',
    component: 'Input',
  },
  {
    label: '负责人姓名',
    field: 'personName',
    component: 'Input',
  },
  {
    label: '负责人联系方式',
    field: 'personContact',
    component: 'Input',
  },
  {
    label: '数据质量控制',
    field: 'qualityControl',
    component: 'Input',
  },
  {
    label: '反馈机制',
    field: 'feedbackMechanism',
    component: 'Input',
  },
  {
    label: '外键',
    field: 'pid',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  sentinelName: {title: '监测点名称',order: 0,view: 'text', type: 'string',},
  sentinelNumber: {title: '监测点编号',order: 1,view: 'text', type: 'string',},
  monitoringTime: {title: '监测时间',order: 2,view: 'text', type: 'string',},
  sampleQuantity: {title: '收集样本数量',order: 3,view: 'text', type: 'string',},
  hivQuantity: {title: 'HIV阳性样本数量',order: 4,view: 'text', type: 'string',},
  hivRate: {title: 'HIV阳性率',order: 5,view: 'text', type: 'string',},
  targetType: {title: '监测对象类型',order: 6,view: 'text', type: 'string',},
  collectionMethod: {title: '数据收集方法',order: 7,view: 'text', type: 'string',},
  analysisDate: {title: '统计分析日期',order: 8,view: 'datetime', type: 'string',},
  analysisResults: {title: '统计分析结果',order: 9,view: 'text', type: 'string',},
  remarks: {title: '备注',order: 10,view: 'text', type: 'string',},
  monitoringLocation: {title: '监测地点',order: 11,view: 'text', type: 'string',},
  personName: {title: '负责人姓名',order: 12,view: 'text', type: 'string',},
  personContact: {title: '负责人联系方式',order: 13,view: 'text', type: 'string',},
  qualityControl: {title: '数据质量控制',order: 14,view: 'text', type: 'string',},
  feedbackMechanism: {title: '反馈机制',order: 15,view: 'text', type: 'string',},
  pid: {title: '外键',order: 16,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}