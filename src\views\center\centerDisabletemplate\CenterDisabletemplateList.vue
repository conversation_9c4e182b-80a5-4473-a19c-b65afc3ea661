<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="6">
            <a-form-item name="templatename">
              <template #label><span title="模板名称">模板名称</span></template>
              <a-input placeholder="请输入模板名称" v-model:value="queryParam.templatename" allow-clear ></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item name="state">
              <template #label><span title="模板状态">模板状态</span></template>
              <j-dict-select-tag placeholder="请选择模板状态" v-model:value="queryParam.state" dictCode="TEMPLATE__STATE" allow-clear />
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :lg="6">
              <a-form-item name="operationtype">
                <template #label><span title="操作类型">操作类型</span></template>
                <a-input placeholder="请输入操作类型" v-model:value="queryParam.operationtype" allow-clear ></a-input>
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <!-- <a-button type="primary" @click="suBmit" preIcon="ant-design:plus-outlined"> 启用</a-button>
        <a-button type="primary" @click="revIew" preIcon="ant-design:plus-outlined"> 禁用</a-button> -->

       <a-button type="primary" @click="suBmit" preIcon="ant-design:check-circle-outlined" style="background-color: #52c41a; border-color: #52c41a;">启用</a-button>
       <a-button type="primary" @click="revIew" preIcon="ant-design:close-circle-outlined" style="background-color: #ff4d4f; border-color: #ff4d4f;">禁用</a-button>
    



        <a-button  type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>
      <template v-slot:bodyCell="{ column, record, index, text }">
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <CenterDisabletemplateModal ref="registerModal" @success="handleSuccess"></CenterDisabletemplateModal>
  </div>
</template>

<script lang="ts" name="centerDisabletemplate-centerDisabletemplate" setup>
  import { message } from 'ant-design-vue'; 
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './CenterDisabletemplate.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl ,pushAotification ,pushAotificationsh} from './CenterDisabletemplate.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import CenterDisabletemplateModal from './components/CenterDisabletemplateModal.vue'
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';

  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '禁用、启用监测报告模板',
      api: list,
      columns,
      canResize:false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: "禁用、启用监测报告模板",
      url: getExportUrl,
      params: queryParam,
    },
	  importConfig: {
	    url: getImportUrl,
	    success: handleSuccess
	  },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;
  const labelCol = reactive({
    xs:24,
    sm:4,
    xl:6,
    xxl:4
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }
  
  /**
   * 编辑事件
   */
  function handleEdit(record: Recordable) {
    registerModal.value.disableSubmit = false;
    registerModal.value.edit(record);
  }
   
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }
   
  /**
   * 删除事件
   */
  async function handleDelete(record) {
    await deleteOne({ id: record.id }, handleSuccess);
  }
   
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }
   
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
   
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
    ];
  }
   
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }, {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        }
      }
    ]
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }
  
  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
  


/**
 * 启用
 */
function suBmit() {
  
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择一条数据进行启用');
    return;
  }
  
  if (selectedRowKeys.value.length > 1) {
    message.warning('只能选择一条数据进行启用');
    return;
  }
  const id = selectedRowKeys.value[0];
     // 检查推送状态
  const dataSource = getDataSource();
  const record = dataSource.find(item => item.id === id);
  

   if (record && record.states === '1') {
   message.warning('已启用的数据不能再次启用');
    return;
   }

  console.log('当前选中的ID:', id);
  pushAotification(id)
    .then(res => {
      // 根据后端实际返回结构判断  后端成功的代码要对应
      if (res  === '启用成功！') {
        handleSuccess();
      }
    })
    .catch(error => {
      console.error('启用失败：', error);
      message.error('启用请求异常，请稍后重试');
    });
}





//启用
function revIew() {
  
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择一条数据进行禁用');
    return;
  }
  
  if (selectedRowKeys.value.length > 1) {
    message.warning('只能选择一条数据进行禁用');
    return;
  }
  const id = selectedRowKeys.value[0];
     // 检查推送状态
  const dataSource = getDataSource();
  const record = dataSource.find(item => item.id === id);
   if (record && record.states === '2') {
   message.warning('已禁用的数据不能再次仅禁用');
    return;
   }

  


  console.log('当前选中的ID:', id);
  pushAotificationsh(id)
    .then(res => {
      // 根据后端实际返回结构判断  后端成功的代码要对应
      if (res  === '禁用成功！') {
        handleSuccess();
      }
    })
    .catch(error => {
      console.error('禁用失败：', error);
      message.error('禁用请求异常，请稍后重试');
    });
}



</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust{
      min-width: 100px !important;
    }
    .query-group-split-cust{
      width: 30px;
      display: inline-block;
      text-align: center
    }
    .ant-form-item:not(.ant-form-item-with-help){
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),:deep(.ant-input-number){
      width: 100%;
    }
  }
</style>
