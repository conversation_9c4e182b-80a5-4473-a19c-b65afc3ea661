import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '卡片编号',
    align:"center",
    dataIndex: 'cardNum'
   },
   {
    title: '治疗机构名称',
    align:"center",
    dataIndex: 'orgName',
   },
   {
    title: '患者姓名',
    align:"center",
    dataIndex: 'patientName'
   },
   {
    title: '有效证件号',
    align:"center",
    dataIndex: 'idCard'
   },
   {
    title: '随访日期',
    align:"center",
    dataIndex: 'followUpTime'
   },
   {
    title: '丙肝核酸检测结果',
    align:"center",
    dataIndex: 'testResult_dictText',
   },
   {
    title: '谷丙转氨酶（ALT）',
    align:"center",
    dataIndex: 'alt'
   },
   {
    title: '谷丙转氨酶正常值上限',
    align:"center",
    dataIndex: 'altUpLimit'
   },
   {
    title: '谷草转氨酶（AST）',
    align:"center",
    dataIndex: 'ast'
   },
   {
    title: '谷草转氨酶正常值上限',
    align:"center",
    dataIndex: 'astUpLimit'
   },
   {
    title: '血小板(PLT)',
    align:"center",
    dataIndex: 'plt'
   },
   {
    title: '临床处置和状态',
    align:"center",
    dataIndex: 'clinicalProcessStatus_dictText',
   },
   {
    title: '服药结束日期',
    align:"center",
    dataIndex: 'medicationEndDate'
   },
   {
    title: '随访医生',
    align:"center",
    dataIndex: 'followUpDoctor'
   },
   {
    title: '填表日期',
    align:"center",
    dataIndex: 'fillFormDate'
   }
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
   label: '卡片编号',
   field: 'cardNum',
   component: 'Input'
  },
  {
   label: '治疗机构名称',
   field: 'orgName',
   component:'Orgcode',
   componentProps:{
     orgType:'A,J'
   }  
  },
  {
    label: '患者姓名',
    field: 'patientName',
    component: 'Input',
  },
  {
    label: '有效证件号',
    field: 'idCard',
    component: 'Input',
  },
  {
    label: '随访日期',
    field: 'followUpTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
   label: '丙肝核酸检测结果',
   field: 'testResult',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_testResult",
       type: "radio"
    },
  },
  {
   label: '丙肝核酸采血日期',
   field: 'bloodCollectDate',
   component: 'DatePicker',
   componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    },
  },
  {
   label: '是否进行生化检查',
   field: 'ifBioTest',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_shifou",
       type: "radio"
    },
  },
  {
   label: '生化检查采血日期',
   field: 'bioTestDate',
   component: 'DatePicker',
   componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    },
  },
  {
   label: '谷丙转氨酶（ALT）',
   field: 'alt',
   component: 'Input'
  },
  {
   label: '谷丙转氨酶正常值上限',
   field: 'altUpLimit',
   component: 'Input'
  },
  {
   label: '谷草转氨酶（AST）',
   field: 'ast',
   component: 'Input'
  },
  {
   label: '谷草转氨酶正常值上限',
   field: 'astUpLimit',
   component: 'Input'
  },
  {
   label: '是否检查血小板',
   field: 'ifCheckPlatelets',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_shifou",
       type: "radio"
    },
  },
  {
   label: '血小板(PLT)',
   field: 'plt',
   component: 'Input'
  },
  {
   label: '血小板采血日期',
   field: 'pltBloodColDate',
   component: 'DatePicker',
   componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    },
  },
  {
   label: '是否检查甲胎蛋白',
   field: 'ifCheckAfp',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_shifou",
       type: "radio"
    },
  },
  {
   label: '临床处置和状态',
   field: 'clinicalProcessStatus',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_clinicalProcessStatus",
       type: "radio"
    },
  },
  {
   label: '服药结束日期',
   field: 'medicationEndDate',
   component: 'DatePicker',
   componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    },
  },
  {
   label: '随访医生',
   field: 'followUpDoctor',
   component: 'Input'
  },
  {
   label: '填表日期',
   field: 'fillFormDate',
   component: 'DatePicker',
   componentProps: {
      showTime: true,
      valueFormat: 'YYYY-MM-DD HH:mm:ss'
    },
  },
  {
   label: '备注',
   field: 'note',
   component: 'Input'
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  cardNum: {title: '卡片编号',order: 0,view: 'text', type: 'string',},
  orgName: {title: '治疗机构名称',order: 1,view: 'text', type: 'string',},
  patientName: {title: '患者姓名',order: 2,view: 'text', type: 'string',},
  idCard: {title: '有效证件号',order: 3,view: 'text', type: 'string',},
  followUpTime: {title: '随访日期',order: 4,view: 'datetime', type: 'string',},
  testResult: {title: '丙肝核酸检测结果',order: 5,view: 'radio', type: 'string',dictCode: 'aids_testResult',},
  ALT: {title: '谷丙转氨酶（ALT）',order: 6,view: 'text', type: 'string',},
  AST: {title: '谷草转氨酶（AST）',order: 7,view: 'text', type: 'string',},
  PLT: {title: '血小板(PLT)',order: 8,view: 'text', type: 'string',},
  clinicalProcessStatus: {title: '临床处置和状态',order: 9,view: 'radio', type: 'string',dictCode:'aids_clinicalProcessStatus',},
  medicationEndDate: {title: '服药结束日期',order: 10,view: 'datetime', type: 'string',},
  followUpDoctor: {title: '随访医生',order: 11,view: 'text', type: 'string',},
  fillFormDate: {title: '填表日期',order: 12,view: 'datetime', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
