-- 注意：该页面对应的前台目录为views/rptSuspectedcard文件夹下
-- 如果你想更改到其他目录，请修改sql中component字段对应的值


INSERT INTO sys_permission(id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external) 
VALUES ('2024101206071210220', NULL, '初筛数据管理', '/rptSuspectedcard/infectioncardSuspectedList', 'rptSuspectedcard/InfectioncardSuspectedList', NULL, NULL, 0, NULL, '1', 0.00, 0, NULL, 1, int_to_bool(0), 0, 0, 0, NULL, '1', 0, 0, 'admin', '2024-10-12 18:07:22', NULL, NULL, 0);

-- 权限控制sql
-- 新增
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101206071210221', '2024101206071210220', '添加初筛数据管理', NULL, NULL, 0, NULL, NULL, 2, 'rptSuspectedcard:infectioncard_suspected:add', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-12 18:07:22', NULL, NULL, 0, 0, '1', 0);
-- 编辑
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101206071210222', '2024101206071210220', '编辑初筛数据管理', NULL, NULL, 0, NULL, NULL, 2, 'rptSuspectedcard:infectioncard_suspected:edit', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-12 18:07:22', NULL, NULL, 0, 0, '1', 0);
-- 删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101206071210223', '2024101206071210220', '删除初筛数据管理', NULL, NULL, 0, NULL, NULL, 2, 'rptSuspectedcard:infectioncard_suspected:delete', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-12 18:07:22', NULL, NULL, 0, 0, '1', 0);
-- 批量删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101206071210224', '2024101206071210220', '批量删除初筛数据管理', NULL, NULL, 0, NULL, NULL, 2, 'rptSuspectedcard:infectioncard_suspected:deleteBatch', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-12 18:07:22', NULL, NULL, 0, 0, '1', 0);
-- 导出excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101206071210225', '2024101206071210220', '导出excel_初筛数据管理', NULL, NULL, 0, NULL, NULL, 2, 'rptSuspectedcard:infectioncard_suspected:exportXls', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-12 18:07:22', NULL, NULL, 0, 0, '1', 0);
-- 导入excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101206071210226', '2024101206071210220', '导入excel_初筛数据管理', NULL, NULL, 0, NULL, NULL, 2, 'rptSuspectedcard:infectioncard_suspected:importExcel', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-12 18:07:22', NULL, NULL, 0, 0, '1', 0);