<template>
  <div class="p-2">
    <!--查询区域-->
    <div class="jeecg-basic-table-form-container">
      <a-form ref="formRef" @keyup.enter.native="searchQuery" :model="queryParam" :label-col="labelCol" :wrapper-col="wrapperCol">
        <a-row :gutter="24">
          <a-col :lg="6">
            <a-form-item name="patientname">
              <template #label><span title="患者姓名">患者姓名</span></template>
              <a-input placeholder="请输入患者姓名" v-model:value="queryParam.patientname" allow-clear ></a-input>
            </a-form-item>
          </a-col>
          <a-col :lg="6">
            <a-form-item name="patientage">
              <template #label><span title="患者年龄">患者年龄</span></template>
              <a-input placeholder="请输入患者年龄" v-model:value="queryParam.patientage" allow-clear ></a-input>
            </a-form-item>
          </a-col>
          <template v-if="toggleSearchStatus">
            <a-col :lg="6">
              <a-form-item name="sex">
                <template #label><span title="患者性别">患者性别</span></template>
                <j-dict-select-tag placeholder="请选择患者性别" v-model:value="queryParam.sex" dictCode="aids_sex" allow-clear />
              </a-form-item>
            </a-col>
          </template>
          <a-col :xl="6" :lg="7" :md="8" :sm="24">
            <span style="float: left; overflow: hidden" class="table-page-search-submitButtons">
              <a-col :lg="6">
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery">查询</a-button>
                <a-button type="primary" preIcon="ant-design:reload-outlined" @click="searchReset" style="margin-left: 8px">重置</a-button>
                <a @click="toggleSearchStatus = !toggleSearchStatus" style="margin-left: 8px">
                  {{ toggleSearchStatus ? '收起' : '展开' }}
                  <Icon :icon="toggleSearchStatus ? 'ant-design:up-outlined' : 'ant-design:down-outlined'" />
                </a>
              </a-col>
            </span>
          </a-col>
        </a-row>
      </a-form>
    </div>
    <!--引用表格-->
    <BasicTable @register="registerTable" :rowSelection="rowSelection">
      <!--插槽:table标题-->
      <template #tableTitle>
        <a-button type="primary" @click="handleAdd" preIcon="ant-design:plus-outlined"> 新增</a-button>
        <a-button type="primary" @click="suBmit" preIcon="ant-design:check-circle-outlined" style="background-color: #52c41a; border-color: #52c41a;">提交</a-button>
        <a-button type="primary" @click="revIew" preIcon="ant-design:eye-outlined" style="background-color: #1890ff; border-color: #1890ff;">审核</a-button>
        <a-button type="primary" @click="revoKe" preIcon="ant-design:rollback-outlined" style="background-color: #faad14; border-color: #faad14;">撤销</a-button>
        <a-button type="primary" @click="suBmitbgsc" preIcon="ant-design:check-circle-outlined" style="background-color: #52c41a; border-color: #52c41a;">批量报告生成</a-button>
        <a-button  type="primary" preIcon="ant-design:export-outlined" @click="onExportXls"> 导出</a-button>
        <j-upload-button  type="primary" preIcon="ant-design:import-outlined" @click="onImportXls">导入</j-upload-button>
        <a-dropdown v-if="selectedRowKeys.length > 0">
          <template #overlay>
            <a-menu>
              <a-menu-item key="1" @click="batchHandleDelete">
                <Icon icon="ant-design:delete-outlined"></Icon>
                删除
              </a-menu-item>
            </a-menu>
          </template>
          <a-button>批量操作
            <Icon icon="mdi:chevron-down"></Icon>
          </a-button>
        </a-dropdown>
        <!-- 高级查询 -->
        <super-query :config="superQueryConfig" @search="handleSuperQuery" />
      </template>
      <!--操作栏-->
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getDropDownAction(record)"/>
      </template>

      <template v-slot:bodyCell="{ column, record, index, text }">
        <template v-if="column.dataIndex==='file'">
          <!--文件字段回显插槽-->
          <span v-if="!text" style="font-size: 12px;font-style: italic;">无文件</span>
          <a-button v-else :ghost="true" type="primary" preIcon="ant-design:download-outlined" size="small" @click="downloadFile(text)">下载</a-button>
        </template>
      </template>
    </BasicTable>
    <!-- 表单区域 -->
    <CenterMedicalModal ref="registerModal" @success="handleSuccess"></CenterMedicalModal>
  </div>
</template>

<script lang="ts" name="centerMedical-centerMedical" setup>
  import { message } from 'ant-design-vue';
  import { ref, reactive } from 'vue';
  import { BasicTable, useTable, TableAction } from '/@/components/Table';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { columns, superQuerySchema } from './CenterMedical.data';
  import { list, deleteOne, batchDelete, getImportUrl, getExportUrl,pushAotification ,pushAotificationsh,pushAotificationshb,pushBotificationshbc} from './CenterMedical.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import CenterMedicalModal from './components/CenterMedicalModal.vue'
  import { useUserStore } from '/@/store/modules/user';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';

  const formRef = ref();
  const queryParam = reactive<any>({});
  const toggleSearchStatus = ref<boolean>(false);
  const registerModal = ref();
  const userStore = useUserStore();
  //注册table数据
  const { prefixCls, tableContext, onExportXls, onImportXls } = useListPage({
    tableProps: {
      title: '病历监测报告生成表',
      api: list,
      columns,
      canResize:false,
      useSearchForm: false,
      actionColumn: {
        width: 120,
        fixed: 'right',
      },
      beforeFetch: (params) => {
        return Object.assign(params, queryParam);
      },
    },
    exportConfig: {
      name: "病历监测报告生成表",
      url: getExportUrl,
      params: queryParam,
    },
	  importConfig: {
	    url: getImportUrl,
	    success: handleSuccess
	  },
  });
  const [registerTable, { reload, collapseAll, updateTableDataRecord, findTableDataRecord, getDataSource }, { rowSelection, selectedRowKeys }] = tableContext;
  const labelCol = reactive({
    xs:24,
    sm:4,
    xl:6,
    xxl:4
  });
  const wrapperCol = reactive({
    xs: 24,
    sm: 20,
  });

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    searchQuery();
  }

  /**
   * 新增事件
   */
  function handleAdd() {
    registerModal.value.disableSubmit = false;
    registerModal.value.add();
  }
  
  /**
   * 编辑事件
   */
function handleEdit(record: Recordable) {
  // 检查states状态
  if (record && (record.states === "2" || record.states === "3")) {
    message.warning('已提交或已审核的数据不可编辑');
    return;
  }
  
  registerModal.value.disableSubmit = false;
  registerModal.value.edit(record);
}
   
  /**
   * 详情
   */
  function handleDetail(record: Recordable) {
    registerModal.value.disableSubmit = true;
    registerModal.value.edit(record);
  }
   
/**
 * 删除事件
 */
async function handleDelete(record) {
  // 定义不可删除的center值及对应的提示信息
  const unremovableCenters = {
    '2': '已提交的数据不可删除',
    '3': '已审核的数据不可删除',
  };
  
  // 检查center是否在不可删除的列表中
  if (unremovableCenters[record.states]) {
    message.warning(unremovableCenters[record.states]);
    return;
  }
  await deleteOne({ id: record.id }, handleSuccess);
}

   
  /**
   * 批量删除事件
   */
  async function batchHandleDelete() {
    await batchDelete({ ids: selectedRowKeys.value }, handleSuccess);
  }




//批量报告生成
//   function suBmitbgsc(){
//     pushBotificationshbc({ ids: selectedRowKeys.value });
// }  











// 批量报告生成
function suBmitbgsc() {
  // 检查是否选择数据
  if (selectedRowKeys.value.length === 0) {
    message.warning('请先选择需要生成报告的数据');
    return;
  }
   const id = selectedRowKeys.value[0];
     // 检查推送状态
  const dataSource = getDataSource();
  const record = dataSource.find(item => item.id === id);
  
  if (record && record.generation === '2') {
    message.warning('选中的数据已生成报告，请勿重复生成');
    return;
  }
  // 将数组转换为逗号分隔的字符串
  const idsString = selectedRowKeys.value.join(',');
  pushBotificationshbc({ ids: idsString })
    .then((res) => {
      if (res  === '批量报告生成成功!') {
        handleSuccess();
      }
    })
    .catch(error => {
      console.error('提交失败：', error);
      message.error('提交请求异常，请稍后重试');
    });
}
   
  /**
   * 成功回调
   */
  function handleSuccess() {
    (selectedRowKeys.value = []) && reload();
  }
   
  /**
   * 操作栏
   */
  function getTableAction(record) {
    return [
      {
        label: '编辑',
        onClick: handleEdit.bind(null, record),
      },
    ];
  }
   
  /**
   * 下拉操作栏
   */
  function getDropDownAction(record) {
    return [
      {
        label: '详情',
        onClick: handleDetail.bind(null, record),
      }, {
        label: '删除',
        popConfirm: {
          title: '是否确认删除',
          confirm: handleDelete.bind(null, record),
          placement: 'topLeft',
        }
      }
    ]
  }

  /**
   * 查询
   */
  function searchQuery() {
    reload();
  }
  
  /**
   * 重置
   */
  function searchReset() {
    formRef.value.resetFields();
    selectedRowKeys.value = [];
    //刷新数据
    reload();
  }
  

/**
 * 提交按钮事件
 */
function suBmit() {
  
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择一条数据进行提交');
    return;
  }
  
  if (selectedRowKeys.value.length > 1) {
    message.warning('只能选择一条数据进行提交');
    return;
  }
  const id = selectedRowKeys.value[0];
     // 检查推送状态
  const dataSource = getDataSource();
  const record = dataSource.find(item => item.id === id);
  
  if (record && record.states === '2') {
    message.warning('已提交的数据不能再次提交');
    return;
  }
  if (record && record.states === '3') {
    message.warning('已审核的数据不能再次提交');
    return;
  }

  console.log('当前选中的ID:', id);
  pushAotification(id)
    .then(res => {
      // 根据后端实际返回结构判断  后端成功的代码要对应
      if (res  === '提交成功！') {
        handleSuccess();
      }
    })
    .catch(error => {
      console.error('提交失败：', error);
      message.error('提交请求异常，请稍后重试');
    });
}





//审核
function revIew() {
  
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择一条数据进行审核');
    return;
  }
  
  if (selectedRowKeys.value.length > 1) {
    message.warning('只能选择一条数据进行审核');
    return;
  }
  const id = selectedRowKeys.value[0];
     // 检查推送状态
  const dataSource = getDataSource();
  const record = dataSource.find(item => item.id === id);

    // 检查states状态
  if (record && record.states === "1") {
    message.warning('未提交的数据不可审核');
    return;
  }
  
  if (record && record.states === '3') {
    message.warning('已审核的数据不能再次审核');
    return;
  }

  console.log('当前选中的ID:', id);
  pushAotificationsh(id)
    .then(res => {
      // 根据后端实际返回结构判断  后端成功的代码要对应
      if (res  === '审核成功！') {
        handleSuccess();
      }
    })
    .catch(error => {
      console.error('审核失败：', error);
      message.error('审核请求异常，请稍后重试');
    });
}






//撤销
function revoKe() {
  // 判断是否选中数据，未选中则提示
  if (selectedRowKeys.value.length === 0) {
    message.warning('请选择一条数据进行撤销');
    return;
  }
  // 判断是否选中多条数据，是则提示
  if (selectedRowKeys.value.length > 1) {
    message.warning('只能选择一条数据进行撤销');
    return;
  }
  const id = selectedRowKeys.value[0];
  const dataSource = getDataSource();
  const record = dataSource.find(item => item.id === id);


  // 新增判断：只有 states 为 '3' 时才允许撤销
  if (record && record.states!== "3") {
    message.warning('只有已审核的数据才可撤销');
    return;
  }

  console.log('当前选中的ID:', id);
  pushAotificationshb(id)
   .then(res => {
      if (res === '撤销成功！') {
        handleSuccess();
      }
    })
   .catch(error => {
      console.error('撤销失败：', error);
      message.error('撤销请求异常，请稍后重试');
    });
}



</script>

<style lang="less" scoped>
  .jeecg-basic-table-form-container {
    padding: 0;
    .table-page-search-submitButtons {
      display: block;
      margin-bottom: 24px;
      white-space: nowrap;
    }
    .query-group-cust{
      min-width: 100px !important;
    }
    .query-group-split-cust{
      width: 30px;
      display: inline-block;
      text-align: center
    }
    .ant-form-item:not(.ant-form-item-with-help){
      margin-bottom: 16px;
      height: 32px;
    }
    :deep(.ant-picker),:deep(.ant-input-number){
      width: 100%;
    }
  }
</style>
