import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '个人编码',
    align:"center",
    dataIndex: 'cardId'
   },
   {
    title: '转出地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: '转出机构',
    align:"center",
    dataIndex: 'orgcode_dictText'
   },
   {
    title: '转入地区',
    align:"center",
    dataIndex: 'referralZonecode_dictText'
   },
   {
    title: '转入机构',
    align:"center",
    dataIndex: 'referralOrgcode_dictText'
   },
   {
    title: '转诊日期',
    align:"center",
    dataIndex: 'referralDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '转诊原因',
    align:"center",
    dataIndex: 'reason'
   },
   {
    title: '转诊确认',
    align:"center",
    dataIndex: 'referralType',
    customRender:({text}) => {
       return  render.renderSwitch(text, [{text:'是',value:'Y'},{text:'否',value:'N'}])
     },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
      label: "转出地区",
      field: 'zonecode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    //所属部门修改后更新负责部门下拉框数据
                    formModel.orgcode=''
                    updateSchema([
                      {
                        field: 'orgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "转出机构",
      field: 'orgcode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
	{
      label: "个人编码",
      field: 'cardId',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "转诊日期",
      field: 'referralDate',
      component: 'DatePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD'
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '个人编码',
    field: 'cardId',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入个人编码!'},
          ];
     },
  },
  {
      label: "转出地区",
      field: 'zonecode',
      required:true,
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    //所属部门修改后更新负责部门下拉框数据
                    formModel.orgcode=''
                    updateSchema([
                      {
                        field: 'orgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "转出机构",
      field: 'orgcode',
       required:true,
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
      label: "转入地区",
      field: 'referralZonecode',
      required:true,
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    //所属部门修改后更新负责部门下拉框数据
                    formModel.referralOrgcode=''
                    updateSchema([
                      {
                        field: 'referralOrgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "转入机构",
      field: 'referralOrgcode',
       required:true,
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
    label: '转诊日期',
    field: 'referralDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入转诊日期!'},
          ];
     },
  },
  {
    label: '转诊原因',
    field: 'reason',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入转诊原因!'},
          ];
     },
  },
  {
    label: '转诊确认',
    field: 'referralType',
     component: 'JSwitch',
     componentProps:{
     },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入转诊确认!'},
          ];
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  cardId: {title: '个人编码',order: 0,view: 'text', type: 'string',},
  zonecode: {title: '转出地区',order: 1,view: 'text', type: 'string',},
  orgcode: {title: '转出机构',order: 2,view: 'text', type: 'string',},
  referralZonecode: {title: '转入地区',order: 3,view: 'text', type: 'string',},
  referralOrgcode: {title: '转入机构',order: 4,view: 'text', type: 'string',},
  referralDate: {title: '转诊日期',order: 5,view: 'date', type: 'string',},
  reason: {title: '转诊原因',order: 6,view: 'text', type: 'string',},
  referralType: {title: '转诊确认',order: 7,view: 'switch', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
