import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '报告地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: '报告机构',
    align:"center",
    dataIndex: 'orgcode_dictText'
   },
   {
    title: '卡片ID',
    align:"center",
    dataIndex: 'cardId'
   },
   {
    title: '姓名',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'sex_dictText'
   },
   {
    title: '疾病名称',
    align:"center",
    dataIndex: 'disasterType_dictText'
   },
   {
    title: '抗病毒治疗号',
    align:"center",
    dataIndex: 'kbd'
   },
   {
    title: '随访次数',
    align:"center",
    dataIndex: 'sfcs'
   },
   {
    title: '最后一次随访时间',
    align:"center",
    dataIndex: 'zhycsfsj',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '失访确认日期',
    align:"center",
    dataIndex: 'sfqrrq',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
      label: "报告地区",
      field: 'zonecode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.orgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'orgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "报告机构",
      field: 'orgcode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
	{
      label: "卡片ID",
      field: 'cardId',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "姓名",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "失访确认日期",
      field: 'sfqrrq',
      component: 'DatePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD'
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
      label: "报告地区",
      field: 'zonecode',
      required:true,
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.orgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'orgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "报告机构",
      field: 'orgcode',
      required:true,
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
    label: '卡片ID',
    field: 'cardId',
    component: 'Input',
  },
  {
    label: '姓名',
    field: 'name',
    component: 'Input',
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sex",
        type: "radio"
     },
  },
  {
    label: '疾病名称',
    field: 'disasterType',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_udp_disaster_type"
     },
  },
  {
    label: '抗病毒治疗号',
    field: 'kbd',
    component: 'Input',
  },
  {
    label: '随访次数',
    field: 'sfcs',
    component: 'Input',
  },
  {
    label: '最后一次随访时间',
    field: 'zhycsfsj',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '失访确认日期',
    field: 'sfqrrq',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  zonecode: {title: '报告地区',order: 0,view: 'text', type: 'string',},
  orgcode: {title: '报告机构',order: 1,view: 'text', type: 'string',},
  cardId: {title: '卡片ID',order: 2,view: 'text', type: 'string',},
  name: {title: '姓名',order: 3,view: 'text', type: 'string',},
  sex: {title: '性别',order: 4,view: 'radio', type: 'string',dictCode: 'aids_sex',},
  disasterType: {title: '疾病名称',order: 5,view: 'list', type: 'string',dictCode: 'aids_udp_disaster_type',},
  kbd: {title: '抗病毒治疗号',order: 6,view: 'text', type: 'string',},
  sfcs: {title: '随访次数',order: 7,view: 'text', type: 'string',},
  zhycsfsj: {title: '最后一次随访时间',order: 8,view: 'date', type: 'string',},
  sfqrrq: {title: '失访确认日期',order: 9,view: 'date', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
