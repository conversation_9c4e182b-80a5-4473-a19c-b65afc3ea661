import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '所属地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: '所属机构',
    align:"center",
    dataIndex: 'orgcode_dictText'
   },
   {
    title: '姓名',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '身份证号',
    align:"center",
    dataIndex: 'idNumber'
   },
   {
    title: '电话',
    align:"center",
    dataIndex: 'phone'
   },
   {
    title: '现住地区',
    align:"center",
    dataIndex: 'address'
   },
   {
    title: '现住详细地址',
    align:"center",
    dataIndex: 'addr'
   },
   {
    title: '初筛检测日期',
    align:"center",
    dataIndex: 'checkDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '确证阳性时间',
    align:"center",
    dataIndex: 'inTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '处置时间',
    align:"center",
    dataIndex: 'handleTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '处置状态',
    align:"center",
    dataIndex: 'handleState_dictText'
   },
   {
    title: '处置人',
    align:"center",
    dataIndex: 'handleUser'
   },
   {
    title: '电话2',
    align:"center",
    dataIndex: 'phone2'
   },
   {
    title: '分配地区',
    align:"center",
    dataIndex: 'allocationZone_dictText'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '所属地区',
    field: 'zonecode',
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.orgcode=''
          //所属部门修改后更新负责部门下拉框数据
          
          updateSchema([
            {
              field: 'orgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '所属单位',
    field: 'orgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
	{
      label: "姓名",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "初筛检测日期",
      field: 'checkDate',
      component: 'DatePicker',
      componentProps: {
        valueFormat: 'YYYY-MM-DD'
      },
      //colProps: {span: 6},
 	},
	{
      label: "处置状态",
      field: 'handleState',
      component: 'JDictSelectTag',
      componentProps:{
          dictCode:"aids_handle_state"
      },
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '所属地区',
    field: 'zonecode',
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.orgcode=''
          //所属部门修改后更新负责部门下拉框数据
          
          updateSchema([
            {
              field: 'orgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '所属单位',
    field: 'orgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: '姓名',
    field: 'name',
    component: 'Input',
  },
  {
    label: '身份证号',
    field: 'idNumber',
    component: 'Input',
  },
  {
    label: '电话',
    field: 'phone',
    component: 'Input',
  },
{
    label: '现住地址',
    field: 'address',
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
         /* formModel.orgcode=''
          //所属部门修改后更新负责部门下拉框数据
          
          updateSchema([
            {
              field: 'orgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]); */
        },
      };
    },
  },
  {
    label: '现住详细地址',
    field: 'addr',
    component: 'Input',
  },
  {
    label: '初筛检测日期',
    field: 'checkDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '确证阳性时间',
    field: 'inTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '处置时间',
    field: 'handleTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '处置状态',
    field: 'handleState',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_handle_state",
        type: "radio"
     },
  },
  {
    label: '处置人',
    field: 'handleUser',
    component: 'Input',
  },
  {
    label: '电话2',
    field: 'phone2',
    component: 'Input',
  },
  {
    label: '分配地区',
    field: 'allocationZone',
    component: 'Zonecode',
    /* defaultValue:'130100000'  ,  */
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
         /* formModel.orgcode=''
          //所属部门修改后更新负责部门下拉框数据
          
          updateSchema([
            {
              field: 'orgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]); */
        },
      };
    },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  zonecode: {title: '所属地区',order: 0,view: 'text', type: 'string',},
  orgcode: {title: '所属机构',order: 1,view: 'text', type: 'string',},
  name: {title: '姓名',order: 2,view: 'text', type: 'string',},
  idNumber: {title: '身份证号',order: 3,view: 'text', type: 'string',},
  phone: {title: '电话',order: 4,view: 'text', type: 'string',},
  address: {title: '现住地区',order: 5,view: 'text', type: 'string',},
  addr: {title: '现住详细地址',order: 6,view: 'text', type: 'string',},
  checkDate: {title: '初筛检测日期',order: 7,view: 'date', type: 'string',},
  inTime: {title: '确证阳性时间',order: 8,view: 'date', type: 'string',},
  handleTime: {title: '处置时间',order: 9,view: 'date', type: 'string',},
  handleState: {title: '处置状态',order: 10,view: 'radio', type: 'string',dictCode: 'aids_handle_state',},
  handleUser: {title: '处置人',order: 11,view: 'text', type: 'string',},
  phone2: {title: '电话2',order: 12,view: 'text', type: 'string',},
  allocationZone: {title: '分配地区',order: 13,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
