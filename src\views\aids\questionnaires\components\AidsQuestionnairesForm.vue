<template>
  <div>
    <BasicForm @register="registerForm" ref="formRef"/>
    <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated  @change="handleChangeTabs">
      <a-tab-pane tab="哨点监测调查问卷子表" key="aidsQuestionnairesSub" :forceRender="true">
        <JVxeTable
          keep-source
          resizable
          ref="aidsQuestionnairesSub"
          v-if="aidsQuestionnairesSubTable.show"
          :loading="aidsQuestionnairesSubTable.loading"
          :columns="aidsQuestionnairesSubTable.columns"
          :dataSource="aidsQuestionnairesSubTable.dataSource"
          :height="340"
          :rowNumber="true"
          :rowSelection="true"
          :disabled="formDisabled"
          :toolbar="true"
        />
      </a-tab-pane>
    </a-tabs>

    <div style="width: 100%;text-align: center" v-if="!formDisabled">
      <a-button @click="handleSubmit" pre-icon="ant-design:check" type="primary">提 交</a-button>
    </div>
  </div>
</template>

<script lang="ts">

  import {BasicForm, useForm} from '/@/components/Form/index';
  import { computed, defineComponent, reactive, ref, unref } from 'vue';
  import {defHttp} from '/@/utils/http/axios';
  import { propTypes } from '/@/utils/propTypes';
  import { useJvxeMethod } from '/@/hooks/system/useJvxeMethods';
  import { VALIDATE_FAILED } from '/@/utils/common/vxeUtils';
  import {getBpmFormSchema,aidsQuestionnairesSubColumns} from '../AidsQuestionnaires.data';
  import {saveOrUpdate,aidsQuestionnairesSubList} from '../AidsQuestionnaires.api';

  export default defineComponent({
    name: "AidsQuestionnairesForm",
    components:{
      BasicForm,
    },
    props:{
      formData: propTypes.object.def({}),
      formBpm: propTypes.bool.def(true),
    },
    setup(props){
      const [registerForm, { setFieldsValue, setProps }] = useForm({
        labelWidth: 150,
        schemas: getBpmFormSchema(props.formData),
        showActionButtonGroup: false,
        baseColProps: {span: 6}
      });

      const formDisabled = computed(()=>{
        if(props.formData.disabled === false){
          return false;
        }
        return true;
      });

      const refKeys = ref(['aidsQuestionnairesSub', ]);
      const activeKey = ref('aidsQuestionnairesSub');
      const aidsQuestionnairesSub = ref();
      const tableRefs = {aidsQuestionnairesSub, };
      const aidsQuestionnairesSubTable = reactive({
        loading: false,
        dataSource: [],
        columns:aidsQuestionnairesSubColumns,
        show: false
      })

      const [handleChangeTabs,handleSubmit,requestSubTableData,formRef] = useJvxeMethod(requestAddOrEdit,classifyIntoFormData,tableRefs,activeKey,refKeys,validateSubForm);

      function classifyIntoFormData(allValues) {
        let main = Object.assign({}, allValues.formValue)
        return {
          ...main, // 展开
          aidsQuestionnairesSubList: allValues.tablesValue[0].tableData,
        }
      }

      //表单提交事件
      async function requestAddOrEdit(values) {
        await saveOrUpdate(values, true);
      }

      const queryByIdUrl = '/questionnaires/aidsQuestionnaires/queryById';
      async function initFormData(){
        let params = {id: props.formData.dataId};
        const data = await defHttp.get({url: queryByIdUrl, params});
        //设置表单的值
        await setFieldsValue({...data});
        requestSubTableData(aidsQuestionnairesSubList, {id: data.id}, aidsQuestionnairesSubTable, ()=>{
          aidsQuestionnairesSubTable.show = true;
        });
        //默认是禁用
        await setProps({disabled: formDisabled.value})
      }

      initFormData();

      return {
        registerForm,
        formDisabled,
        formRef,
        handleSubmit,
        activeKey,
        handleChangeTabs,
        aidsQuestionnairesSub,
        aidsQuestionnairesSubTable,
      }
    }
  });
</script>