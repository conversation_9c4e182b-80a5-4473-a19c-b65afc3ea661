import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '病原体类型',
    align: "center",
    dataIndex: 'pathogentype'
  },
  {
    title: '样本来源',
    align: "center",
    dataIndex: 'samplesource'
  },
  {
    title: '样本采集日期',
    align: "center",
    dataIndex: 'samplecollectiondate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '检测方法',
    align: "center",
    dataIndex: 'testmethod'
  },
  {
    title: '检测结果',
    align: "center",
    dataIndex: 'testresult'
  },
  {
    title: '检测日期',
    align: "center",
    dataIndex: 'testdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '病原体亚型',
    align: "center",
    dataIndex: 'pathogen'
  },
  {
    title: '抗微生物药物敏感性',
    align: "center",
    dataIndex: 'antimicrobi'
  },
  {
    title: '机构代码',
    align: "center",
    dataIndex: 'reportingorgid'
  },
  {
    title: '生成人姓名',
    align: "center",
    dataIndex: 'reportername'
  },
  {
    title: '报告状态',
    align: "center",
    dataIndex: 'state_dictText'
  },
  {
    title: '报告生成日期',
    align: "center",
    dataIndex: 'reportdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '状态',
    align: "center",
    dataIndex: 'states_dictText'
  },
  {
    title: '附件',
    align: "center",
    dataIndex: 'file',
  },
    {
    title: '批量报告生成',
    align: "center",
    dataIndex: 'generation_dictText'
  },
];

// 高级查询数据
export const superQuerySchema = {
  pathogentype: {title: '病原体类型',order: 0,view: 'text', type: 'string',},
  samplesource: {title: '样本来源',order: 1,view: 'text', type: 'string',},
  samplecollectiondate: {title: '样本采集日期',order: 2,view: 'date', type: 'string',},
  testmethod: {title: '检测方法',order: 3,view: 'text', type: 'string',},
  testresult: {title: '检测结果',order: 4,view: 'text', type: 'string',},
  testdate: {title: '检测日期',order: 5,view: 'date', type: 'string',},
  pathogen: {title: '病原体亚型',order: 6,view: 'text', type: 'string',},
  antimicrobi: {title: '抗微生物药物敏感性',order: 7,view: 'text', type: 'string',},
  reportingorgid: {title: '机构代码',order: 8,view: 'text', type: 'string',},
  reportername: {title: '生成人姓名',order: 9,view: 'text', type: 'string',},
  state: {title: '报告状态',order: 10,view: 'list', type: 'string',dictCode: 'BG_STATE',},
  reportdate: {title: '报告生成日期',order: 11,view: 'date', type: 'string',},
  states: {title: '状态',order: 12,view: 'list', type: 'string',dictCode: 'STATES',},
  file: {title: '附件',order: 13,view: 'file', type: 'string',},
  generation: {title: '批量报告生成',order: 14,view: 'list', type: 'string',dictCode: 'RATION',},
};
