-- 注意：该页面对应的前台目录为views/hivymzixun文件夹下
-- 如果你想更改到其他目录，请修改sql中component字段对应的值


INSERT INTO sys_permission(id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external) 
VALUES ('2024101506507180220', NULL, '检测数据管理', '/hivymzixun/aidsHivTestList', 'hivymzixun/AidsHivTestList', NULL, NULL, 0, NULL, '1', 0.00, 0, NULL, 1, int_to_bool(0), 0, 0, 0, NULL, '1', 0, 0, 'admin', '2024-10-15 18:50:22', NULL, NULL, 0);

-- 权限控制sql
-- 新增
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101506507190221', '2024101506507180220', '添加检测数据管理', NULL, NULL, 0, NULL, NULL, 2, 'hivymzixun:aids_hiv_test:add', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-15 18:50:22', NULL, NULL, 0, 0, '1', 0);
-- 编辑
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101506507190222', '2024101506507180220', '编辑检测数据管理', NULL, NULL, 0, NULL, NULL, 2, 'hivymzixun:aids_hiv_test:edit', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-15 18:50:22', NULL, NULL, 0, 0, '1', 0);
-- 删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101506507190223', '2024101506507180220', '删除检测数据管理', NULL, NULL, 0, NULL, NULL, 2, 'hivymzixun:aids_hiv_test:delete', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-15 18:50:22', NULL, NULL, 0, 0, '1', 0);
-- 批量删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101506507190224', '2024101506507180220', '批量删除检测数据管理', NULL, NULL, 0, NULL, NULL, 2, 'hivymzixun:aids_hiv_test:deleteBatch', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-15 18:50:22', NULL, NULL, 0, 0, '1', 0);
-- 导出excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101506507190225', '2024101506507180220', '导出excel_检测数据管理', NULL, NULL, 0, NULL, NULL, 2, 'hivymzixun:aids_hiv_test:exportXls', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-15 18:50:22', NULL, NULL, 0, 0, '1', 0);
-- 导入excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101506507190226', '2024101506507180220', '导入excel_检测数据管理', NULL, NULL, 0, NULL, NULL, 2, 'hivymzixun:aids_hiv_test:importExcel', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-15 18:50:22', NULL, NULL, 0, 0, '1', 0);