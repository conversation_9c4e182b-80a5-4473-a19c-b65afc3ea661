import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '筛查地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: '筛查机构',
    align:"center",
    dataIndex: 'orgcode_dictText'
   },
   {
    title: '姓名',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '身份证号',
    align:"center",
    dataIndex: 'idCard'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'sex_dictText'
   },
   {
    title: '出生日期',
    align:"center",
    dataIndex: 'birthDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '筛查原因',
    align:"center",
    dataIndex: 'cause_dictText'
   },
   {
    title: '其它筛查原因',
    align:"center",
    dataIndex: 'causeOthen'
   },
   {
    title: '症状',
    align:"center",
    dataIndex: 'symptom_dictText'
   },
   {
    title: '其它症状',
    align:"center",
    dataIndex: 'symptomOthen'
   },
   {
    title: '样本类型',
    align:"center",
    dataIndex: 'sampleType_dictText'
   },
   {
    title: '其它样本',
    align:"center",
    dataIndex: 'sampleOthen'
   },
   {
    title: '筛查检测结果',
    align:"center",
    dataIndex: 'bencjieg_dictText'
   },
   {
    title: '筛查检测日期',
    align:"center",
    dataIndex: 'scdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
      label: "筛查地区",
      field: 'zonecode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.orgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'orgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "筛查机构",
      field: 'orgcode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
	{
      label: "姓名",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "身份证号",
      field: 'idCard',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "筛查检测结果",
      field: 'bencjieg',
      component: 'JDictSelectTag',
      componentProps:{
          dictCode:"aids_xbscjg"
      },
      //colProps: {span: 6},
 	},
     {
      label: "筛查检测日期",
      field: "scdate",
      component: 'RangePicker',
      componentProps: {
        valueType: 'Date',
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
      label: "筛查地区",
      field: 'zonecode',
      required:true,
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.orgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'orgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "筛查机构",
      field: 'orgcode',
      required:true,
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
    label: '姓名',
    field: 'name',
    component: 'Input',
  },
  {
    label: '身份证号',
    field: 'idCard',
    component: 'Input',
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sex",
        type: "radio"
     },
  },
  {
    label: '出生日期',
    field: 'birthDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '筛查原因',
    field: 'cause',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_xbscyy"
     },
  },
  {
    label: '其它筛查原因',
    field: 'causeOthen',
    component: 'Input',
  },
  {
    label: '症状',
    field: 'symptom',
    component: 'JSelectMultiple',
    componentProps:{
        dictCode:"aids_xbsczz"
     },
  },
  {
    label: '其它症状',
    field: 'symptomOthen',
    component: 'Input',
  },
  {
    label: '样本类型',
    field: 'sampleType',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_xbscyb"
     },
  },
  {
    label: '其它样本',
    field: 'sampleOthen',
    component: 'Input',
  },
  {
    label: '筛查检测结果',
    field: 'bencjieg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_xbscjg",
        type: "radio"
     },
  },
  {
    label: '筛查检测日期',
    field: 'scdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  zonecode: {title: '筛查地区',order: 0,view: 'text', type: 'string',},
  orgcode: {title: '筛查机构',order: 1,view: 'text', type: 'string',},
  name: {title: '姓名',order: 2,view: 'text', type: 'string',},
  idCard: {title: '身份证号',order: 3,view: 'text', type: 'string',},
  sex: {title: '性别',order: 4,view: 'radio', type: 'string',dictCode: 'aids_sex',},
  birthDate: {title: '出生日期',order: 5,view: 'date', type: 'string',},
  cause: {title: '筛查原因',order: 6,view: 'list', type: 'string',dictCode: 'aids_xbscyy',},
  causeOthen: {title: '其它筛查原因',order: 7,view: 'text', type: 'string',},
  symptom: {title: '症状',order: 8,view: 'list_multi', type: 'string',dictCode: 'aids_xbsczz',},
  symptomOthen: {title: '其它症状',order: 9,view: 'text', type: 'string',},
  sampleType: {title: '样本类型',order: 10,view: 'list', type: 'string',dictCode: 'aids_xbscyb',},
  sampleOthen: {title: '其它样本',order: 11,view: 'text', type: 'string',},
  bencjieg: {title: '筛查检测结果',order: 12,view: 'radio', type: 'string',dictCode: 'aids_xbscjg',},
  scdate: {title: '筛查检测日期',order: 13,view: 'date', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
