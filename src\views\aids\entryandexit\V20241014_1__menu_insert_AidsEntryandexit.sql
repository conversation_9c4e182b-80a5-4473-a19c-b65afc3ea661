-- 注意：该页面对应的前台目录为views/entryandexit文件夹下
-- 如果你想更改到其他目录，请修改sql中component字段对应的值


INSERT INTO sys_permission(id, parent_id, name, url, component, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_route, is_leaf, keep_alive, hidden, hide_tab, description, status, del_flag, rule_flag, create_by, create_time, update_by, update_time, internal_or_external) 
VALUES ('2024101403182030500', NULL, '药品出入库管理', '/entryandexit/aidsEntryandexitList', 'entryandexit/AidsEntryandexitList', NULL, NULL, 0, NULL, '1', 0.00, 0, NULL, 1, int_to_bool(0), 0, 0, 0, NULL, '1', 0, 0, 'admin', '2024-10-14 15:18:50', NULL, NULL, 0);

-- 权限控制sql
-- 新增
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101403182030501', '2024101403182030500', '添加药品出入库管理', NULL, NULL, 0, NULL, NULL, 2, 'entryandexit:aids_entryandexit:add', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-14 15:18:50', NULL, NULL, 0, 0, '1', 0);
-- 编辑
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101403182030502', '2024101403182030500', '编辑药品出入库管理', NULL, NULL, 0, NULL, NULL, 2, 'entryandexit:aids_entryandexit:edit', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-14 15:18:50', NULL, NULL, 0, 0, '1', 0);
-- 删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101403182030503', '2024101403182030500', '删除药品出入库管理', NULL, NULL, 0, NULL, NULL, 2, 'entryandexit:aids_entryandexit:delete', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-14 15:18:50', NULL, NULL, 0, 0, '1', 0);
-- 批量删除
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101403182030504', '2024101403182030500', '批量删除药品出入库管理', NULL, NULL, 0, NULL, NULL, 2, 'entryandexit:aids_entryandexit:deleteBatch', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-14 15:18:50', NULL, NULL, 0, 0, '1', 0);
-- 导出excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101403182030505', '2024101403182030500', '导出excel_药品出入库管理', NULL, NULL, 0, NULL, NULL, 2, 'entryandexit:aids_entryandexit:exportXls', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-14 15:18:50', NULL, NULL, 0, 0, '1', 0);
-- 导入excel
INSERT INTO sys_permission(id, parent_id, name, url, component, is_route, component_name, redirect, menu_type, perms, perms_type, sort_no, always_show, icon, is_leaf, keep_alive, hidden, hide_tab, description, create_by, create_time, update_by, update_time, del_flag, rule_flag, status, internal_or_external)
VALUES ('2024101403182030506', '2024101403182030500', '导入excel_药品出入库管理', NULL, NULL, 0, NULL, NULL, 2, 'entryandexit:aids_entryandexit:importExcel', '1', NULL, 0, NULL, int_to_bool(1), 0, 0, 0, NULL, 'admin', '2024-10-14 15:18:50', NULL, NULL, 0, 0, '1', 0);