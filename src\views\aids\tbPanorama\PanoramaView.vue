<!-- PanoramaView.vue -->
<template>
  <div class="panorama-view">
    <!-- 右上角关闭图标 -->
    <a-icon type="close" @click="handleClose" style="position: absolute; top: 10px; right: 10px; cursor: pointer" />
    <!-- 使用 a-row 和 a-col 创建网格布局 -->
    <a-row :gutter="[16, 16]">
      <!-- 设置列间距和行间距 -->
      <!-- 左侧：个人信息和链接列表 -->
      <a-col :span="4">
        <!-- 占据 4/24 的宽度 -->
        <!-- 个人基本信息卡片 -->
        <a-card class="personal-info-card light-blue-bg" :bordered="false">
          <template #title>
            <a-icon type="user" />
            <!-- 添加用户图标 -->
            个人基本信息
          </template>
          <p v-if="personalInfo?.patientName"><span class="label-bold">姓名：</span>{{ personalInfo.patientName }}</p>
          <p v-if="personalInfo?.sex1"><span class="label-bold">性别：</span> {{ personalInfo.sex1 == '1' ? '男' : '女' }}</p>
          <p v-if="personalInfo?.idCard"><span class="label-bold">身份证号：</span> {{ personalInfo.idCard }}</p>
          <p v-if="personalInfo?.addr"><span class="label-bold">现居住地：</span><br />{{ personalInfo.addr }}</p>
<!--          <p><span class="label-bold">病种：</span><br />
            <span v-for="(disease, index) in personalInfo.diseaseType" :key="index" :class="['disease-box', getDiseaseColor(disease)]">
              {{ disease }} <span v-if="index !== personalInfo.diseaseType.length - 1"></span>
            </span>
          </p>-->
          <p v-if="personalInfo?.diseaseId1"><span class="label-bold">病种：</span><br /><span :class="['disease-box', getDiseaseColor(personalInfo.diseaseId1)]">{{ personalInfo.diseaseId1 == '14' ? '艾滋' : ' ' }}</span></p>
        </a-card>

        <!-- 超链接列表卡片 -->
        <a-card class="link-list-card light-blue-bg" :bordered="false" style="margin-top: 16px">
          <template #title>
            <a-icon type="link" />
            <!-- 添加链接图标 -->
            查询超链接列表
          </template>
          <ul class="link-list">
            <li v-for="(link, index) in linkList" :key="index">
              <a @click.prevent="handleLinkClick(link.queryKey)" class="link-item"> <span class="dot"></span>{{ link.text }} </a>
            </li>
          </ul>
        </a-card>
      </a-col>

      <!-- 右侧：时间轴 + 查询结果卡片 -->
      <a-col :span="20" class="right-column">
        <!-- 占据 20/24 的宽度 -->
        <a-card v-if="showLastFiveEvents" class="event-timeline-card light-blue-bg" :bordered="false" style="margin-bottom: 16px">
          <template #title>
            <a-icon type="clock-circle" />
            <!-- 添加时钟图标 -->
            {{ eventCardTitle }}
          </template>
          <div class="timeline-container">
            <div v-for="(event, index) in lastFiveEvents" :key="index" class="timeline-item">
              <strong>{{ event.followupdate }}</strong> - {{ event.followupmethodcode }}
              <span v-if="index !== lastFiveEvents.length - 1" class="arrow">-></span>
            </div>
          </div>
        </a-card>

        <!-- 查询结果卡片 -->
        <a-card v-if="currentComponent" class="query-result-card" :bordered="false">
          <component :is="currentComponent" />
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts" setup>
  import { computed, onMounted, ref, watch } from 'vue';
  import { useRoute } from 'vue-router'; // 确保正确导入 useRoute
  import { defHttp } from '@/utils/http/axios';
  // 直接导入所有需要的组件
  import QueryEpidemiologicalHistoryInfo from '@/views/aids/aidsEpide/AidsEpideList.vue';
  import QueryFollowInfo from '@/views/aids/zhiliao/remind/suifangtixing/AidsZhiliaosuifangtixingList.vue';
  import { router } from '@/router';
  // 如果有其他组件，请继续导入
  // import QueryPersonalStateInfo from '...'; // 示例
  // import QueryDeathRegisterInfo from '...'; // 示例
  // ...

  // 定义路径到组件的映射表
  const pathToComponentMap = {
    // 'queryPersonalStateInfo': QueryPersonalStateInfo,
    // 'queryDeathRegisterInfo': QueryDeathRegisterInfo,
    // 'queryDiagnosisInfo': QueryDiagnosisInfo,
    // queryLabTestInfo: QueryLabTestInfo,
    queryEpidemiologicalHistoryInfo: QueryEpidemiologicalHistoryInfo,
    queryFollowInfo: QueryFollowInfo,
    // 添加更多查询组件映射...
  };

  // 初始化个人信息为空对象，并固定“病种”属性
  const personalInfo = ref<{
    patientName?: string;
    sex1?: string;
    idCard?: string;
    addr?: string; // 添加现居住地字段
    diseaseId1: string; // 固定为 ["肺结核", "艾滋病", "手足口"]
  }>();
  // 从后端获取个人信息的方法
  async function fetchPersonalInfo(id: string) {
    try {
      const response = await defHttp.get(
        {
          url: '/reportManage/infectioncardAids/queryById',
          params: { id }, // 请求体数据
          headers: {
            'Content-Type': 'application/json', // 请求头
          },
          timeout: 5000, // 请求超时时间
        },
        {
          isTransformResponse: false, // 是否转换响应数据
          errorMessageMode: 'none', // 错误处理模式
        }
      );
      if (response.result) {
        // 更新个人信息，但保持病种为 "肺结核"
        personalInfo.value = { ...response.result };
      } else {
        console.error('API 响应中没有找到结果数据');
      }
    } catch (error) {
      console.error('获取个人信息失败:', error);
    }
  }
  // 使用 useRoute 获取当前路由信息
  const route = useRoute(); // 直接调用 useRoute
  // 组件挂载时调用此方法
  onMounted(() => {
    const id = route.query.id as string;
    fetchPersonalInfo(id);
  });
  // 监听路由变化，以便在参数改变时重新获取数据
  watch(
    () => route.query.id,
    async (newId) => {
      if (newId) {
        await fetchPersonalInfo(newId as string);
      }
    },
    { immediate: true } // 立即执行一次，确保首次加载时也获取数据
  );

  // 填充超链接列表
  const linkList = ref([
    { text: '个人状态信息', queryKey: 'queryPersonalStateInfo' },
    { text: '死亡登记信息', queryKey: 'queryDeathRegisterInfo' },
    { text: '临床诊疗信息', queryKey: 'queryDiagnosisInfo' },
    { text: '实验室检测信息', queryKey: 'queryLabTestInfo' },
    { text: '流行病学史信息', queryKey: 'queryEpidemiologicalHistoryInfo' },
    { text: '随访信息', queryKey: 'queryFollowInfo' },
  ]);

  // 控制最后五次事件卡片是否显示
  const showLastFiveEvents = ref(false);
  // 获取最后五次事件的数据（假设从API获取）
  const lastFiveEvents = ref([] as { followupdate: string; followupmethodcode: string }[]);
  //查询界面
  const currentComponent = ref(null);
  // 新增响应式变量用于存储当前选中的链接文本
  const selectedLinkText = ref('');
  //点击超链接
  function handleLinkClick(queryKey: string) {
    console.log('Clicked queryKey:', queryKey); // 添加这一行来调试
    // 根据 queryKey 设置 selectedLinkText 的值
    switch (queryKey) {
      case 'queryFollowInfo':
        selectedLinkText.value = '最近五次随访信息摘要';
        break;
      case 'queryEpidemiologicalHistoryInfo':
        selectedLinkText.value = '最近五次流行病学调查摘要';
        break;
      // 添加更多 case 处理其他类型的链接...
      default:
        selectedLinkText.value = '默认标题';
    }

    if (!pathToComponentMap[queryKey]) {
      console.error('Invalid queryKey:', queryKey);
      return;
    }

    const component = pathToComponentMap[queryKey];
    if (component) {
      currentComponent.value = component;

      // 独立地处理最后五次事件的显示逻辑，并传递 id
      const registrationno = route.query.registrationno as string;
      fetchLastFiveEvents(queryKey, registrationno);
    } else {
      console.error('找不到对应的查询组件:', queryKey);
    }
  }
  // 使用计算属性来生成标题（可选）
  // 或者直接在模板中使用 selectedLinkText 变量
  const eventCardTitle = computed(() => {
    return selectedLinkText.value || '最近五次事件摘要'; // 默认标题
  });
  async function fetchLastFiveEvents(queryKey: string, registrationno: string) {
    try {
      const urlMapping = {
        queryFollowInfo: '/tbFollow/tbFollow/queryByRegistrationno',
        queryLabTestInfo: '/labSputumsmear/labSputumsmear/queryByRegistrationno',
        // 添加更多查询组件映射...
      };

      const url = urlMapping[queryKey];
      if (!url) {
        console.warn('未知的查询键:', queryKey);
        return;
      }

      const response = await defHttp.get(
        {
          url,
          params: { registrationno }, // 使用提供的病案登记号参数
          headers: {
            'Content-Type': 'application/json',
          },
          timeout: 5000,
        },
        {
          isTransformResponse: false,
          errorMessageMode: 'none',
        }
      );

      if (response.result) {
        // 处理不同类型的查询结果并统一格式
        lastFiveEvents.value = normalizeEvents(queryKey, response.result);
        showLastFiveEvents.value = true; // 显示最后五次事件卡片
      } else {
        console.error('API 响应中没有找到结果数据');
        showLastFiveEvents.value = false; // 如果没有数据，则不显示卡片
      }
    } catch (error) {
      console.error('获取最后五次事件失败:', error);
      showLastFiveEvents.value = false; // 如果请求失败，则不显示卡片
    }
  }

  // 根据查询类型处理查询结果并返回统一格式
  function normalizeEvents(queryKey: string, results: any[]): { followupdate: string; followupmethodcode: string }[] {
    switch (queryKey) {
      case 'queryFollowInfo':
        return results.map((event) => ({
          followupdate: event.followupdate || '无日期',
          followupmethodcode: event.followupmethodcode || '无方法',
        }));
      case 'queryLabTestInfo':
        return results.map((test) => ({
          followupdate: test.checkdate || '无日期',
          followupmethodcode: test.checkresultcode || '无方法',
        }));
      // 可以继续为其他查询类型添加类似的处理逻辑
      default:
        return [];
    }
  }
  // 方法：根据疾病名称返回对应的颜色类名
  function getDiseaseColor(disease: string): string {
    switch (disease) {
      case '肺结核':
      case '14':
        return 'text-red';
      case '手足口':
        return 'text-blue';
      default:
        return '';
    }
  }
  // 定义关闭图标的点击处理函数
  function handleClose() {
    router.back(); // 直接返回到上一级
  }
</script>

<style scoped>
  .panorama-view {
    padding: 20px;
  }

  .personal-info-card,
  .link-list-card,
  .query-result-card,
  .event-timeline-card {
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  }

  /* 确保卡片有合适的最小高度 */
  .link-list-card {
    min-height: 10px; /* 根据实际内容调整 */
    margin-top: 52px; /* 增加间距 */
  }
  /* 修改超链接列表样式 */
  .link-list {
    list-style-type: none; /* 移除默认的列表样式 */
    padding-left: 0;
  }

  .link-list li {
    margin-bottom: 20px; /* 增加每行之间的间距 */
  }

  .link-item {
    text-decoration: underline; /* 添加下划线 */
    color: #007bff; /* 链接颜色 */
    display: flex;
    align-items: center;
  }

  .dot {
    display: inline-block;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background-color: black;
    margin-right: 8px; /* 圆点与文字之间的间距 */
  }
  .event-timeline-card {
    margin-bottom: 16px;
    max-width: 100%; /* 控制卡片的最大宽度 */
  }

  .timeline-container {
    display: flex;
    flex-wrap: wrap; /* 允许换行 */
    gap: 8px; /* 控制项目之间的间距 */
  }

  .timeline-item {
    display: flex;
    align-items: center;
    white-space: nowrap; /* 防止文本换行 */
  }

  .arrow {
    margin-left: 6px;
    margin-right: 6px;
    font-size: 20px; /* 增加箭头大小 */
    color: blue; /* 增加颜色对比度 */
    transform: scaleX(2); /* 横向拉长箭头 */
  }
  /* 增加箭头左侧边距 */
  .arrow {
    margin-right: 16px; /* 增加左侧边距 */
    margin-left: 16px;
  }

  @media (max-width: 768px) {
    .panorama-view .ant-col {
      flex: none;
      width: 100%;
    }

    .personal-info-card,
    .link-list-card {
      margin-bottom: 16px;
    }
  }
  /* 加粗标签 */
  .label-bold {
    font-weight: bold;
  }
  /* 淡蓝色背景 */
  .light-blue-bg {
    background-color: #e7f3fd; /* 淡蓝色 */
  }
  /* 文本颜色 */
  .text-red {
    color: red;
  }
  .text-blue {
    color: blue;
  }
  /* 圆角框样式 */
  .disease-box {
    display: inline-block;
    margin: 3px; /* 设置右侧间距 */
    padding: 4px 12px; /* 内边距 */
    border-radius: 12px; /* 圆角 */
    background-color: #f0f0f0; /* 默认背景颜色 */
    font-size: 12px; /* 字体大小 */
    border: 1px solid #ccc; /* 默认边框颜色 */
    color: #333; /* 文字颜色 */
  }

  .text-red {
    border-color: red;
    background-color: #ffcccc; /* 淡红色背景 */
    color: red;
  }

  .text-blue {
    border-color: blue;
    background-color: #cce6ff; /* 淡蓝色背景 */
    color: blue;
  }
</style>
