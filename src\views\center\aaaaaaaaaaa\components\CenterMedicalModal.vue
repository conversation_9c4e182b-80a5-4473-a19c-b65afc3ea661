<template>
  <!--  :visible="visible" 模态框的显示与隐藏 -->
  <!-- @ok="handleOk"   模态框的保存按钮 可以点击 -->
  <!--@cancel="handleCancel" cancelText="关闭" 关闭按钮和(x) -->
  <j-modal :title="title" :width="width" :visible="visible"  @ok="handleOk" :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" @cancel="handleCancel" cancelText="关闭">
   <!-- registerForm 父绑定子组件的实列-->
   <!-- @ok="submitCallback"  组建的自定义事件 -->
    <!-- :formDisabled="disableSubmit" :formBpm="false"   添加，编辑，详情表单的判断-->
    <CenterMedicalForm ref="registerForm" @ok="submitCallback" :formDisabled="disableSubmit" :formBpm="false"></CenterMedicalForm>
  </j-modal>
</template>


      <!-- 表单区域 
       ref="registerModal"  defineExpose  父绑定子组件的实列
       @success="handleSuccess"   const emit = defineEmits(['register', 'ok']);子组件调用父组件自定义的事件函数
      -->

<script lang="ts" setup>
  import { ref, nextTick, defineExpose } from 'vue';
  import CenterMedicalForm from './CenterMedicalForm.vue'
  import JModal from '/@/components/Modal/src/JModal/JModal.vue';
  
  const title = ref<string>('');
  const width = ref<number>(800);
  // 初始值为false，模态框隐藏
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const registerForm = ref();


  //组建的自定义事件
  const emit = defineEmits(['register', 'success']);

  /**
   * 新增
   */
  function add() {
    title.value = '新增';
    // 显示模态框
    visible.value = true;
    nextTick(() => {
      // 调用子组件的add方法
      registerForm.value.add();
    });
  }
  
  /**
   * 编辑
   * @param record
   */
  function edit(record) {
    title.value = disableSubmit.value ? '详情' : '编辑';
    // 显示模态框
    visible.value = true;
    nextTick(() => {
       // 调用子组件的add方法
      registerForm.value.edit(record);
    });
  }
  
  /**
   * 确定按钮点击事件（新增，编辑保存按钮）
   */
  function handleOk() {
    // 新增，编辑的时候调用子组件的submitForm方法
    registerForm.value.submitForm();
  }

  /**
   * form保存回调事件
   */
  function submitCallback() {
    //调用handleCancel方法 隐藏模态框
    handleCancel();
     //执行成功调用父组件的@success="handleSuccess"
    emit('success');
  }

  /**
   * 取消按钮回调事件(关闭 或者 X号)
   */
  function handleCancel() {
    alert("77777777777")
    visible.value = false;
  }





   //ref  父组件访问子组件的方法  defineExpos导出的意思
  defineExpose({
    add,
    edit,
    disableSubmit,
  });
</script>

<style lang="less">
  /**隐藏样式-modal确定按钮 */
  .jee-hidden {
    display: none !important;
  }
</style>
<style lang="less" scoped></style>
