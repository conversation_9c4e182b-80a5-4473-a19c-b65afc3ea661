import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '首治疗地区',
    align:"center",
    dataIndex: 'firstZonecode_dictText'
   },
   {
    title: '首治疗机构',
    align:"center",
    dataIndex: 'firstOrgcode_dictText'
   },
   {
    title: '抗病毒治疗号',
    align:"center",
    dataIndex: 'avtn'
   },
   {
    title: '现治疗地区',
    align:"center",
    dataIndex: 'nowZonecode_dictText'
   },
   {
    title: '现治疗机构',
    align:"center",
    dataIndex: 'nowOrgcode_dictText'
   },
   {
    title: '患者姓名',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '身份证件类型',
    align:"center",
    dataIndex: 'identityType_dictText'
   },
   {
    title: '身份证件号码',
    align:"center",
    dataIndex: 'identity'
   },
   {
    title: '现住地区',
    align:"center",
    dataIndex: 'livingaddresscode_dictText'
   },
   {
    title: '现住详细地址',
    align:"center",
    dataIndex: 'livingaddressdetails'
   },
   {
    title: '户籍地址',
    align:"center",
    dataIndex: 'domicileaddresscode_dictText'
   },
   {
    title: '户籍详细地址',
    align:"center",
    dataIndex: 'domicileadrressdetails'
   },
   {
    title: '出生日期',
    align:"center",
    dataIndex: 'birthDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '年龄',
    align:"center",
    dataIndex: 'age'
   },
   {
    title: '年龄单位',
    align:"center",
    dataIndex: 'ageUnit_dictText'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'sex_dictText'
   },
   {
    title: '婚姻状况',
    align:"center",
    dataIndex: 'maritalstatuscode_dictText'
   },
   {
    title: '孕周',
    align:"center",
    dataIndex: 'gw'
   },
   {
    title: '医疗付费方式',
    align:"center",
    dataIndex: 'drg_dictText'
   },
   {
    title: '感染途径',
    align:"center",
    dataIndex: 'infectionroutecode_dictText'
   },
   {
    title: '确诊HIV抗体阳性时间',
    align:"center",
    dataIndex: 'hivresultdate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '疾病症状',
    align:"center",
    dataIndex: 'diseaseSymptoms_dictText'
   },
   {
    title: '病人目前是否出现以下症状、体征、疾病?',
    align:"center",
    dataIndex: 'isSymptoms_dictText'
   },
   {
    title: '病人目前WHOI临床分期',
    align:"center",
    dataIndex: 'who_dictText'
   },
   {
    title: '病人目前身高',
    align:"center",
    dataIndex: 'height'
   },
   {
    title: '病人目前体重',
    align:"center",
    dataIndex: 'weight'
   },
   {
    title: '既往服用过哪些抗病毒治疗药物',
    align:"center",
    dataIndex: 'jwfy'
   },
   {
    title: '是否接受过复方新诺明预防机会性感染治疗',
    align:"center",
    dataIndex: 'isnoff_dictText'
   },
   {
    title: '本次艾滋病抗病毒治疗开始日期',
    align:"center",
    dataIndex: 'hivTreat',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '药品名称1',
    align:"center",
    dataIndex: 'drug1'
   },
   {
    title: '发药量',
    align:"center",
    dataIndex: 'medicine1'
   },
   {
    title: '药品名称2',
    align:"center",
    dataIndex: 'drug2'
   },
   {
    title: '发药量',
    align:"center",
    dataIndex: 'medicine2'
   },
   {
    title: '药品名称3',
    align:"center",
    dataIndex: 'drug3'
   },
   {
    title: '发药量',
    align:"center",
    dataIndex: 'medicine3'
   },
   {
    title: '下次随访领药日期',
    align:"center",
    dataIndex: 'nextDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '录入日期',
    align:"center",
    dataIndex: 'addTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '录入人员',
    align:"center",
    dataIndex: 'addUser'
   },
   {
    title: '治疗方案',
    align:"center",
    dataIndex: 'treatment_dictText'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
      label: "现治疗地区",
      field: 'nowZonecode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.nowOrgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'nowOrgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "现治疗机构",
      field: 'nowOrgcode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
	{
      label: "患者姓名",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
  {
      label: "抗病毒治疗号",
      field: 'avtn',
      component: 'Input',
      //colProps: {span: 6},
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
      label: "首治疗地区",
      field: 'firstZonecode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.firstOrgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'firstOrgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "首治疗机构",
      field: 'firstOrgcode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
    label: '抗病毒治疗号',
    field: 'avtn',
    component: 'Input',
  },
  {
      label: "现治疗地区",
      field: 'nowZonecode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
                 const { updateSchema } = formActionType;
                    formModel.nowOrgcode=''
                    //所属部门修改后更新负责部门下拉框数据
                    updateSchema([
                      {
                        field: 'nowOrgcode',
                        componentProps: { zonecode:values,value:''},
                      },
                    ]);
               },
             };
           },
      //colProps: {span: 6},
  },
  {
      label: "现治疗机构",
      field: 'nowOrgcode',
      component:'Orgcode',
      componentProps:{
        /* zonecode:'120101000', */
        orgType:'A,J'
      }  
  },
  {
    label: '患者姓名',
    field: 'name',
    component: 'Input',
  },
  {
    label: '身份证件类型',
    field: 'identityType',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_identity_type"
     },
  },
  {
    label: '身份证件号码',
    field: 'identity',
    component: 'Input',
  },
  {
      label: "现住地区",
      field: 'livingaddresscode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
               },
             };
           },
  },
  {
    label: '现住详细地址',
    field: 'livingaddressdetails',
    component: 'Input',
  },
  {
      label: "户籍地址",
      field: 'domicileaddresscode',
      component:'Zonecode',
      /* defaultValue:'130100000'  ,  */
       componentProps: ({ formActionType, formModel }) => {
             return {
               allowClear:false,
               onOptionsChange: (values) => {
               },
             };
           },
  },
  {
    label: '户籍详细地址',
    field: 'domicileadrressdetails',
    component: 'Input',
  },
  {
    label: '出生日期',
    field: 'birthDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '年龄',
    field: 'age',
    component: 'Input',
  },
  {
    label: '年龄单位',
    field: 'ageUnit',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_age_unit",
        type: "radio"
     },
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sex",
        type: "radio"
     },
  },
  {
    label: '婚姻状况',
    field: 'maritalstatuscode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_report_marital",
        type: "radio"
     },
  },
  {
    label: '孕周',
    field: 'gw',
    component: 'Input',
  },
  {
    label: '医疗付费方式',
    field: 'drg',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_paying_type"
     },
  },
  {
    label: '感染途径',
    field: 'infectionroutecode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_infectionRouteCode"
     },
  },
  {
    label: '确诊HIV抗体阳性时间',
    field: 'hivresultdate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '疾病症状',
    field: 'diseaseSymptoms',
    component: 'JCheckbox',
    componentProps:{
        dictCode:"aids_report_symptom"
     },
  },
  {
    label: '病人目前是否出现以下症状、体征、疾病?',
    field: 'isSymptoms',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_report_nois",
        type: "radio"
     },
  },
  {
    label: '病人目前WHOI临床分期',
    field: 'who',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_report_who",
        type: "radio"
     },
  },
  {
    label: '病人目前身高',
    field: 'height',
    component: 'Input',
  },
  {
    label: '病人目前体重',
    field: 'weight',
    component: 'Input',
  },
  {
    label: '既往服用过哪些抗病毒治疗药物',
    field: 'jwfy',
    component: 'Input',
  },
  {
    label: '是否接受过复方新诺明预防机会性感染治疗',
    field: 'isnoff',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_report_nois",
        type: "radio"
     },
  },
  {
    label: '本次艾滋病抗病毒治疗开始日期',
    field: 'hivTreat',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '药品名称1',
    field: 'drug1',
    component: 'Input',
  },
  {
    label: '发药量',
    field: 'medicine1',
    component: 'Input',
  },
  {
    label: '药品名称2',
    field: 'drug2',
    component: 'Input',
  },
  {
    label: '发药量',
    field: 'medicine2',
    component: 'Input',
  },
  {
    label: '药品名称3',
    field: 'drug3',
    component: 'Input',
  },
  {
    label: '发药量',
    field: 'medicine3',
    component: 'Input',
  },
  {
    label: '下次随访领药日期',
    field: 'nextDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '录入日期',
    field: 'addTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '录入人员',
    field: 'addUser',
    component: 'Input',
  },
  {
    label: '治疗方案',
    field: 'treatment',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_treatment_plan"
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  firstZonecode: {title: '首治疗地区',order: 0,view: 'text', type: 'string',},
  firstOrgcode: {title: '首治疗机构',order: 1,view: 'text', type: 'string',},
  avtn: {title: '抗病毒治疗号',order: 2,view: 'text', type: 'string',},
  nowZonecode: {title: '现治疗地区',order: 3,view: 'text', type: 'string',},
  nowOrgcode: {title: '现治疗机构',order: 4,view: 'text', type: 'string',},
  name: {title: '患者姓名',order: 5,view: 'text', type: 'string',},
  identityType: {title: '身份证件类型',order: 6,view: 'list', type: 'string',dictCode: 'aids_identity_type',},
  identity: {title: '身份证件号码',order: 7,view: 'text', type: 'string',},
  livingaddresscode: {title: '现住地区',order: 8,view: 'text', type: 'string',},
  livingaddressdetails: {title: '现住详细地址',order: 9,view: 'text', type: 'string',},
  domicileaddresscode: {title: '户籍地址',order: 10,view: 'text', type: 'string',},
  domicileadrressdetails: {title: '户籍详细地址',order: 11,view: 'text', type: 'string',},
  birthDate: {title: '出生日期',order: 12,view: 'date', type: 'string',},
  age: {title: '年龄',order: 13,view: 'text', type: 'string',},
  ageUnit: {title: '年龄单位',order: 14,view: 'radio', type: 'string',dictCode: 'aids_age_unit',},
  sex: {title: '性别',order: 15,view: 'radio', type: 'string',dictCode: 'aids_sex',},
  maritalstatuscode: {title: '婚姻状况',order: 16,view: 'radio', type: 'string',dictCode: 'aids_report_marital',},
  gw: {title: '孕周',order: 17,view: 'text', type: 'string',},
  drg: {title: '医疗付费方式',order: 18,view: 'list', type: 'string',dictCode: 'aids_paying_type',},
  infectionroutecode: {title: '感染途径',order: 19,view: 'list', type: 'string',dictCode: 'aids_infectionRouteCode',},
  hivresultdate: {title: '确诊HIV抗体阳性时间',order: 20,view: 'date', type: 'string',},
  diseaseSymptoms: {title: '疾病症状',order: 21,view: 'checkbox', type: 'string',dictCode: 'aids_report_symptom',},
  isSymptoms: {title: '病人目前是否出现以下症状、体征、疾病?',order: 22,view: 'radio', type: 'string',dictCode: 'aids_report_nois',},
  who: {title: '病人目前WHOI临床分期',order: 23,view: 'radio', type: 'string',dictCode: 'aids_report_who',},
  height: {title: '病人目前身高',order: 24,view: 'text', type: 'string',},
  weight: {title: '病人目前体重',order: 25,view: 'text', type: 'string',},
  jwfy: {title: '既往服用过哪些抗病毒治疗药物',order: 26,view: 'text', type: 'string',},
  isnoff: {title: '是否接受过复方新诺明预防机会性感染治疗',order: 27,view: 'radio', type: 'string',dictCode: 'aids_report_nois',},
  hivTreat: {title: '本次艾滋病抗病毒治疗开始日期',order: 28,view: 'date', type: 'string',},
  drug1: {title: '药品名称1',order: 29,view: 'text', type: 'string',},
  medicine1: {title: '发药量',order: 30,view: 'text', type: 'string',},
  drug2: {title: '药品名称2',order: 31,view: 'text', type: 'string',},
  medicine2: {title: '发药量',order: 32,view: 'text', type: 'string',},
  drug3: {title: '药品名称3',order: 33,view: 'text', type: 'string',},
  medicine3: {title: '发药量',order: 34,view: 'text', type: 'string',},
  nextDate: {title: '下次随访领药日期',order: 35,view: 'date', type: 'string',},
  addTime: {title: '录入日期',order: 36,view: 'date', type: 'string',},
  addUser: {title: '录入人员',order: 37,view: 'text', type: 'string',},
  treatment: {title: '治疗方案',order: 38,view: 'list', type: 'string',dictCode: 'aids_treatment_plan',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
