<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-row>
						<a-col :span="12">
							<a-form-item label="报告编号" v-bind="validateInfos.reportcode">
								<a-input v-model:value="formData.reportcode" placeholder="请输入报告编号"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="报告标题" v-bind="validateInfos.reporttitle">
								<a-input v-model:value="formData.reporttitle" placeholder="请输入报告标题"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="模板类型" v-bind="validateInfos.modeltype">
								<j-dict-select-tag v-model:value="formData.modeltype" dictCode="MODELC_TYPE" placeholder="请选择模板类型"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="报告状态" v-bind="validateInfos.reportstate">
								<j-dict-select-tag v-model:value="formData.reportstate" dictCode="BG_STATE" placeholder="请选择报告状态"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="审核人" v-bind="validateInfos.audituser">
								<a-input v-model:value="formData.audituser" placeholder="请输入审核人"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="审核状态" v-bind="validateInfos.auditstate">
								<j-dict-select-tag v-model:value="formData.auditstate" dictCode="AUDIT_STATE" placeholder="请选择审核状态"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="审核时间" v-bind="validateInfos.audittime">
								<a-date-picker placeholder="请选择审核时间"  v-model:value="formData.audittime" value-format="YYYY-MM-DD"  style="width: 100%"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="退回意见" v-bind="validateInfos.backreason">
								<a-input v-model:value="formData.backreason" placeholder="请输入退回意见"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="附件" v-bind="validateInfos.file">
								<j-upload v-model:value="formData.file"   ></j-upload>
							</a-form-item>
						</a-col>
            <a-col :span="12">
							<a-form-item v-bind="validateInfos.backreason">
                <a-button 
                  v-if="showPdfOrWord==='1'"
                  type="primary" 
                  style="margin-top: 10px;"
                  @click="requestPrint('test')"
                >
                  <!-- 修改图标使用方式 -->
                  <PrinterOutlined /> 打印pdf
                </a-button>
                <a-button 
                 v-if="showPdfOrWord==='2'"
                  type="primary" 
                  style="margin-top: 10px;"
                  @click="requestPrint2('test')"
                >
                  <!-- 修改图标使用方式 -->
                  <PrinterOutlined /> 打印docx
                  <div id='test'></div>
                </a-button>
							</a-form-item>
						</a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../CenterNdromerereport.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  import axios from 'axios';
  //import mammoth from "mammoth";
  let showPdfOrWord = ref("-1");
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({})},
    formBpm: { type: Boolean, default: true }
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    reportcode: '',   
    reporttitle: '',   
    modeltype: '',   
    reportstate: '',   
    audituser: '',   
    auditstate: '',   
    audittime: '',   
    backreason: '',   
    file: '',   
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  //表单验证
  const validatorRules = reactive({
    reportcode: [{ required: true, message: '请输入报告编号!'},],
    reporttitle: [{ required: true, message: '请输入报告标题!'},],
    modeltype: [{ required: true, message: '请输入模板类型!'},],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(()=>{
    if(props.formBpm === true){
      if(props.formData.disabled === false){
        return false;
      }else{
        return true;
      }
    }
    return props.formDisabled;
  });

  
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if(record.hasOwnProperty(key)){
          tmpData[key] = record[key]
        }
      })
      //赋值
      Object.assign(formData, tmpData);
      showPdfOrWord.value = '-1'
      if("" !== formData.file &&null !== formData.file && undefined !== formData.file && formData.file.indexOf(".pdf") != '-1'){
        showPdfOrWord.value = '1';
      }else if("" !== formData.file &&null !== formData.file && undefined !== formData.file && formData.file.indexOf(".docx") != '-1'){
        showPdfOrWord.value = '2';
      }
    });
  }
  function requestPrint(){
    let lsFeil = formData.file;
    let lsDkh  = window.location;
    let purl ="";
    if(lsDkh.port.indexOf('3100') !== -1){
      purl = 'http://localhost:8080';
    }else{
      purl = 'http://**************:9001';
    }
    axios({
        method: "get",
        url: purl+'/sxstc-prod-monitorreportcenter/sys/common/downFile/'+lsFeil,
        responseType: 'blob',
      }).then((opt) => {
        
        //pdf
       handlePrint(opt)
    })
  }

  function handlePrint(opt) {
    let pdf = "";
    const fileData = new Blob([opt.data], {
                type: 'application/pdf'
            });
    pdf = URL.createObjectURL(fileData);
    if (document.getElementById("print-iframe")) {
        document.body.removeChild(document.getElementById("print-iframe"));
    }
 
    let iframe = document.getElementById("print-iframe");
    // 如果 iframe 不存在，则创建它
    if (!iframe) {
        iframe = document.createElement("iframe");
        iframe.id = "print-iframe";
        iframe.style.display = "none"; // 隐藏 iframe，避免影响页面布局
 
        // 当 iframe 加载完成时，打印内容
        iframe.onload = function () {
            setTimeout(() => {
               // 尝试将焦点设置到 iframe 上（某些浏览器可能需要）
                iframe.contentWindow.focus(); 
                iframe.contentWindow.print(); // 触发打印
            }, 500)
        };
        document.body.appendChild(iframe);
    }
 
    // 设置 iframe 的 src 属性为 PDF URL
    iframe.src = pdf;
}

function requestPrint2(ids){
  let lsFeil = formData.file;
    let lsDkh  = window.location;
    let purl ="";
    if(lsDkh.port.indexOf('3100') !== -1){
      purl = 'http://localhost:8080';
    }else{
      purl = 'http://**************:9001';
    }
  var xhr = new XMLHttpRequest();
    xhr.open("get", purl + '/sxstc-prod-monitorreportcenter/sys/common/downFile/'+lsFeil, true);
    xhr.responseType = "arraybuffer";
    xhr.onload = function () {
    if (xhr.status == 200) {
        mammoth.convertToHtml({ arrayBuffer: new Uint8Array(xhr.response) })
        .then(function (resultObject) {
            nextTick(() => {
            // document.querySelector("#wordView").innerHTML =
            //   resultObject.value;
            // test.value= resultObject.value;
            var el=document.getElementById(ids);
            var iframe = document.createElement('IFRAME');
            var doc = null;
            iframe.setAttribute('style','position:absolute;width:0px;height:px;left:,top:0;');
            document.getElementById(ids).appendChild(iframe);
            doc = iframe.contentWindow.document;
            doc.write(resultObject.value);
            doc.close();
            iframe.contentWindow.focus();
            iframe.contentWindow.print();
            });
        });
    }
    };
    xhr.send();
  }
  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    await validate();
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }


  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
