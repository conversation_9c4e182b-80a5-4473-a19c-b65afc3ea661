import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '病毒种类',
    align:"center",
    dataIndex: 'virusSpecies_dictText'
   },
   {
    title: '相关症状',
    align:"center",
    dataIndex: 'symptoms'
   },
   {
    title: '预防方式',
    align:"center",
    dataIndex: 'preventWays'
   },
   {
    title: '应对措施',
    align:"center",
    dataIndex: 'responseWays'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '病毒种类',
    field: 'virusSpecies',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"virus_species"
     },
  },
  {
    label: '相关症状',
    field: 'symptoms',
    component: 'Input',
  },
  {
    label: '预防方式',
    field: 'preventWays',
    component: 'Input',
  },
  {
    label: '应对措施',
    field: 'responseWays',
    component: 'Input',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  virusSpecies: {title: '病毒种类',order: 0,view: 'list', type: 'string',dictCode: 'virus_species',},
  symptoms: {title: '相关症状',order: 1,view: 'text', type: 'string',},
  preventWays: {title: '预防方式',order: 2,view: 'text', type: 'string',},
  responseWays: {title: '应对措施',order: 3,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}