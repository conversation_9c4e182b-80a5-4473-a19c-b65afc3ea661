import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: 'T04梅毒结果判断',
    align:"center",
    dataIndex: 't04'
   },
   {
    title: 'BED检测ODn值',
    align:"center",
    dataIndex: 'bedodn'
   },
   {
    title: 'A06样本来源',
    align:"center",
    dataIndex: 'a06'
   },
   {
    title: 'D02最近一年，你与她/他（配偶或同居者）发生性行为时使用安全套的频率如何？',
    align:"center",
    dataIndex: 'd02_dictText'
   },
   {
    title: '上报国家时间',
    align:"center",
    dataIndex: 'chianTime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'F03最近一年，你最近一次与临时性伴发生性行为时使用安全套了吗？',
    align:"center",
    dataIndex: 'f03_dictText'
   },
   {
    title: 'A05调查日期',
    align:"center",
    dataIndex: 'a05',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'B04户籍所在地',
    align:"center",
    dataIndex: 'b04_dictText'
   },
   {
    title: '添加人',
    align:"center",
    dataIndex: 'adduser'
   },
   {
    title: '国家返回错误信息',
    align:"center",
    dataIndex: 'messagedesc'
   },
   {
    title: '审核人',
    align:"center",
    dataIndex: 'auduser'
   },
   {
    title: '锁定状态',
    align:"center",
    dataIndex: 'sdstate'
   },
   {
    title: 'BED检测',
    align:"center",
    dataIndex: 'bedjc'
   },
   {
    title: '添加地区',
    align:"center",
    dataIndex: 'addzone'
   },
   {
    title: 'B04户籍所在地-市',
    align:"center",
    dataIndex: 'b04c'
   },
   {
    title: 'F02最近一年，你与临时性伴发生性行为时使用安全套的频率如何？',
    align:"center",
    dataIndex: 'f02_dictText'
   },
   {
    title: '亲和力检测结果',
    align:"center",
    dataIndex: 'qhjg'
   },
   {
    title: 'T03aHIV第一次ELISA初筛',
    align:"center",
    dataIndex: 't03a_dictText'
   },
   {
    title: '修改人',
    align:"center",
    dataIndex: 'modyuser'
   },
   {
    title: 'G02最近一年，你与同性发生过商业性行为吗？',
    align:"center",
    dataIndex: 'g02_dictText'
   },
   {
    title: 'T01本次调查是否采血？',
    align:"center",
    dataIndex: 't01_dictText'
   },
   {
    title: 'T04a梅毒ELISA初筛结果',
    align:"center",
    dataIndex: 't04a_dictText'
   },
   {
    title: '申请修改时间',
    align:"center",
    dataIndex: 'sqedittime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'A02哨点类型',
    align:"center",
    dataIndex: 'a02_dictText'
   },
   {
    title: '亲和力检测ODn值',
    align:"center",
    dataIndex: 'qhodn'
   },
   {
    title: 'T03bHIV第二次ELISA复检',
    align:"center",
    dataIndex: 't03b_dictText'
   },
   {
    title: 'C03与艾滋病病毒感染者一起吃饭会感染艾滋病吗？',
    align:"center",
    dataIndex: 'c03_dictText'
   },
   {
    title: '是否有效',
    align:"center",
    dataIndex: 'state'
   },
   {
    title: 'C06感染艾滋病病毒的妇女生下的小孩有可能得艾滋病吗？',
    align:"center",
    dataIndex: 'c06_dictText'
   },
   {
    title: '调查员签字',
    align:"center",
    dataIndex: 'inquiryname'
   },
   {
    title: 'E03最近一年，你最近一次与小姐(暗娼)发生性行为时使用安全套了吗？',
    align:"center",
    dataIndex: 'e03_dictText'
   },
   {
    title: '删除时间',
    align:"center",
    dataIndex: 'deletetime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'D03最近一年，你最近一次与她/他（配偶或同居者）发生性行为时使用安全套了吗？',
    align:"center",
    dataIndex: 'd03_dictText'
   },
   {
    title: '申请修改状态',
    align:"center",
    dataIndex: 'sqstate'
   },
   {
    title: '亲和力检测',
    align:"center",
    dataIndex: 'qhjc'
   },
   {
    title: 'C09 你认为自己感染上艾滋病病毒的风险有多高？',
    align:"center",
    dataIndex: 'c09_dictText'
   },
   {
    title: 'T03HIV抗体检测结果',
    align:"center",
    dataIndex: 't03_dictText'
   },
   {
    title: '修改地区',
    align:"center",
    dataIndex: 'modyzone'
   },
   {
    title: 'C08只与一个性伴发生性行为可以减少艾滋病的传播吗？',
    align:"center",
    dataIndex: 'c08_dictText'
   },
   {
    title: 'G04最近一年，你最近一次与同性发生肛交性行为时使用安全套了吗？',
    align:"center",
    dataIndex: 'g04_dictText'
   },
   {
    title: 'T03cHIV抗体确认试验结果',
    align:"center",
    dataIndex: 't03c_dictText'
   },
   {
    title: 'T05bHCV第二次ELISA复检',
    align:"center",
    dataIndex: 't05b_dictText'
   },
   {
    title: '上报国家人',
    align:"center",
    dataIndex: 'chianUser'
   },
   {
    title: 'BED检测结果',
    align:"center",
    dataIndex: 'bedjg'
   },
   {
    title: 'B07文化程度',
    align:"center",
    dataIndex: 'b07_dictText'
   },
   {
    title: 'T02b如果是，最早确证检测为阳性的时间是',
    align:"center",
    dataIndex: 't02b_dictText'
   },
   {
    title: 'H03你与别人共用过针具吗？',
    align:"center",
    dataIndex: 'h03_dictText'
   },
   {
    title: 'H02你注射过毒品吗？',
    align:"center",
    dataIndex: 'h02_dictText'
   },
   {
    title: 'E02最近一年，你与小姐（暗娼）发生性行为时使用安全套的频率如何？',
    align:"center",
    dataIndex: 'e02_dictText'
   },
   {
    title: 'B02出生年',
    align:"center",
    dataIndex: 'b022_dictText'
   },
   {
    title: '哨点负责单位',
    align:"center",
    dataIndex: 'orgcode_dictText'
   },
   {
    title: '添加科室',
    align:"center",
    dataIndex: 'adddep'
   },
   {
    title: '督导员签字',
    align:"center",
    dataIndex: 'custodian'
   },
   {
    title: 'B02出生年',
    align:"center",
    dataIndex: 'b02',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '样本来源其它',
    align:"center",
    dataIndex: 'a06a'
   },
   {
    title: 'HIV病毒载量（单位：拷贝数/ml，t03为0时不填。2024年改造新加字段。）',
    align:"center",
    dataIndex: 'viralLoad'
   },
   {
    title: '修改时间',
    align:"center",
    dataIndex: 'modytime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'B04户籍所在地-省',
    align:"center",
    dataIndex: 'b04b'
   },
   {
    title: 'G03最近一年，你与同性发生肛交性行为时使用安全套的频率如何？',
    align:"center",
    dataIndex: 'g03_dictText'
   },
   {
    title: 'A04问卷编号',
    align:"center",
    dataIndex: 'a04'
   },
   {
    title: 'C04输入带有艾滋病病毒的血液会得艾滋病吗？',
    align:"center",
    dataIndex: 'c04_dictText'
   },
   {
    title: '数据交换-数据来源',
    align:"center",
    dataIndex: 'datasource'
   },
   {
    title: '未做新发感染检测原因',
    align:"center",
    dataIndex: 'weixf'
   },
   {
    title: '监测对象征集单位',
    align:"center",
    dataIndex: 'potorgcode'
   },
   {
    title: 'B02a年龄',
    align:"center",
    dataIndex: 'b02a'
   },
   {
    title: '修改科室',
    align:"center",
    dataIndex: 'modydep'
   },
   {
    title: '报告地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: 'A03哨点所在地区行政编码',
    align:"center",
    dataIndex: 'a03'
   },
   {
    title: '审核状态',
    align:"center",
    dataIndex: 'audstate'
   },
   {
    title: '哨点所在地区',
    align:"center",
    dataIndex: 'potzonecode_dictText'
   },
   {
    title: 'C05与艾滋病病毒感染者共用注射器有可能得艾滋病吗？',
    align:"center",
    dataIndex: 'c05_dictText'
   },
   {
    title: 'F01最近一年，你与临时性伴发生过性行为吗？',
    align:"center",
    dataIndex: 'f01_dictText'
   },
   {
    title: 'T02a是否是既往检测HIV抗体阳性',
    align:"center",
    dataIndex: 't02a_dictText'
   },
   {
    title: '填表日期',
    align:"center",
    dataIndex: 'filltime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '锁定时间',
    align:"center",
    dataIndex: 'sdtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '上报国家返回一位标识，用来操作删除',
    align:"center",
    dataIndex: 'returnVlues'
   },
   {
    title: 'T04b梅毒RPR/TRUST复检结果',
    align:"center",
    dataIndex: 't04b_dictText'
   },
   {
    title: 'T05HCV抗体检测结果',
    align:"center",
    dataIndex: 't05_dictText'
   },
   {
    title: '上报国家状态',
    align:"center",
    dataIndex: 'messageFlag'
   },
   {
    title: 'H04最近六个月注射毒品时，你与别人共用针具的频率如何？',
    align:"center",
    dataIndex: 'h04'
   },
   {
    title: '添加机构',
    align:"center",
    dataIndex: 'addorg'
   },
   {
    title: 'B04户籍所在地-县',
    align:"center",
    dataIndex: 'b04d'
   },
   {
    title: 'E01最近一年，你与小姐（暗娼）发生过性行为吗？',
    align:"center",
    dataIndex: 'e01_dictText'
   },
   {
    title: 'G01最近一年，你与同性发生过肛交性行为吗？',
    align:"center",
    dataIndex: 'g01_dictText'
   },
   {
    title: 'C01一个感染了艾滋病病毒的人能从外表上看出来吗？',
    align:"center",
    dataIndex: 'c01_dictText'
   },
   {
    title: 'T05aHCV第一次ELISA初筛',
    align:"center",
    dataIndex: 't05a_dictText'
   },
   {
    title: 'H01你吸毒吗？',
    align:"center",
    dataIndex: 'h01_dictText'
   },
   {
    title: 'B03婚姻状况',
    align:"center",
    dataIndex: 'b03_dictText'
   },
   {
    title: 'B06在本地居住时间',
    align:"center",
    dataIndex: 'b06'
   },
   {
    title: 'B05民族',
    align:"center",
    dataIndex: 'b05_dictText'
   },
   {
    title: 'D01你有过性行为（指阴道交、肛交、口交）的经历吗？',
    align:"center",
    dataIndex: 'd01_dictText'
   },
   {
    title: '哨点识别码',
    align:"center",
    dataIndex: 'a01'
   },
   {
    title: 'B04A国籍',
    align:"center",
    dataIndex: 'b04a_dictText'
   },
   {
    title: '修改机构',
    align:"center",
    dataIndex: 'modyorg'
   },
   {
    title: '数据交换-更新时间',
    align:"center",
    dataIndex: 'datamodytime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '外键',
    align:"center",
    dataIndex: 'pid'
   },
   {
    title: 'C02蚊虫叮咬会传播艾滋病吗？',
    align:"center",
    dataIndex: 'c02_dictText'
   },
   {
    title: 'C07正确使用安全套可以减少艾滋病的传播吗？',
    align:"center",
    dataIndex: 'c07_dictText'
   },
   {
    title: '删除人',
    align:"center",
    dataIndex: 'deleteuser'
   },
   {
    title: '添加时间',
    align:"center",
    dataIndex: 'addtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'B01性别',
    align:"center",
    dataIndex: 'b01_dictText'
   },
   {
    title: '审核时间',
    align:"center",
    dataIndex: 'audtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '病例报告系统卡片ID',
    align:"center",
    dataIndex: 'bingliid'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '报告地区',
    field: 'apanagecode',
    component: 'Zonecode',
    
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.rptorgcode=''
          //所属部门修改后更新负责部门下拉框数据
          updateSchema([
            {
              field: 'rptorgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'rptorgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '病例报告系统卡片ID',
    field: 'bingliid',
    component: 'Input',
  },
  {
    label: 'T04梅毒结果判断',
    field: 't04',
    component: 'Input',
  },
  {
    label: 'BED检测ODn值',
    field: 'bedodn',
    component: 'Input',
  },
  {
    label: 'A06样本来源',
    field: 'a06',
    component: 'Input',
  },
  {
    label: 'D02最近一年，你与她/他（配偶或同居者）发生性行为时使用安全套的频率如何？',
    field: 'd02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques15",
        type: "radio"
     },
  },
  {
    label: '上报国家时间',
    field: 'chianTime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'F03最近一年，你最近一次与临时性伴发生性行为时使用安全套了吗？',
    field: 'f03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'A05调查日期',
    field: 'a05',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'B04户籍所在地',
    field: 'b04',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques12",
        type: "radio"
     },
  },
  {
    label: '添加人',
    field: 'adduser',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加人!'},
          ];
     },
  },
  {
    label: '国家返回错误信息',
    field: 'messagedesc',
    component: 'Input',
  },
  {
    label: '审核人',
    field: 'auduser',
    component: 'Input',
  },
  {
    label: '锁定状态',
    field: 'sdstate',
    component: 'Input',
  },
  {
    label: 'BED检测',
    field: 'bedjc',
    component: 'Input',
  },
  {
    label: '添加地区',
    field: 'addzone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加地区!'},
          ];
     },
  },
  {
    label: 'B04户籍所在地-市',
    field: 'b04c',
    component: 'Input',
  },
  {
    label: 'F02最近一年，你与临时性伴发生性行为时使用安全套的频率如何？',
    field: 'f02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques15",
        type: "radio"
     },
  },
  {
    label: '亲和力检测结果',
    field: 'qhjg',
    component: 'Input',
  },
  {
    label: 'T03aHIV第一次ELISA初筛',
    field: 't03a',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '修改人',
    field: 'modyuser',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改人!'},
          ];
     },
  },
  {
    label: 'G02最近一年，你与同性发生过商业性行为吗？',
    field: 'g02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'T01本次调查是否采血？',
    field: 't01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'T04a梅毒ELISA初筛结果',
    field: 't04a',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '申请修改时间',
    field: 'sqedittime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'A02哨点类型',
    field: 'a02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sentinel_type",
        type: "radio"
     },
  },
  {
    label: '亲和力检测ODn值',
    field: 'qhodn',
    component: 'Input',
  },
  {
    label: 'T03bHIV第二次ELISA复检',
    field: 't03b',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'C03与艾滋病病毒感染者一起吃饭会感染艾滋病吗？',
    field: 'c03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '是否有效',
    field: 'state',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入是否有效!'},
          ];
     },
  },
  {
    label: 'C06感染艾滋病病毒的妇女生下的小孩有可能得艾滋病吗？',
    field: 'c06',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques14",
        type: "radio"
     },
  },
  {
    label: '调查员签字',
    field: 'inquiryname',
    component: 'Input',
  },
  {
    label: 'E03最近一年，你最近一次与小姐(暗娼)发生性行为时使用安全套了吗？',
    field: 'e03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '删除时间',
    field: 'deletetime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'D03最近一年，你最近一次与她/他（配偶或同居者）发生性行为时使用安全套了吗？',
    field: 'd03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '申请修改状态',
    field: 'sqstate',
    component: 'Input',
  },
  {
    label: '亲和力检测',
    field: 'qhjc',
    component: 'Input',
  },
  {
    label: 'C09 你认为自己感染上艾滋病病毒的风险有多高？',
    field: 'c09',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques14",
        type: "radio"
     },
  },
  {
    label: 'T03HIV抗体检测结果',
    field: 't03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '修改地区',
    field: 'modyzone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改地区!'},
          ];
     },
  },
  {
    label: 'C08只与一个性伴发生性行为可以减少艾滋病的传播吗？',
    field: 'c08',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'G04最近一年，你最近一次与同性发生肛交性行为时使用安全套了吗？',
    field: 'g04',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'T03cHIV抗体确认试验结果',
    field: 't03c',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'T05bHCV第二次ELISA复检',
    field: 't05b',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '上报国家人',
    field: 'chianUser',
    component: 'Input',
  },
  {
    label: 'BED检测结果',
    field: 'bedjg',
    component: 'Input',
  },
  {
    label: 'B07文化程度',
    field: 'b07',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques17",
        type: "radio"
     },
  },
  {
    label: 'T02b如果是，最早确证检测为阳性的时间是',
    field: 't02b',
    component: 'Input',
  },
  {
    label: 'H03你与别人共用过针具吗？',
    field: 'h03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'H02你注射过毒品吗？',
    field: 'h02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'E02最近一年，你与小姐（暗娼）发生性行为时使用安全套的频率如何？',
    field: 'e02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques14",
        type: "radio"
     },
  },
  {
    label: 'B02出生年',
    field: 'b022',
    component: 'Input',
  },
  {
    label: '哨点负责单位',
    field: 'orgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: '添加科室',
    field: 'adddep',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加科室!'},
          ];
     },
  },
  {
    label: '督导员签字',
    field: 'custodian',
    component: 'Input',
  },
  {
    label: 'B02出生年',
    field: 'b02',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '样本来源其它',
    field: 'a06a',
    component: 'Input',
  },
  {
    label: 'HIV病毒载量（单位：拷贝数/ml，t03为0时不填。2024年改造新加字段。）',
    field: 'viralLoad',
    component: 'Input',
  },
  {
    label: '修改时间',
    field: 'modytime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改时间!'},
          ];
     },
  },
  {
    label: 'B04户籍所在地-省',
    field: 'b04b',
    component: 'Input',
  },
  {
    label: 'G03最近一年，你与同性发生肛交性行为时使用安全套的频率如何？',
    field: 'g03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques14",
        type: "radio"
     },
  },
  {
    label: 'A04问卷编号',
    field: 'a04',
    component: 'Input',
  },
  {
    label: 'C04输入带有艾滋病病毒的血液会得艾滋病吗？',
    field: 'c04',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5	",
        type: "radio"
     },
  },
  {
    label: '数据交换-数据来源',
    field: 'datasource',
    component: 'Input',
  },
  {
    label: '未做新发感染检测原因',
    field: 'weixf',
    component: 'Input',
  },
  {
    label: '监测对象征集单位',
    field: 'potorgcode',
    component: 'Input',
  },
  {
    label: 'B02a年龄',
    field: 'b02a',
    component: 'Input',
  },
  {
    label: '修改科室',
    field: 'modydep',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改科室!'},
          ];
     },
  },
  {
    label: '报告地区',
    field: 'apanagecode',
    component: 'Zonecode',
    
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.rptorgcode=''
          //所属部门修改后更新负责部门下拉框数据
          updateSchema([
            {
              field: 'rptorgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: 'A03哨点所在地区行政编码',
    field: 'a03',
    component: 'Input',
  },
  {
    label: '审核状态',
    field: 'audstate',
    component: 'Input',
  },
  {
    label: '哨点所在地区',
    field: 'potzonecode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: 'C05与艾滋病病毒感染者共用注射器有可能得艾滋病吗？',
    field: 'c05',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques14",
        type: "radio"
     },
  },
  {
    label: 'F01最近一年，你与临时性伴发生过性行为吗？',
    field: 'f01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'T02a是否是既往检测HIV抗体阳性',
    field: 't02a',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '填表日期',
    field: 'filltime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '锁定时间',
    field: 'sdtime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '上报国家返回一位标识，用来操作删除',
    field: 'returnVlues',
    component: 'Input',
  },
  {
    label: 'T04b梅毒RPR/TRUST复检结果',
    field: 't04b',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'T05HCV抗体检测结果',
    field: 't05',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '上报国家状态',
    field: 'messageFlag',
    component: 'Input',
  },
  {
    label: 'H04最近六个月注射毒品时，你与别人共用针具的频率如何？',
    field: 'h04',
    component: 'Input',
  },
  {
    label: '添加机构',
    field: 'addorg',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加机构!'},
          ];
     },
  },
  {
    label: 'B04户籍所在地-县',
    field: 'b04d',
    component: 'Input',
  },
  {
    label: 'E01最近一年，你与小姐（暗娼）发生过性行为吗？',
    field: 'e01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'G01最近一年，你与同性发生过肛交性行为吗？',
    field: 'g01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'C01一个感染了艾滋病病毒的人能从外表上看出来吗？',
    field: 'c01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'T05aHCV第一次ELISA初筛',
    field: 't05a',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'H01你吸毒吗？',
    field: 'h01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'B03婚姻状况',
    field: 'b03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques16",
        type: "radio"
     },
  },
  {
    label: 'B06在本地居住时间',
    field: 'b06',
    component: 'Input',
  },
  {
    label: 'B05民族',
    field: 'b05',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_nation",
        type: "select"
     },
  },
  {
    label: 'D01你有过性行为（指阴道交、肛交、口交）的经历吗？',
    field: 'd01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '哨点识别码',
    field: 'a01',
    component: 'Input',
  },
  {
    label: 'B04A国籍',
    field: 'b04a',
     component: 'JDictSelectTag',
     componentProps:{
         dictCode:"edr_nationality",
         type: "select"
      },
  },
  {
    label: '修改机构',
    field: 'modyorg',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改机构!'},
          ];
     },
  },
  {
    label: '数据交换-更新时间',
    field: 'datamodytime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '外键',
    field: 'pid',
    component: 'Input',
  },
  {
    label: 'C02蚊虫叮咬会传播艾滋病吗？',
    field: 'c02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'C07正确使用安全套可以减少艾滋病的传播吗？',
    field: 'c07',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '删除人',
    field: 'deleteuser',
    component: 'Input',
  },
  {
    label: '添加时间',
    field: 'addtime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加时间!'},
          ];
     },
  },
  {
    label: 'B01性别',
    field: 'b01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sex",
        type: "radio"
     },
  },
  {
    label: '审核时间',
    field: 'audtime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },

	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  t04: {title: 'T04梅毒结果判断',order: 0,view: 'text', type: 'string',},
  bedodn: {title: 'BED检测ODn值',order: 1,view: 'text', type: 'string',},
  a06: {title: 'A06样本来源',order: 2,view: 'text', type: 'string',},
  d02: {title: 'D02最近一年，你与她/他（配偶或同居者）发生性行为时使用安全套的频率如何？',order: 3,view: 'radio', type: 'string',},
  chianTime: {title: '上报国家时间',order: 4,view: 'date', type: 'string',},
  f03: {title: 'F03最近一年，你最近一次与临时性伴发生性行为时使用安全套了吗？',order: 5,view: 'radio', type: 'string',},
  a05: {title: 'A05调查日期',order: 6,view: 'date', type: 'string',},
  b04: {title: 'B04户籍所在地',order: 7,view: 'radio', type: 'string',},
  adduser: {title: '添加人',order: 8,view: 'text', type: 'string',},
  messagedesc: {title: '国家返回错误信息',order: 9,view: 'text', type: 'string',},
  auduser: {title: '审核人',order: 10,view: 'text', type: 'string',},
  sdstate: {title: '锁定状态',order: 11,view: 'text', type: 'string',},
  bedjc: {title: 'BED检测',order: 12,view: 'text', type: 'string',},
  addzone: {title: '添加地区',order: 13,view: 'text', type: 'string',},
  b04c: {title: 'B04户籍所在地-市',order: 14,view: 'text', type: 'string',},
  f02: {title: 'F02最近一年，你与临时性伴发生性行为时使用安全套的频率如何？',order: 15,view: 'radio', type: 'string',},
  qhjg: {title: '亲和力检测结果',order: 16,view: 'text', type: 'string',},
  t03a: {title: 'T03aHIV第一次ELISA初筛',order: 17,view: 'radio', type: 'string',},
  modyuser: {title: '修改人',order: 18,view: 'text', type: 'string',},
  g02: {title: 'G02最近一年，你与同性发生过商业性行为吗？',order: 19,view: 'radio', type: 'string',},
  t01: {title: 'T01本次调查是否采血？',order: 20,view: 'radio', type: 'string',},
  t04a: {title: 'T04a梅毒ELISA初筛结果',order: 21,view: 'radio', type: 'string',},
  sqedittime: {title: '申请修改时间',order: 22,view: 'date', type: 'string',},
  a02: {title: 'A02哨点类型',order: 23,view: 'radio', type: 'string',},
  qhodn: {title: '亲和力检测ODn值',order: 24,view: 'text', type: 'string',},
  t03b: {title: 'T03bHIV第二次ELISA复检',order: 25,view: 'radio', type: 'string',},
  c03: {title: 'C03与艾滋病病毒感染者一起吃饭会感染艾滋病吗？',order: 26,view: 'radio', type: 'string',},
  state: {title: '是否有效',order: 27,view: 'text', type: 'string',},
  c06: {title: 'C06感染艾滋病病毒的妇女生下的小孩有可能得艾滋病吗？',order: 28,view: 'radio', type: 'string',},
  inquiryname: {title: '调查员签字',order: 29,view: 'text', type: 'string',},
  e03: {title: 'E03最近一年，你最近一次与小姐(暗娼)发生性行为时使用安全套了吗？',order: 30,view: 'radio', type: 'string',},
  deletetime: {title: '删除时间',order: 31,view: 'date', type: 'string',},
  d03: {title: 'D03最近一年，你最近一次与她/他（配偶或同居者）发生性行为时使用安全套了吗？',order: 32,view: 'radio', type: 'string',},
  sqstate: {title: '申请修改状态',order: 33,view: 'text', type: 'string',},
  qhjc: {title: '亲和力检测',order: 34,view: 'text', type: 'string',},
  c09: {title: 'C09 你认为自己感染上艾滋病病毒的风险有多高？',order: 35,view: 'radio', type: 'string',},
  t03: {title: 'T03HIV抗体检测结果',order: 36,view: 'radio', type: 'string',},
  modyzone: {title: '修改地区',order: 37,view: 'text', type: 'string',},
  c08: {title: 'C08只与一个性伴发生性行为可以减少艾滋病的传播吗？',order: 38,view: 'radio', type: 'string',},
  g04: {title: 'G04最近一年，你最近一次与同性发生肛交性行为时使用安全套了吗？',order: 39,view: 'radio', type: 'string',},
  t03c: {title: 'T03cHIV抗体确认试验结果',order: 40,view: 'radio', type: 'string',},
  t05b: {title: 'T05bHCV第二次ELISA复检',order: 41,view: 'radio', type: 'string',},
  chianUser: {title: '上报国家人',order: 42,view: 'text', type: 'string',},
  bedjg: {title: 'BED检测结果',order: 43,view: 'text', type: 'string',},
  b07: {title: 'B07文化程度',order: 44,view: 'radio', type: 'string',},
  t02b: {title: 'T02b如果是，最早确证检测为阳性的时间是',order: 45,view: 'radio', type: 'string',},
  h03: {title: 'H03你与别人共用过针具吗？',order: 46,view: 'radio', type: 'string',},
  h02: {title: 'H02你注射过毒品吗？',order: 47,view: 'radio', type: 'string',},
  e02: {title: 'E02最近一年，你与小姐（暗娼）发生性行为时使用安全套的频率如何？',order: 48,view: 'radio', type: 'string',},
  b022: {title: 'B02出生年',order: 49,view: 'list', type: 'string',},
  orgcode: {title: '哨点负责单位',order: 50,view: 'list', type: 'string',},
  adddep: {title: '添加科室',order: 51,view: 'text', type: 'string',},
  custodian: {title: '督导员签字',order: 52,view: 'text', type: 'string',},
  b02: {title: 'B02出生年',order: 53,view: 'date', type: 'string',},
  a06a: {title: '样本来源其它',order: 54,view: 'text', type: 'string',},
  viralLoad: {title: 'HIV病毒载量（单位：拷贝数/ml，t03为0时不填。2024年改造新加字段。）',order: 55,view: 'text', type: 'string',},
  modytime: {title: '修改时间',order: 56,view: 'date', type: 'string',},
  b04b: {title: 'B04户籍所在地-省',order: 57,view: 'text', type: 'string',},
  g03: {title: 'G03最近一年，你与同性发生肛交性行为时使用安全套的频率如何？',order: 58,view: 'radio', type: 'string',},
  a04: {title: 'A04问卷编号',order: 59,view: 'text', type: 'string',},
  c04: {title: 'C04输入带有艾滋病病毒的血液会得艾滋病吗？',order: 60,view: 'radio', type: 'string',},
  datasource: {title: '数据交换-数据来源',order: 61,view: 'text', type: 'string',},
  weixf: {title: '未做新发感染检测原因',order: 62,view: 'text', type: 'string',},
  potorgcode: {title: '监测对象征集单位',order: 63,view: 'text', type: 'string',},
  b02a: {title: 'B02a年龄',order: 64,view: 'text', type: 'string',},
  modydep: {title: '修改科室',order: 65,view: 'text', type: 'string',},
  zonecode: {title: '报告地区',order: 66,view: 'list', type: 'string',},
  a03: {title: 'A03哨点所在地区行政编码',order: 67,view: 'text', type: 'string',},
  audstate: {title: '审核状态',order: 68,view: 'text', type: 'string',},
  potzonecode: {title: '哨点所在地区',order: 69,view: 'list', type: 'string',},
  c05: {title: 'C05与艾滋病病毒感染者共用注射器有可能得艾滋病吗？',order: 70,view: 'radio', type: 'string',},
  f01: {title: 'F01最近一年，你与临时性伴发生过性行为吗？',order: 71,view: 'radio', type: 'string',},
  t02a: {title: 'T02a是否是既往检测HIV抗体阳性',order: 72,view: 'radio', type: 'string',},
  filltime: {title: '填表日期',order: 73,view: 'date', type: 'string',},
  sdtime: {title: '锁定时间',order: 74,view: 'date', type: 'string',},
  returnVlues: {title: '上报国家返回一位标识，用来操作删除',order: 75,view: 'text', type: 'string',},
  t04b: {title: 'T04b梅毒RPR/TRUST复检结果',order: 76,view: 'radio', type: 'string',},
  t05: {title: 'T05HCV抗体检测结果',order: 77,view: 'radio', type: 'string',},
  messageFlag: {title: '上报国家状态',order: 78,view: 'text', type: 'string',},
  h04: {title: 'H04最近六个月注射毒品时，你与别人共用针具的频率如何？',order: 79,view: 'text', type: 'string',},
  addorg: {title: '添加机构',order: 80,view: 'text', type: 'string',},
  b04d: {title: 'B04户籍所在地-县',order: 81,view: 'text', type: 'string',},
  e01: {title: 'E01最近一年，你与小姐（暗娼）发生过性行为吗？',order: 82,view: 'radio', type: 'string',},
  g01: {title: 'G01最近一年，你与同性发生过肛交性行为吗？',order: 83,view: 'radio', type: 'string',},
  c01: {title: 'C01一个感染了艾滋病病毒的人能从外表上看出来吗？',order: 84,view: 'radio', type: 'string',},
  t05a: {title: 'T05aHCV第一次ELISA初筛',order: 85,view: 'radio', type: 'string',},
  h01: {title: 'H01你吸毒吗？',order: 86,view: 'radio', type: 'string',},
  b03: {title: 'B03婚姻状况',order: 87,view: 'radio', type: 'string',},
  b06: {title: 'B06在本地居住时间',order: 88,view: 'text', type: 'string',},
  b05: {title: 'B05民族',order: 89,view: 'radio', type: 'string',},
  d01: {title: 'D01你有过性行为（指阴道交、肛交、口交）的经历吗？',order: 90,view: 'radio', type: 'string',},
  a01: {title: '哨点识别码',order: 91,view: 'text', type: 'string',},
  b04a: {title: 'B04A国籍',order: 92,view: 'radio', type: 'string',},
  modyorg: {title: '修改机构',order: 93,view: 'text', type: 'string',},
  datamodytime: {title: '数据交换-更新时间',order: 94,view: 'date', type: 'string',},
  pid: {title: '外键',order: 95,view: 'text', type: 'string',},
  c02: {title: 'C02蚊虫叮咬会传播艾滋病吗？',order: 96,view: 'radio', type: 'string',},
  c07: {title: 'C07正确使用安全套可以减少艾滋病的传播吗？',order: 97,view: 'radio', type: 'string',},
  deleteuser: {title: '删除人',order: 98,view: 'text', type: 'string',},
  addtime: {title: '添加时间',order: 99,view: 'date', type: 'string',},
  b01: {title: 'B01性别',order: 100,view: 'radio', type: 'string',},
  audtime: {title: '审核时间',order: 101,view: 'date', type: 'string',},
  bingliid: {title: '病例报告系统卡片ID',order: 102,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
