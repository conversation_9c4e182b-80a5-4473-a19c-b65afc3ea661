import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '患者姓名',
    align:"center",
    dataIndex: 'patientName'
   },
   {
    title: '身份证件类别',
    align:"center",
    dataIndex: 'cardType_dictText'
   },
   {
    title: '身份证件号码',
    align:"center",
    dataIndex: 'idCard'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'sex_dictText'
   },
   {
    title: '诊断日期时间',
    align:"center",
    dataIndex: 'diagnoseTime'
   },
   {
    title: '发病日期',
    align:"center",
    dataIndex: 'onsetDate'
   },
   {
    title: '诊断结果',
    align:"center",
    dataIndex: 'diseaseCode_dictText'
   },
   {
    title: '诊断地点代码',
    align:"center",
    dataIndex: 'diagnosisOrgCode_dictText'
   },
   {
    title: '科室代码',
    align:"center",
    dataIndex: 'deptCode'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '患者姓名',
    field: 'patientName',
    component: 'Input',
  },
  {
   label: '身份证件类别',
   field: 'cardType',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_identity_type"
    },
  },
  {
    label: '身份证件号码',
    field: 'idCard',
    component: 'Input',
  },
  {
   label: '性别',
   field: 'sex',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"aids_sex",
       type: "radio"
    },
  },
  {
    label: '诊断日期时间',
    field: 'diagnoseTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '发病日期',
    field: 'onsetDate',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '诊断结果',
    field: 'diseaseCode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"diagnosis_result"
     },
  },
  {
    label: '诊断地点代码',
    field: 'diagnosisOrgCode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  },
  {
    label: '科室代码',
    field: 'deptCode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:""
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  patientName: {title: '患者姓名',order: 0,view: 'text', type: 'string',},
  cardType: {title: '身份证件类别',order: 1,view: 'list', type: 'string',dictCode: 'aids_testResult',},
  idCard: {title: '身份证件号码',order: 2,view: 'text', type: 'string',},
  sex: {title: '性别',order: 3,view: 'radio', type: 'string',dictCode: 'aids_sex',},
  diagnoseTime: {title: '诊断日期时间',order: 4,view: 'datetime', type: 'string',},
  onsetDate: {title: '发病日期',order: 5,view: 'datetime', type: 'string',},
  diseaseCode: {title: '诊断结果',order: 6,view: 'list', type: 'string',dictCode: 'diagnosis_result',},
  diagnosisOrgCode: {title: '诊断地点代码',order: 7,view: 'list', type: 'string',},
  deptCode: {title: '科室代码',order: 8,view: 'list', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
