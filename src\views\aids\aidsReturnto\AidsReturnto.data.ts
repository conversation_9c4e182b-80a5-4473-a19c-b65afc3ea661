import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '患者姓名',
    align:"center",
    dataIndex: 'pname'
   },
   {
    title: '患者性别',
    align:"center",
    dataIndex: 'pgender_dictText'
   },
   {
    title: '患者出生日期',
    align:"center",
    dataIndex: 'pbirth',
     customRender:({text}) =>{
       text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
       return text;
     },
   },
   {
    title: '患者年龄',
    align:"center",
    dataIndex: 'page'
   },
   {
    title: '患者身份证号',
    align:"center",
    dataIndex: 'pidcard'
   },
   {
    title: '患者联系方式',
    align:"center",
    dataIndex: 'pcontact'
   },
   {
    title: '患者户籍地址',
    align:"center",
    dataIndex: 'pdom'
   },
   {
    title: '患者现住地址',
    align:"center",
    dataIndex: 'pcurr'
   },
   {
    title: '检查结果',
    align:"center",
    dataIndex: 'checkresult'
   },
   {
    title: '用药记录',
    align:"center",
    dataIndex: 'medicalhistory'
   },
   {
    title: '治疗效果评估',
    align:"center",
    dataIndex: 'cureresult'
   },
   {
    title: '并发症监测',
    align:"center",
    dataIndex: 'syptom'
   },
   {
    title: '患者转归情况',
    align:"center",
    dataIndex: 'returnto'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
	{
      label: "患者姓名",
      field: 'pname',
      component: 'Input',
      //colProps: {span: 6},
 	},
	{
      label: "患者身份证号",
      field: 'pidcard',
      component: 'Input',
      //colProps: {span: 6},
 	},
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '患者姓名',
    field: 'pname',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入患者姓名!'},
          ];
     },
  },
  {
    label: '患者性别',
    field: 'pgender',
    component: 'JDictSelectTag',
    componentProps:{
      dictCode: 'aids_sex',
      type: 'radio',
    },
  },
  {
    label: '患者出生日期',
    field: 'pbirth',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '患者年龄',
    field: 'page',
    component: 'Input',
  },
  {
    label: '患者身份证号',
    field: 'pidcard',
    component: 'Input',
  },
  {
    label: '患者联系方式',
    field: 'pcontact',
    component: 'Input',
  },
  {
    label: '患者户籍地址',
    field: 'pdom',
    component: 'Input',
  },
  {
    label: '患者现住地址',
    field: 'pcurr',
    component: 'Input',
  },
  {
    label: '检查结果',
    field: 'checkresult',
    component: 'InputTextArea',
  },
  {
    label: '用药记录',
    field: 'medicalhistory',
    component: 'InputTextArea',
  },
  {
    label: '治疗效果评估',
    field: 'cureresult',
    component: 'InputTextArea',
  },
  {
    label: '并发症监测',
    field: 'syptom',
    component: 'InputTextArea',
  },
  {
    label: '患者转归情况',
    field: 'returnto',
    component: 'InputTextArea',
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  pname: {title: '患者姓名',order: 0,view: 'text', type: 'string',},
  pgender: {title: '患者性别',order: 1,view: 'list', type: 'aids_sex',},
  pbirth: {title: '患者出生日期',order: 2,view: 'date', type: 'string',},
  page: {title: '患者年龄',order: 3,view: 'text', type: 'string',},
  pidcard: {title: '患者身份证号',order: 4,view: 'text', type: 'string',},
  pcontact: {title: '患者联系方式',order: 5,view: 'text', type: 'string',},
  pdom: {title: '患者户籍地址',order: 6,view: 'text', type: 'string',},
  pcurr: {title: '患者现住地址',order: 7,view: 'text', type: 'string',},
  checkresult: {title: '检查结果',order: 8,view: 'textarea', type: 'string',},
  medicalhistory: {title: '用药记录',order: 9,view: 'textarea', type: 'string',},
  cureresult: {title: '治疗效果评估',order: 10,view: 'textarea', type: 'string',},
  syptom: {title: '并发症监测',order: 11,view: 'textarea', type: 'string',},
  returnto: {title: '患者转归情况',order: 12,view: 'textarea', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
