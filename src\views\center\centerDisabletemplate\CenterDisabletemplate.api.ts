import { defHttp } from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  list = '/centerDisabletemplate/centerDisabletemplate/list',
  save='/centerDisabletemplate/centerDisabletemplate/add',
  edit='/centerDisabletemplate/centerDisabletemplate/edit',
  deleteOne = '/centerDisabletemplate/centerDisabletemplate/delete',
  deleteBatch = '/centerDisabletemplate/centerDisabletemplate/deleteBatch',
  importExcel = '/centerDisabletemplate/centerDisabletemplate/importExcel',
  exportXls = '/centerDisabletemplate/centerDisabletemplate/exportXls',
  //禁用
  pushBotification = '/centerDisabletemplate/centerDisabletemplate/pushCotification',
  //启用
  pushBotificationsh = '/centerDisabletemplate/centerDisabletemplate/pushCotificationsh',  
}

/**
 * 导出api
 * @param params
 */
export const getExportUrl = Api.exportXls;

/**
 * 导入api
 */
export const getImportUrl = Api.importExcel;

/**
 * 列表接口
 * @param params
 */
export const list = (params) => defHttp.get({ url: Api.list, params });

/**
 * 删除单个
 * @param params
 * @param handleSuccess
 */
export const deleteOne = (params,handleSuccess) => {
  return defHttp.delete({url: Api.deleteOne, params}, {joinParamsToUrl: true}).then(() => {
    handleSuccess();
  });
}

/**
 * 批量删除
 * @param params
 * @param handleSuccess
 */
export const batchDelete = (params, handleSuccess) => {
  createConfirm({
    iconType: 'warning',
    title: '确认删除',
    content: '是否删除选中数据',
    okText: '确认',
    cancelText: '取消',
    onOk: () => {
      return defHttp.delete({url: Api.deleteBatch, data: params}, {joinParamsToUrl: true}).then(() => {
        handleSuccess();
      });
    }
  });
}

/**
 * 保存或者更新
 * @param params
 * @param isUpdate
 */
export const saveOrUpdate = (params, isUpdate) => {
  let url = isUpdate ? Api.edit : Api.save;
  return defHttp.post({ url: url, params }, { isTransformResponse: false });
}



//禁用
export const pushAotification = (id) => defHttp.post({ 
  url: Api.pushBotification, 
  data: { id }  // 使用 data 传递参数
});


//停用
export const pushAotificationsh = (id) => defHttp.post({ 
  url: Api.pushBotificationsh, 
  data: { id }  // 使用 data 传递参数
});