<template>
  <BasicModal v-bind="$attrs" @register="registerModal" :defaultFullscreen="true" destroyOnClose :title="title" :width="896" @ok="handleSubmit">
    <BasicForm @register="registerForm" ref="formRef"/>
    <!-- 子表单区域 -->
    <a-tabs v-model:activeKey="activeKey" animated @change="handleChangeTabs">
      <a-tab-pane tab="传染病报告卡艾滋病性病附卡" key="aidsRptMasculine" :forceRender="true">
        <AidsRptMasculineForm ref="aidsRptMasculineForm" :disabled="formDisabled"></AidsRptMasculineForm>
      </a-tab-pane>

    </a-tabs>
  </BasicModal>
</template>

<script lang="ts" setup>
    import {ref, computed, unref,reactive} from 'vue';
    import {BasicModal, useModalInner} from '/src/components/Modal';
    import {BasicForm, useForm} from '/src/components/Form';
    import { JVxeTable } from '/src/components/jeecg/JVxeTable'
    import { useJvxeMethod } from '/src/hooks/system/useJvxeMethods.ts'
    import AidsRptMasculineForm from './AidsRptMasculineForm.vue'
    import {formSchema} from '../InfectioncardAids.data';
    import {saveOrUpdate,aidsRptMasculineList} from '../InfectioncardAids.api';
    import { VALIDATE_FAILED } from '/src/utils/common/vxeUtils'
    // Emits声明
    const emit = defineEmits(['register','success']);
    const isUpdate = ref(true);
    const formDisabled = ref(false);
    const refKeys = ref(['aidsRptMasculine', ]);
    const activeKey = ref('aidsRptMasculine');
    const aidsRptMasculineForm = ref();
    const tableRefs = {};
    //表单配置
    const [registerForm, {setProps,resetFields, setFieldsValue, validate}] = useForm({
        //labelWidth: 150,
        schemas: formSchema,
        showActionButtonGroup: false,
         baseColProps: {span: 20},
                labelCol: {span: 9},
    });
     //表单赋值
    const [registerModal, {setModalProps, closeModal}] = useModalInner(async (data) => {
        //重置表单
        await reset();
        setModalProps({confirmLoading: false,showCancelBtn:data?.showFooter,showOkBtn:data?.showFooter});
        isUpdate.value = !!data?.isUpdate;
        formDisabled.value = !data?.showFooter;
        if (unref(isUpdate)) {
            //表单赋值
            await setFieldsValue({
                ...data.record,
            });
             aidsRptMasculineForm.value.initFormData(aidsRptMasculineList,data?.record?.id)
        }
        // 隐藏底部时禁用整个表单
       setProps({ disabled: !data?.showFooter })
    });
    //方法配置
    const [handleChangeTabs,handleSubmit,requestSubTableData,formRef] = useJvxeMethod(requestAddOrEdit,classifyIntoFormData,tableRefs,activeKey,refKeys,validateSubForm);

    //设置标题
    const title = computed(() => (!unref(isUpdate) ? '新增' : !unref(formDisabled) ? '编辑' : '详情'));

    async function reset(){
      await resetFields();
      activeKey.value = 'aidsRptMasculine';
      aidsRptMasculineForm.value.resetFields();
    }
    function classifyIntoFormData(allValues) {
         let main = Object.assign({}, allValues.formValue)
         return {
           ...main, // 展开
           aidsRptMasculineList: aidsRptMasculineForm.value.getFormData(),
         }
       }
     //校验所有一对一子表表单
     function validateSubForm(allValues){
         return new Promise((resolve,reject)=>{
             Promise.all([
                  aidsRptMasculineForm.value.validateForm(0),
             ]).then(() => {
                 resolve(allValues)
             }).catch(e => {
                 if (e.error === VALIDATE_FAILED) {
                     // 如果有未通过表单验证的子表，就自动跳转到它所在的tab
                     activeKey.value = e.index == null ? unref(activeKey) : refKeys.value[e.index]
                 } else {
                     console.error(e)
                 }
             })
         })
     }
    //表单提交事件
    async function requestAddOrEdit(values) {
        try {
            setModalProps({confirmLoading: true});
            if(values.apanagecode){
              let arr  = values.apanagecode.split(",")
              values.apanagecode = arr[arr.length-1]
            }
            if(values.addrcode){
              let arr  = values.addrcode.split(",")
              values.addrcode = arr[arr.length-1]
            }
            if(values.aidsRptMasculineList[0].addrcode){
              let arr  = values.aidsRptMasculineList[0].addrcode.split(",")
              values.aidsRptMasculineList[0].addrcode = arr[arr.length-1]
            }
            //提交表单
            await saveOrUpdate(values, isUpdate.value);
            //关闭弹窗
            closeModal();
            //刷新列表
            emit('success');
        } finally {
            setModalProps({confirmLoading: false});
        }
    }
</script>

<style lang="less" scoped>
	/** 时间和数字输入框样式 */
  :deep(.ant-input-number) {
    width: 100%;
  }

  :deep(.ant-calendar-picker) {
    width: 100%;
  }
</style>
