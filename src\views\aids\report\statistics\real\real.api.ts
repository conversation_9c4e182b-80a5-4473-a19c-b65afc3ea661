import {defHttp} from '/@/utils/http/axios';
import { useMessage } from "/@/hooks/web/useMessage";

const { createConfirm } = useMessage();

enum Api {
  zonelist='/reportstatistics/reportHivOntime/zonelist',
  agelist='/reportstatistics/reportHivOntime/agelist',
}
/**
 * 列表接口
 * @param params
 */
export const zonelist = (params) =>
  defHttp.get({url: Api.zonelist, params});
export const agelist = (params) =>
  defHttp.get({url: Api.agelist, params});
