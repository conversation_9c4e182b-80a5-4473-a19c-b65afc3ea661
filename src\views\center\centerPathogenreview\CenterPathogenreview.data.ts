import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
    title: '报告编号',
    align: "center",
    dataIndex: 'reportcode'
  },
  {
    title: '报告标题',
    align: "center",
    dataIndex: 'reporttitle'
  },
  {
    title: '模板类型',
    align: "center",
    dataIndex: 'modeltype_dictText'
  },
  {
    title: '报告状态',
    align: "center",
    dataIndex: 'reportstate_dictText'
  },
  {
    title: '审核人',
    align: "center",
    dataIndex: 'audituser'
  },
  {
    title: '审核状态',
    align: "center",
    dataIndex: 'auditstate_dictText'
  },
  {
    title: '审核时间',
    align: "center",
    dataIndex: 'audittime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
  },
  {
    title: '退回意见',
    align: "center",
    dataIndex: 'backreason'
  },
  {
    title: '附件',
    align: "center",
    dataIndex: 'file',
  },
];

// 高级查询数据
export const superQuerySchema = {
  reportcode: {title: '报告编号',order: 0,view: 'text', type: 'string',},
  reporttitle: {title: '报告标题',order: 1,view: 'text', type: 'string',},
  modeltype: {title: '模板类型',order: 2,view: 'list', type: 'string',dictCode: 'MODELD_TYPE',},
  reportstate: {title: '报告状态',order: 3,view: 'list', type: 'string',dictCode: 'BG_STATE',},
  audituser: {title: '审核人',order: 4,view: 'text', type: 'string',},
  auditstate: {title: '审核状态',order: 5,view: 'list', type: 'string',dictCode: 'AUDIT_STATE',},
  audittime: {title: '审核时间',order: 6,view: 'date', type: 'string',},
  backreason: {title: '退回意见',order: 7,view: 'text', type: 'string',},
  file: {title: '附件',order: 8,view: 'file', type: 'string',},
};
