<template>
  <div>
    <!--引用表格-->
   <BasicTable @register="registerTable"   :columns="columns"  :pagination="false">
     
         
    </BasicTable> 
  </div>
</template>

<script lang="ts" name="syxjbjcsj-diaJcSyxjbjcsj" setup>
  import {ref, reactive, computed, unref} from 'vue';
  import {BasicTable, useTable } from '/@/components/Table';
  import {useModal} from '/@/components/Modal';
  import { useListPage } from '/@/hooks/system/useListPage' 
  import {columns, searchFormSchema, superQuerySchema} from './firstfollowsta.data';
  import {list, deleteOne, batchDelete, getImportUrl,getExportUrl} from './firstfollowsta.api';
  import { downloadFile } from '/@/utils/common/renderUtils';
  import { useUserStore } from '/@/store/modules/user';
  const queryParam = reactive<any>({});
  const checkedKeys = ref<Array<string | number>>([]);
  const userStore = useUserStore();
  //注册model
  const [registerModal, {openModal}] = useModal();
  //注册table数据
  const { prefixCls,tableContext,onExportXls,onImportXls } = useListPage({
      tableProps:{
           title: 'HIV/AIDS流行病学调查（首次随访）情况统计',
           //api: list, 
           ellipsis:false,
           formConfig: {
              //labelWidth: 120,
              schemas: searchFormSchema
              
            }, 
            beforeFetch: (params) => {
              return Object.assign(params, queryParam);
            },
            showIndexColumn:false,//是否显示序号
            showActionColumn:false, //是哦福显示操作栏
      },
     
       exportConfig: {
            name:"HIV/AIDS流行病学调查（首次随访）情况统计",
            url: getExportUrl,
            params: queryParam,
          } 
  })

  const [registerTable, {reload},{ rowSelection, selectedRowKeys }] = tableContext

  // 高级查询配置
  const superQueryConfig = reactive(superQuerySchema);

  /**
   * 高级查询事件
   */
  function handleSuperQuery(params) {
    Object.keys(params).map((k) => {
      queryParam[k] = params[k];
    });
    reload();
  }
   
   /**
    * 成功回调
    */
  function handleSuccess() {
      (selectedRowKeys.value = []) && reload();
   }
 
    

</script>

<style scoped>
  :deep(.ant-picker),:deep(.ant-input-number){
    width: 100%;
  }
</style>
