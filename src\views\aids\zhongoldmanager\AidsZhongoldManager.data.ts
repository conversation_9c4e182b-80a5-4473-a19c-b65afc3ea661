import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '病例报告系统卡片ID',
    align:"center",
    dataIndex: 'bingliid'
   },
   {
    title: 'T04梅毒结果判断',
    align:"center",
    dataIndex: 't04_dictText'
   },
   {
    title: 'BED检测ODn值',
    align:"center",
    dataIndex: 'bedodn'
   },
   {
    title: 'A06样本来源',
    align:"center",
    dataIndex: 'a06_dictText'
   },
   {
    title: 'D02最近三个月，你与多少个小姐（卖淫妇女 ）发生过性行为？',
    align:"center",
    dataIndex: 'd02_dictText'
   },
   {
    title: 'B03b外籍',
    align:"center",
    dataIndex: 'b03b_dictText'
   },
   {
    title: 'A05调查日期',
    align:"center",
    dataIndex: 'a05',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'B04民族',
    align:"center",
    dataIndex: 'b04'
   },
   {
    title: '添加人',
    align:"center",
    dataIndex: 'adduser'
   },
   {
    title: '审核人',
    align:"center",
    dataIndex: 'auduser'
   },
   {
    title: '锁定状态',
    align:"center",
    dataIndex: 'sdstate'
   },
   {
    title: 'BED检测',
    align:"center",
    dataIndex: 'bedjc'
   },
   {
    title: 'b03a外省',
    align:"center",
    dataIndex: 'b03a'
   },
   {
    title: '添加地区',
    align:"center",
    dataIndex: 'addzone'
   },
   {
    title: 'F02你与同性发生过肛交性行为吗？',
    align:"center",
    dataIndex: 'f02_dictText'
   },
   {
    title: '亲和力检测结果',
    align:"center",
    dataIndex: 'qhjg'
   },
   {
    title: 'T03aHIV第一次ELISA初筛',
    align:"center",
    dataIndex: 't03a_dictText'
   },
   {
    title: '修改人',
    align:"center",
    dataIndex: 'modyuser'
   },
   {
    title: 'G02最近一年，你曾被诊断患过何种性病？（可多选）',
    align:"center",
    dataIndex: 'g02_dictText'
   },
   {
    title: 'T01本次调查是否采血？',
    align:"center",
    dataIndex: 't01_dictText'
   },
   {
    title: 'T04a梅毒ELISA初筛结果',
    align:"center",
    dataIndex: 't04a_dictText'
   },
   {
    title: '申请修改时间',
    align:"center",
    dataIndex: 'sqedittime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'A02哨点类型',
    align:"center",
    dataIndex: 'a02_dictText'
   },
   {
    title: '亲和力检测ODn值',
    align:"center",
    dataIndex: 'qhodn'
   },
   {
    title: 'T03bHIV第二次ELISA复检',
    align:"center",
    dataIndex: 't03b_dictText'
   },
   {
    title: 'C03与艾滋病病毒感染者一起吃饭会感染艾滋病吗？',
    align:"center",
    dataIndex: 'c03_dictText'
   },
   {
    title: '是否有效',
    align:"center",
    dataIndex: 'state'
   },
   {
    title: 'C06感染艾滋病病毒的妇女生下的小孩有可能得艾滋病吗？',
    align:"center",
    dataIndex: 'c06_dictText'
   },
   {
    title: '调查员签字',
    align:"center",
    dataIndex: 'inquiryname'
   },
   {
    title: '删除时间',
    align:"center",
    dataIndex: 'deletetime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'C09你认为自己感染上艾滋病病毒的风险有多高？ ',
    align:"center",
    dataIndex: 'c09_dictText'
   },
   {
    title: '申请修改状态',
    align:"center",
    dataIndex: 'sqstate'
   },
   {
    title: '亲和力检测',
    align:"center",
    dataIndex: 'qhjc'
   },
   {
    title: 'T03HIV抗体检测结果',
    align:"center",
    dataIndex: 't03_dictText'
   },
   {
    title: '修改地区',
    align:"center",
    dataIndex: 'modyzone'
   },
   {
    title: 'C08只与一个性伴发生性行为可以减少艾滋病的传播吗？',
    align:"center",
    dataIndex: 'c08_dictText'
   },
   {
    title: 'T03cHIV抗体确认试验结果',
    align:"center",
    dataIndex: 't03c_dictText'
   },
   {
    title: 'T05bHCV第二次ELISA复检',
    align:"center",
    dataIndex: 't05b_dictText'
   },
   {
    title: 'BED检测结果',
    align:"center",
    dataIndex: 'bedjg'
   },
   {
    title: 'T02b如果是，最早确证检测为阳性的时间',
    align:"center",
    dataIndex: 't02b_dictText'
   },
   {
    title: 'H03同伴教育？',
    align:"center",
    dataIndex: 'h03_dictText'
   },
   {
    title: 'H02社区药物维持治疗 /清洁针具提供/交换？',
    align:"center",
    dataIndex: 'h02_dictText'
   },
   {
    title: 'D02人数',
    align:"center",
    dataIndex: 'd02text'
   },
   {
    title: 'E02最近三个月，你与多少个临时性伴发生过性行为？  ',
    align:"center",
    dataIndex: 'e02_dictText'
   },
   {
    title: '哨点负责单位',
    align:"center",
    dataIndex: 'orgcode_dictText'
   },
   {
    title: '添加科室',
    align:"center",
    dataIndex: 'adddep'
   },
   {
    title: '督导员签字',
    align:"center",
    dataIndex: 'custodian'
   },
   {
    title: 'A06A样本来源-其他',
    align:"center",
    dataIndex: 'a06a_dictText'
   },
   {
    title: 'B02婚姻状况',
    align:"center",
    dataIndex: 'b02_dictText'
   },
   {
    title: '修改时间',
    align:"center",
    dataIndex: 'modytime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'A04问卷编号',
    align:"center",
    dataIndex: 'a04'
   },
   {
    title: 'C04输入带有艾滋病病毒的血液会得艾滋病吗？',
    align:"center",
    dataIndex: 'c04_dictText'
   },
   {
    title: '数据交换-数据来源',
    align:"center",
    dataIndex: 'datasource'
   },
   {
    title: '未做新发感染检测原因',
    align:"center",
    dataIndex: 'weixf'
   },
   {
    title: '监测对象征集单位',
    align:"center",
    dataIndex: 'potorgcode'
   },
   {
    title: 'B01a年龄',
    align:"center",
    dataIndex: 'b01a'
   },
   {
    title: '修改科室',
    align:"center",
    dataIndex: 'modydep'
   },
   {
    title: '报告地区',
    align:"center",
    dataIndex: 'zonecode_dictText'
   },
   {
    title: 'A03哨点所在地区行政编码',
    align:"center",
    dataIndex: 'a03_dictText'
   },
   {
    title: '审核状态',
    align:"center",
    dataIndex: 'audstate'
   },
   {
    title: '哨点所在地区',
    align:"center",
    dataIndex: 'potzonecode_dictText'
   },
   {
    title: 'C05与艾滋病病毒感染者共用注射器有可能得艾滋病吗？',
    align:"center",
    dataIndex: 'c05_dictText'
   },
   {
    title: 'F01你注射过毒品吗？',
    align:"center",
    dataIndex: 'f01_dictText'
   },
   {
    title: 'T02a是否是既往检测HIV抗体阳性',
    align:"center",
    dataIndex: 't02a_dictText'
   },
   {
    title: '填表日期',
    align:"center",
    dataIndex: 'filltime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '锁定时间',
    align:"center",
    dataIndex: 'sdtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'T04b梅毒RPR/TRUST复检结果',
    align:"center",
    dataIndex: 't04b_dictText'
   },
   {
    title: 'T05HCV抗体检测结果',
    align:"center",
    dataIndex: 't05_dictText'
   },
   {
    title: '添加机构',
    align:"center",
    dataIndex: 'addorg'
   },
   {
    title: 'E01最近三个月，你与临时性伴发生过性行为吗？',
    align:"center",
    dataIndex: 'e01_dictText'
   },
   {
    title: 'G01最近一年，你是否曾被诊断患过性病？',
    align:"center",
    dataIndex: 'g01_dictText'
   },
   {
    title: 'C01一个感染了艾滋病病毒的人能从外表上看出来吗？',
    align:"center",
    dataIndex: 'c01_dictText'
   },
   {
    title: 'T05aHCV第一次ELISA初筛',
    align:"center",
    dataIndex: 't05a_dictText'
   },
   {
    title: 'E02人数',
    align:"center",
    dataIndex: 'e02text'
   },
   {
    title: 'G02其它',
    align:"center",
    dataIndex: 'g02a'
   },
   {
    title: 'H01安全套宣传和发放/艾滋病咨询与检测 ?',
    align:"center",
    dataIndex: 'h01_dictText'
   },
   {
    title: 'B03户籍所在地',
    align:"center",
    dataIndex: 'b03'
   },
   {
    title: 'B06文化程度',
    align:"center",
    dataIndex: 'b06_dictText'
   },
   {
    title: 'B05在本地居住时间',
    align:"center",
    dataIndex: 'b05'
   },
   {
    title: 'D01最近三个月，你与小姐（卖淫妇女）发生过性行为吗？',
    align:"center",
    dataIndex: 'd01_dictText'
   },
   {
    title: '哨点识别码',
    align:"center",
    dataIndex: 'a01'
   },
   {
    title: 'I03你的最近一次检测是在什么时间？',
    align:"center",
    dataIndex: 'i03',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '修改机构',
    align:"center",
    dataIndex: 'modyorg'
   },
   {
    title: '数据交换-更新时间',
    align:"center",
    dataIndex: 'datamodytime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'C02蚊虫叮咬会传播艾滋病吗？',
    align:"center",
    dataIndex: 'c02_dictText'
   },
   {
    title: 'C07正确使用安全套可以减少艾滋病的传播吗？',
    align:"center",
    dataIndex: 'c07_dictText'
   },
   {
    title: 'I04你的最近一次检测结果是？',
    align:"center",
    dataIndex: 'i04_dictText'
   },
   {
    title: '删除人',
    align:"center",
    dataIndex: 'deleteuser'
   },
   {
    title: '添加时间',
    align:"center",
    dataIndex: 'addtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: 'B01出生年',
    align:"center",
    dataIndex: 'b01',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '审核时间',
    align:"center",
    dataIndex: 'audtime',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },

];
//查询数据
export const searchFormSchema: FormSchema[] = [
  {
    label: '报告地区',
    field: 'apanagecode',
    component: 'Zonecode',
    
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.rptorgcode=''
          //所属部门修改后更新负责部门下拉框数据
          updateSchema([
            {
              field: 'rptorgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: '报告单位',
    field: 'rptorgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '病例报告系统卡片ID',
    field: 'bingliid',
    component: 'Input',
  },
  {
    label: 'T04梅毒结果判断',
    field: 't04',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'BED检测ODn值',
    field: 'bedodn',
    component: 'Input',
  },
  {
    label: 'A06样本来源',
    field: 'a06',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_mas_dd_sampsrc",
        type: "radio"
     },
  },
  {
    label: 'D02最近三个月，你与多少个小姐（卖淫妇女 ）发生过性行为？',
    field: 'd02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques23",
        type: "radio"
     },
  },
  {
    label: 'B03b国籍',
    field: 'b03b',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"edr_nationality",
        type: "select"
     },
  },
  {
    label: 'A05调查日期',
    field: 'a05',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'B04民族',
    field: 'b04',
    component: 'Input',
  },
  {
    label: '添加人',
    field: 'adduser',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加人!'},
          ];
     },
  },
  {
    label: '审核人',
    field: 'auduser',
    component: 'Input',
  },
  {
    label: '锁定状态',
    field: 'sdstate',
    component: 'Input',
  },
  {
    label: 'BED检测',
    field: 'bedjc',
    component: 'Input',
  },
  {
    label: 'b03a外省',
    field: 'b03a',
    component: 'Input',
  },
  {
    label: '添加地区',
    field: 'addzone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加地区!'},
          ];
     },
  },
  {
    label: 'F02你与同性发生过肛交性行为吗？',
    field: 'f02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '亲和力检测结果',
    field: 'qhjg',
    component: 'Input',
  },
  {
    label: 'T03aHIV第一次ELISA初筛',
    field: 't03a',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '修改人',
    field: 'modyuser',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改人!'},
          ];
     },
  },
  {
    label: 'G02最近一年，你曾被诊断患过何种性病？（可多选）',
    field: 'g02',
    component: 'JSelectMultiple',
    componentProps:{
        dictCode:"aids_ques20",
        /* type: "radio" */
     },
  },
  {
    label: 'T01本次调查是否采血？',
    field: 't01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'T04a梅毒ELISA初筛结果',
    field: 't04a',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '申请修改时间',
    field: 'sqedittime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'A02哨点类型',
    field: 'a02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sentinel_type	",
        type: "radio"
     },
  },
  {
    label: '亲和力检测ODn值',
    field: 'qhodn',
    component: 'Input',
  },
  {
    label: 'T03bHIV第二次ELISA复检',
    field: 't03b',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'C03与艾滋病病毒感染者一起吃饭会感染艾滋病吗？',
    field: 'c03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '是否有效',
    field: 'state',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入是否有效!'},
          ];
     },
  },
  {
    label: 'C06感染艾滋病病毒的妇女生下的小孩有可能得艾滋病吗？',
    field: 'c06',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '调查员签字',
    field: 'inquiryname',
    component: 'Input',
  },
  {
    label: '删除时间',
    field: 'deletetime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'C09你认为自己感染上艾滋病病毒的风险有多高？ ',
    field: 'c09',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques14",
        type: "radio"
     },
  },
  {
    label: '申请修改状态',
    field: 'sqstate',
    component: 'Input',
  },
  {
    label: '亲和力检测',
    field: 'qhjc',
    component: 'Input',
  },
  {
    label: 'T03HIV抗体检测结果',
    field: 't03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '修改地区',
    field: 'modyzone',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改地区!'},
          ];
     },
  },
  {
    label: 'C08只与一个性伴发生性行为可以减少艾滋病的传播吗？',
    field: 'c08',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5	",
        type: "radio"
     },
  },
  {
    label: 'T03cHIV抗体确认试验结果',
    field: 't03c',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'T05bHCV第二次ELISA复检',
    field: 't05b',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'BED检测结果',
    field: 'bedjg',
    component: 'Input',
  },
  {
    label: 'T02b如果是，最早确证检测为阳性的时间',
    field: 't02b',
    component: 'Input',
  },
  {
    label: 'H03同伴教育？',
    field: 'h03',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'H02社区药物维持治疗 /清洁针具提供/交换？',
    field: 'h02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'D02人数',
    field: 'd02text',
    component: 'Input',
  },
  {
    label: 'E02最近三个月，你与多少个临时性伴发生过性行为？  ',
    field: 'e02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques23",
        type: "radio"
     },
  },
  {
    label: '哨点负责单位',
    field: 'orgcode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: '添加科室',
    field: 'adddep',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加科室!'},
          ];
     },
  },
  {
    label: '督导员签字',
    field: 'custodian',
    component: 'Input',
  },
  {
    label: 'A06A样本来源-其他',
    field: 'a06a',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques18",
        type: "radio"
     },
  },
  {
    label: 'B02婚姻状况',
    field: 'b02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques16	",
        type: "radio"
     },
  },
  {
    label: '修改时间',
    field: 'modytime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改时间!'},
          ];
     },
  },
  {
    label: 'A04问卷编号',
    field: 'a04',
    component: 'Input',
  },
  {
    label: 'C04输入带有艾滋病病毒的血液会得艾滋病吗？',
    field: 'c04',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '数据交换-数据来源',
    field: 'datasource',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入数据交换-数据来源!'},
          ];
     },
  },
  {
    label: '未做新发感染检测原因',
    field: 'weixf',
    component: 'Input',
  },
  {
    label: '监测对象征集单位',
    field: 'potorgcode',
    component: 'Input',
  },
  {
    label: 'B01a年龄',
    field: 'b01a',
    component: 'Input',
  },
  {
    label: '修改科室',
    field: 'modydep',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改科室!'},
          ];
     },
  },
  {
    label: '报告地区',
    field: 'apanagecode',
    component: 'Zonecode',
    
    componentProps: ({ formActionType, formModel }) => {
      return {
        onOptionsChange: (values) => {
          const { updateSchema } = formActionType;
          formModel.rptorgcode=''
          //所属部门修改后更新负责部门下拉框数据
          updateSchema([
            {
              field: 'rptorgcode',
              componentProps: { zonecode:values,value:''},
            },
          ]);
        },
      };
    },
  },
  {
    label: 'A03哨点所在地区行政编码',
    field: 'a03',
    component: 'Input',
  },
  {
    label: '审核状态',
    field: 'audstate',
    component: 'Input',
  },
  {
    label: '哨点所在地区',
    field: 'potzonecode',
    component: 'Orgcode',
    componentProps:{
      /* zonecode:'120101000', */
      orgType:'A,J'
    },
  },
  {
    label: 'C05与艾滋病病毒感染者共用注射器有可能得艾滋病吗？',
    field: 'c05',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques14",
        type: "radio"
     },
  },
  {
    label: 'F01你注射过毒品吗？',
    field: 'f01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'T02a是否是既往检测HIV抗体阳性',
    field: 't02a',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '填表日期',
    field: 'filltime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '锁定时间',
    field: 'sdtime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: 'T04b梅毒RPR/TRUST复检结果',
    field: 't04b',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'T05HCV抗体检测结果',
    field: 't05',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '添加机构',
    field: 'addorg',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加机构!'},
          ];
     },
  },
  {
    label: 'E01最近三个月，你与临时性伴发生过性行为吗？',
    field: 'e01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'G01最近一年，你是否曾被诊断患过性病？',
    field: 'g01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'C01一个感染了艾滋病病毒的人能从外表上看出来吗？',
    field: 'c01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'T05aHCV第一次ELISA初筛',
    field: 't05a',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: 'E02人数',
    field: 'e02text',
    component: 'Input',
  },
  {
    label: 'G02其它',
    field: 'g02a',
    component: 'Input',
  },
  {
    label: 'H01安全套宣传和发放/艾滋病咨询与检测 ?',
    field: 'h01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'B03户籍所在地',
    field: 'b03',
    component: 'Input',
  },
  {
    label: 'B06文化程度',
    field: 'b06',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques17",
        type: "radio"
     },
  },
  {
    label: 'B05在本地居住时间',
    field: 'b05',
    component: 'Input',
  },
  {
    label: 'D01最近三个月，你与小姐（卖淫妇女）发生过性行为吗？',
    field: 'd01',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: '哨点识别码',
    field: 'a01',
    component: 'Input',
  },
  {
    label: 'I03你的最近一次检测是在什么时间？',
    field: 'i03',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '修改机构',
    field: 'modyorg',
    component: 'Input',
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入修改机构!'},
          ];
     },
  },
  {
    label: '数据交换-更新时间',
    field: 'datamodytime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入数据交换-更新时间!'},
          ];
     },
  },
  {
    label: 'C02蚊虫叮咬会传播艾滋病吗？',
    field: 'c02',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'C07正确使用安全套可以减少艾滋病的传播吗？',
    field: 'c07',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_ques5",
        type: "radio"
     },
  },
  {
    label: 'I04你的最近一次检测结果是？',
    field: 'i04',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_results",
        type: "radio"
     },
  },
  {
    label: '删除人',
    field: 'deleteuser',
    component: 'Input',
  },
  {
    label: '添加时间',
    field: 'addtime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
    dynamicRules: ({model,schema}) => {
          return [
                 { required: true, message: '请输入添加时间!'},
          ];
     },
  },
  {
    label: 'B01出生年',
    field: 'b01',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '审核时间',
    field: 'audtime',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },

	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  t04: {title: 'T04梅毒结果判断',order: 0,view: 'radio', type: 'string',},
  bedodn: {title: 'BED检测ODn值',order: 1,view: 'text', type: 'string',},
  a06: {title: 'A06样本来源',order: 2,view: 'radio', type: 'string',},
  d02: {title: 'D02最近三个月，你与多少个小姐（卖淫妇女 ）发生过性行为？',order: 3,view: 'radio', type: 'string',},
  b03b: {title: 'B03b外籍',order: 4,view: 'radio', type: 'string',},
  a05: {title: 'A05调查日期',order: 5,view: 'date', type: 'string',},
  b04: {title: 'B04民族',order: 6,view: 'text', type: 'string',},
  adduser: {title: '添加人',order: 7,view: 'text', type: 'string',},
  auduser: {title: '审核人',order: 8,view: 'text', type: 'string',},
  sdstate: {title: '锁定状态',order: 9,view: 'text', type: 'string',},
  bedjc: {title: 'BED检测',order: 10,view: 'text', type: 'string',},
  b03a: {title: 'b03a外省',order: 11,view: 'text', type: 'string',},
  addzone: {title: '添加地区',order: 12,view: 'text', type: 'string',},
  f02: {title: 'F02你与同性发生过肛交性行为吗？',order: 13,view: 'radio', type: 'string',},
  qhjg: {title: '亲和力检测结果',order: 14,view: 'text', type: 'string',},
  t03a: {title: 'T03aHIV第一次ELISA初筛',order: 15,view: 'radio', type: 'string',},
  modyuser: {title: '修改人',order: 16,view: 'text', type: 'string',},
  g02: {title: 'G02最近一年，你曾被诊断患过何种性病？（可多选）',order: 17,view: 'radio', type: 'string',},
  t01: {title: 'T01本次调查是否采血？',order: 18,view: 'radio', type: 'string',},
  t04a: {title: 'T04a梅毒ELISA初筛结果',order: 19,view: 'radio', type: 'string',},
  sqedittime: {title: '申请修改时间',order: 20,view: 'date', type: 'string',},
  a02: {title: 'A02哨点类型',order: 21,view: 'radio', type: 'string',},
  qhodn: {title: '亲和力检测ODn值',order: 22,view: 'text', type: 'string',},
  t03b: {title: 'T03bHIV第二次ELISA复检',order: 23,view: 'radio', type: 'string',},
  c03: {title: 'C03与艾滋病病毒感染者一起吃饭会感染艾滋病吗？',order: 24,view: 'radio', type: 'string',},
  state: {title: '是否有效',order: 25,view: 'text', type: 'string',},
  c06: {title: 'C06感染艾滋病病毒的妇女生下的小孩有可能得艾滋病吗？',order: 26,view: 'radio', type: 'string',},
  inquiryname: {title: '调查员签字',order: 27,view: 'text', type: 'string',},
  deletetime: {title: '删除时间',order: 28,view: 'date', type: 'string',},
  c09: {title: 'C09你认为自己感染上艾滋病病毒的风险有多高？ ',order: 29,view: 'radio', type: 'string',},
  sqstate: {title: '申请修改状态',order: 30,view: 'text', type: 'string',},
  qhjc: {title: '亲和力检测',order: 31,view: 'text', type: 'string',},
  t03: {title: 'T03HIV抗体检测结果',order: 32,view: 'radio', type: 'string',},
  modyzone: {title: '修改地区',order: 33,view: 'text', type: 'string',},
  c08: {title: 'C08只与一个性伴发生性行为可以减少艾滋病的传播吗？',order: 34,view: 'radio', type: 'string',},
  t03c: {title: 'T03cHIV抗体确认试验结果',order: 35,view: 'radio', type: 'string',},
  t05b: {title: 'T05bHCV第二次ELISA复检',order: 36,view: 'radio', type: 'string',},
  bedjg: {title: 'BED检测结果',order: 37,view: 'text', type: 'string',},
  t02b: {title: 'T02b如果是，最早确证检测为阳性的时间',order: 38,view: 'radio', type: 'string',},
  h03: {title: 'H03同伴教育？',order: 39,view: 'radio', type: 'string',},
  h02: {title: 'H02社区药物维持治疗 /清洁针具提供/交换？',order: 40,view: 'radio', type: 'string',},
  d02text: {title: 'D02人数',order: 41,view: 'text', type: 'string',},
  e02: {title: 'E02最近三个月，你与多少个临时性伴发生过性行为？  ',order: 42,view: 'radio', type: 'string',},
  orgcode: {title: '哨点负责单位',order: 43,view: 'list', type: 'string',},
  adddep: {title: '添加科室',order: 44,view: 'text', type: 'string',},
  custodian: {title: '督导员签字',order: 45,view: 'text', type: 'string',},
  a06a: {title: 'A06A样本来源-其他',order: 46,view: 'radio', type: 'string',},
  b02: {title: 'B02婚姻状况',order: 47,view: 'radio', type: 'string',},
  modytime: {title: '修改时间',order: 48,view: 'date', type: 'string',},
  a04: {title: 'A04问卷编号',order: 49,view: 'text', type: 'string',},
  c04: {title: 'C04输入带有艾滋病病毒的血液会得艾滋病吗？',order: 50,view: 'radio', type: 'string',},
  datasource: {title: '数据交换-数据来源',order: 51,view: 'text', type: 'string',},
  weixf: {title: '未做新发感染检测原因',order: 52,view: 'text', type: 'string',},
  potorgcode: {title: '监测对象征集单位',order: 53,view: 'text', type: 'string',},
  b01a: {title: 'B01a年龄',order: 54,view: 'text', type: 'string',},
  modydep: {title: '修改科室',order: 55,view: 'text', type: 'string',},
  zonecode: {title: '报告地区',order: 56,view: 'list', type: 'string',},
  a03: {title: 'A03哨点所在地区行政编码',order: 57,view: 'list', type: 'string',},
  audstate: {title: '审核状态',order: 58,view: 'text', type: 'string',},
  potzonecode: {title: '哨点所在地区',order: 59,view: 'list', type: 'string',},
  c05: {title: 'C05与艾滋病病毒感染者共用注射器有可能得艾滋病吗？',order: 60,view: 'radio', type: 'string',},
  f01: {title: 'F01你注射过毒品吗？',order: 61,view: 'radio', type: 'string',},
  t02a: {title: 'T02a是否是既往检测HIV抗体阳性',order: 62,view: 'radio', type: 'string',},
  filltime: {title: '填表日期',order: 63,view: 'date', type: 'string',},
  sdtime: {title: '锁定时间',order: 64,view: 'date', type: 'string',},
  t04b: {title: 'T04b梅毒RPR/TRUST复检结果',order: 65,view: 'radio', type: 'string',},
  t05: {title: 'T05HCV抗体检测结果',order: 66,view: 'radio', type: 'string',},
  addorg: {title: '添加机构',order: 67,view: 'text', type: 'string',},
  e01: {title: 'E01最近三个月，你与临时性伴发生过性行为吗？',order: 68,view: 'radio', type: 'string',},
  g01: {title: 'G01最近一年，你是否曾被诊断患过性病？',order: 69,view: 'radio', type: 'string',},
  c01: {title: 'C01一个感染了艾滋病病毒的人能从外表上看出来吗？',order: 70,view: 'radio', type: 'string',},
  t05a: {title: 'T05aHCV第一次ELISA初筛',order: 71,view: 'radio', type: 'string',},
  e02text: {title: 'E02人数',order: 72,view: 'text', type: 'string',},
  g02a: {title: 'G02其它',order: 73,view: 'text', type: 'string',},
  h01: {title: 'H01安全套宣传和发放/艾滋病咨询与检测 ?',order: 74,view: 'radio', type: 'string',},
  b03: {title: 'B03户籍所在地',order: 75,view: 'text', type: 'string',},
  b06: {title: 'B06文化程度',order: 76,view: 'radio', type: 'string',},
  b05: {title: 'B05在本地居住时间',order: 77,view: 'text', type: 'string',},
  d01: {title: 'D01最近三个月，你与小姐（卖淫妇女）发生过性行为吗？',order: 78,view: 'radio', type: 'string',},
  a01: {title: '哨点识别码',order: 79,view: 'text', type: 'string',},
  i03: {title: 'I03你的最近一次检测是在什么时间？',order: 80,view: 'date', type: 'string',},
  modyorg: {title: '修改机构',order: 81,view: 'text', type: 'string',},
  datamodytime: {title: '数据交换-更新时间',order: 82,view: 'date', type: 'string',},
  c02: {title: 'C02蚊虫叮咬会传播艾滋病吗？',order: 83,view: 'radio', type: 'string',},
  c07: {title: 'C07正确使用安全套可以减少艾滋病的传播吗？',order: 84,view: 'radio', type: 'string',},
  i04: {title: 'I04你的最近一次检测结果是？',order: 85,view: 'radio', type: 'string',},
  deleteuser: {title: '删除人',order: 86,view: 'text', type: 'string',},
  addtime: {title: '添加时间',order: 87,view: 'date', type: 'string',},
  b01: {title: 'B01出生年',order: 88,view: 'date', type: 'string',},
  audtime: {title: '审核时间',order: 89,view: 'date', type: 'string',},
  bingliid: {title: '病例报告系统卡片ID',order: 90,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
