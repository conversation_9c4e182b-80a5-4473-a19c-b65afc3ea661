import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
  {
   title: '转入地区',
   align:"center",
   dataIndex: 'referralZonecode_dictText'
  },
  {
   title: '转入机构',
   align:"center",
   dataIndex: 'referralOrgcode_dictText'
  },
   {
    title: '抗病毒治疗号',
    align:"center",
    dataIndex: 'avtn'
   },
   {
    title: '患者姓名',
    align:"center",
    dataIndex: 'name'
   },
   {
    title: '身份证件类型',
    align:"center",
    dataIndex: 'identityType_dictText'
   },
   {
    title: '身份证件号码',
    align:"center",
    dataIndex: 'identity'
   },
   {
    title: '联系方式',
    align:"center",
    dataIndex: 'phone'
   },
   {
    title: '现住地区',
    align:"center",
    dataIndex: 'livingaddresscode_dictText'
   },
   {
    title: '现住详细地址',
    align:"center",
    dataIndex: 'livingaddressdetails'
   },
   {
    title: '户籍地址',
    align:"center",
    dataIndex: 'domicileaddresscode_dictText'
   },
   {
    title: '户籍详细地址',
    align:"center",
    dataIndex: 'domicileadrressdetails'
   },
   {
    title: '出生日期',
    align:"center",
    dataIndex: 'birthDate',
    customRender:({text}) =>{
      text = !text ? "" : (text.length > 10 ? text.substr(0,10) : text);
      return text;
    },
   },
   {
    title: '年龄',
    align:"center",
    dataIndex: 'age'
   },
   {
    title: '年龄单位',
    align:"center",
    dataIndex: 'ageUnit_dictText'
   },
   {
    title: '性别',
    align:"center",
    dataIndex: 'sex_dictText'
   },
   {
    title: '婚姻状况',
    align:"center",
    dataIndex: 'maritalstatuscode_dictText'
   },
   {
    title: '治疗地区',
    align:"center",
    dataIndex: 'firstZonecode_dictText'
   },
   {
    title: '治疗机构',
    align:"center",
    dataIndex: 'firstOrgcode_dictText'
   },
   {
    title: '转诊原因',
    align:"center",
    dataIndex: 'referralReason'
   },
   {
    title: '转诊状态',
    align:"center",
    dataIndex: 'reasonState_dictText'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [

 {
     label: "转入地区",
     field: 'referralZonecode',
     component:'Zonecode',
     /* defaultValue:'130100000'  ,  */
      componentProps: ({ formActionType, formModel }) => {
            return {
              allowClear:false,
              onOptionsChange: (values) => {
                const { updateSchema } = formActionType;
                   formModel.referralOrgcode=''
                   //所属部门修改后更新负责部门下拉框数据
                   updateSchema([
                     {
                       field: 'referralOrgcode',
                       componentProps: { zonecode:values,value:''},
                     },
                   ]);
              },
            };
          },
     //colProps: {span: 6},
 },
 {
     label: "转入机构",
     field: 'referralOrgcode',
     component:'Orgcode',
     componentProps:{
       /* zonecode:'120101000', */
       orgType:'A,J'
     }  
 },
 {
     label: "抗病毒治疗号",
     field: 'avtn',
     component: 'Input',
     //colProps: {span: 6},
 },
 {
     label: "治疗地区",
     field: 'firstZonecode',
     component:'Zonecode',
     /* defaultValue:'130100000'  ,  */
      componentProps: ({ formActionType, formModel }) => {
            return {
              allowClear:false,
              onOptionsChange: (values) => {
                const { updateSchema } = formActionType;
                   formModel.firstOrgcode=''
                   //所属部门修改后更新负责部门下拉框数据
                   updateSchema([
                     {
                       field: 'firstOrgcode',
                       componentProps: { zonecode:values,value:''},
                     },
                   ]);
              },
            };
          },
     //colProps: {span: 6},
 },
 {
     label: "治疗机构",
     field: 'firstOrgcode',
     component:'Orgcode',
     componentProps:{
       /* zonecode:'120101000', */
       orgType:'A,J'
     }  
 },

	{
      label: "患者姓名",
      field: 'name',
      component: 'Input',
      //colProps: {span: 6},
 	},
    {
      label: "创建日期",
      field: "createTime",
      component: 'RangePicker',
      componentProps: {
          valueType: 'Date',
          showTime:true
      },
      //colProps: {span: 6},
	},
];
//表单数据
export const formSchema: FormSchema[] = [
 {
     label: "治疗地区",
     field: 'firstZonecode',
     component:'Zonecode',
     /* defaultValue:'130100000'  ,  */
      componentProps: ({ formActionType, formModel }) => {
            return {
              allowClear:false,
              onOptionsChange: (values) => {
                const { updateSchema } = formActionType;
                   formModel.firstOrgcode=''
                   //所属部门修改后更新负责部门下拉框数据
                   updateSchema([
                     {
                       field: 'firstOrgcode',
                       componentProps: { zonecode:values,value:''},
                     },
                   ]);
              },
            };
          },
     //colProps: {span: 6},
 },
 {
     label: "治疗机构",
     field: 'firstOrgcode',
     component:'Orgcode',
     componentProps:{
       /* zonecode:'120101000', */
       orgType:'A,J'
     }  
 },
  
  {
    label: '抗病毒治疗号',
    field: 'avtn',
    component: 'Input',
  },
  {
    label: '患者姓名',
    field: 'name',
    component: 'Input',
  },
  {
    label: '身份证件类型',
    field: 'identityType',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_identity_type"
     },
  },
  {
    label: '身份证件号码',
    field: 'identity',
    component: 'Input',
  },
  {
    label: '联系方式',
    field: 'phone',
    component: 'Input',
  },
 {
     label: "现住地区",
     field: 'livingaddresscode',
     component:'Zonecode',
     /* defaultValue:'130100000'  ,  */
      componentProps: ({ formActionType, formModel }) => {
            return {
              allowClear:false,
              onOptionsChange: (values) => {
                const { updateSchema } = formActionType;
                   /* formModel.referralOrgcode=''
                   //所属部门修改后更新负责部门下拉框数据
                   updateSchema([
                     {
                       field: 'referralOrgcode',
                       componentProps: { zonecode:values,value:''},
                     },
                   ]); */
              },
            };
          },
     //colProps: {span: 6},
 },
  {
    label: '现住详细地址',
    field: 'livingaddressdetails',
    component: 'Input',
  },
 {
     label: "户籍地址",
     field: 'domicileaddresscode',
     component:'Zonecode',
     /* defaultValue:'130100000'  ,  */
      componentProps: ({ formActionType, formModel }) => {
            return {
              allowClear:false,
              onOptionsChange: (values) => {
                const { updateSchema } = formActionType;
                   /* formModel.referralOrgcode=''
                   //所属部门修改后更新负责部门下拉框数据
                   updateSchema([
                     {
                       field: 'referralOrgcode',
                       componentProps: { zonecode:values,value:''},
                     },
                   ]); */
              },
            };
          },
     //colProps: {span: 6},
 },
  {
    label: '户籍详细地址',
    field: 'domicileadrressdetails',
    component: 'Input',
  },
  {
    label: '出生日期',
    field: 'birthDate',
    component: 'DatePicker',
    componentProps: {
      valueFormat: 'YYYY-MM-DD'
    },
  },
  {
    label: '年龄',
    field: 'age',
    component: 'Input',
  },
  {
    label: '年龄单位',
    field: 'ageUnit',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_age_unit",
        type: "radio"
     },
  },
  {
    label: '性别',
    field: 'sex',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_sex",
        type: "radio"
     },
  },
  {
    label: '婚姻状况',
    field: 'maritalstatuscode',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_report_marital",
        type: "radio"
     },
  },
 {
     label: "转入地区",
     field: 'referralZonecode',
     component:'Zonecode',
     /* defaultValue:'130100000'  ,  */
      componentProps: ({ formActionType, formModel }) => {
            return {
              allowClear:false,
              onOptionsChange: (values) => {
                const { updateSchema } = formActionType;
                   formModel.referralOrgcode=''
                   //所属部门修改后更新负责部门下拉框数据
                   updateSchema([
                     {
                       field: 'referralOrgcode',
                       componentProps: { zonecode:values,value:''},
                     },
                   ]);
              },
            };
          },
     //colProps: {span: 6},
 },
 {
     label: "转入机构",
     field: 'referralOrgcode',
     component:'Orgcode',
     componentProps:{
       /* zonecode:'120101000', */
       orgType:'A,J'
     }  
 },
  {
    label: '转诊原因',
    field: 'referralReason',
    component: 'Input',
  },
  {
    label: '转诊状态',
    field: 'reasonState',
    component: 'JDictSelectTag',
    componentProps:{
        dictCode:"aids_zhiliaoreferral_state"
     },
  },
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  firstZonecode: {title: '治疗地区',order: 1,view: 'text', type: 'string',},
  firstOrgcode: {title: '治疗机构',order: 2,view: 'text', type: 'string',},
  avtn: {title: '抗病毒治疗号',order: 3,view: 'text', type: 'string',},
  name: {title: '患者姓名',order: 4,view: 'text', type: 'string',},
  identityType: {title: '身份证件类型',order: 5,view: 'list', type: 'string',dictCode: 'aids_identity_type',},
  identity: {title: '身份证件号码',order: 6,view: 'text', type: 'string',},
  phone: {title: '联系方式',order: 7,view: 'text', type: 'string',},
  livingaddresscode: {title: '现住地区',order: 8,view: 'text', type: 'string',},
  livingaddressdetails: {title: '现住详细地址',order: 9,view: 'text', type: 'string',},
  domicileaddresscode: {title: '户籍地址',order: 10,view: 'text', type: 'string',},
  domicileadrressdetails: {title: '户籍详细地址',order: 11,view: 'text', type: 'string',},
  birthDate: {title: '出生日期',order: 12,view: 'date', type: 'string',},
  age: {title: '年龄',order: 13,view: 'text', type: 'string',},
  ageUnit: {title: '年龄单位',order: 14,view: 'radio', type: 'string',dictCode: 'aids_age_unit',},
  sex: {title: '性别',order: 15,view: 'radio', type: 'string',dictCode: 'aids_sex',},
  maritalstatuscode: {title: '婚姻状况',order: 16,view: 'radio', type: 'string',dictCode: 'aids_report_marital',},
  referralZonecode: {title: '转入地区',order: 17,view: 'text', type: 'string',},
  referralOrgcode: {title: '转入机构',order: 18,view: 'text', type: 'string',},
  referralReason: {title: '转诊原因',order: 19,view: 'text', type: 'string',},
  reasonState: {title: '转诊状态',order: 20,view: 'list', type: 'string',dictCode: 'aids_zhiliaoreferral_state',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
