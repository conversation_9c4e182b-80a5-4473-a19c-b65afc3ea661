<template>
  <j-modal 
    :title="title" 
    :width="width" 
    :visible="visible" 
    @ok="handleOk" 
    :okButtonProps="{ class: { 'jee-hidden': disableSubmit } }" 
    @cancel="handleCancel" 
    cancelText="关闭"
  >
    <CenterQuerytemplateForm 
      ref="registerForm" 
      @ok="submitCallback" 
      :formDisabled="disableSubmit" 
      :formBpm="false"
    ></CenterQuerytemplateForm>
  </j-modal>
</template>

<script lang="ts" setup>
  import { ref, nextTick, defineExpose } from 'vue';
  import CenterQuerytemplateForm from './CenterQuerytemplateForm.vue';
  import JModal from '/@/components/Modal/src/JModal/JModal.vue';
  
  const title = ref<string>('');
  const width = ref<number>(800);
  const visible = ref<boolean>(false);
  const disableSubmit = ref<boolean>(false);
  const registerForm = ref();
  const emit = defineEmits(['register', 'success']);

  function add() {
    title.value = '新增';
    visible.value = true;
    nextTick(() => {
      registerForm.value?.add();
    });
  }
  
  function edit(record) {
    title.value = disableSubmit.value ? '详情' : '编辑';
    visible.value = true;
    nextTick(() => {
      registerForm.value?.edit(record);
    });
  }
  
  function handleOk() {
    registerForm.value?.submitForm(); // 现在可以正确调用这个方法
  }

  function submitCallback() {
    handleCancel();
    emit('success');
  }

  function handleCancel() {
    visible.value = false;
  }

  defineExpose({
    add,
    edit,
    disableSubmit,
  });
</script>