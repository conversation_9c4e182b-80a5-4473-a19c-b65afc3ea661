<template>
  <a-spin :spinning="confirmLoading">
    <JFormContainer :disabled="disabled">
      <template #detail>
        <a-form ref="formRef" class="antd-modal-form" :labelCol="labelCol" :wrapperCol="wrapperCol">
          <a-row>
						<a-col :span="12">
							<a-form-item label="病原体类型" v-bind="validateInfos.pathogentype">
								<a-input v-model:value="formData.pathogentype" placeholder="请输入病原体类型"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="样本来源" v-bind="validateInfos.samplesource">
								<a-input v-model:value="formData.samplesource" placeholder="请输入样本来源"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="样本采集日期" v-bind="validateInfos.samplecollectiondate">
								<a-date-picker placeholder="请选择样本采集日期"  v-model:value="formData.samplecollectiondate" value-format="YYYY-MM-DD"  style="width: 100%"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="检测方法" v-bind="validateInfos.testmethod">
								<a-input v-model:value="formData.testmethod" placeholder="请输入检测方法"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="检测结果" v-bind="validateInfos.testresult">
								<a-input v-model:value="formData.testresult" placeholder="请输入检测结果"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="检测日期" v-bind="validateInfos.testdate">
								<a-date-picker placeholder="请选择检测日期"  v-model:value="formData.testdate" value-format="YYYY-MM-DD"  style="width: 100%"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="病原体亚型" v-bind="validateInfos.pathogen">
								<a-input v-model:value="formData.pathogen" placeholder="请输入病原体亚型"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="抗微生物药物敏感性" v-bind="validateInfos.antimicrobi">
								<a-input v-model:value="formData.antimicrobi" placeholder="请输入抗微生物药物敏感性"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="机构代码" v-bind="validateInfos.reportingorgid">
								<a-input v-model:value="formData.reportingorgid" placeholder="请输入机构代码"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="生成人姓名" v-bind="validateInfos.reportername">
								<a-input v-model:value="formData.reportername" placeholder="请输入生成人姓名"  allow-clear ></a-input>
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="报告状态" v-bind="validateInfos.state">
								<j-dict-select-tag v-model:value="formData.state" dictCode="BG_STATE" placeholder="请选择报告状态"  allow-clear />
							</a-form-item>
						</a-col>
						<a-col :span="12">
							<a-form-item label="报告生成日期" v-bind="validateInfos.reportdate">
								<a-date-picker placeholder="请选择报告生成日期"  v-model:value="formData.reportdate" value-format="YYYY-MM-DD"  style="width: 100%"  allow-clear />
							</a-form-item>
						</a-col>



                        <!-- 附件区域，根据generation值显示或隐藏 -->
            <a-col :span="12" v-if="showAttachment">
              <a-form-item label="报告生成" v-bind="validateInfos.file">
                <j-upload v-model:value="formData.file"   ></j-upload>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
      </template>
    </JFormContainer>
  </a-spin>
</template>

<script lang="ts" setup>
  import JUpload from '/@/components/Form/src/jeecg/components/JUpload/JUpload.vue';
  import { ref, reactive, defineExpose, nextTick, defineProps, computed, onMounted } from 'vue';
  import { defHttp } from '/@/utils/http/axios';
  import { useMessage } from '/@/hooks/web/useMessage';
  import JDictSelectTag from '/@/components/Form/src/jeecg/components/JDictSelectTag.vue';
  import { getValueType } from '/@/utils';
  import { saveOrUpdate } from '../CenterPathogen.api';
  import { Form } from 'ant-design-vue';
  import JFormContainer from '/@/components/Form/src/container/JFormContainer.vue';
  
  const props = defineProps({
    formDisabled: { type: Boolean, default: false },
    formData: { type: Object, default: () => ({})},
    formBpm: { type: Boolean, default: true }
  });
  const formRef = ref();
  const useForm = Form.useForm;
  const emit = defineEmits(['register', 'ok']);
  const formData = reactive<Record<string, any>>({
    id: '',
    pathogentype: '',   
    samplesource: '',   
    samplecollectiondate: '',   
    testmethod: '',   
    testresult: '',   
    testdate: '',   
    pathogen: '',   
    antimicrobi: '',   
    reportingorgid: '',   
    reportername: '',   
    state: '',   
    reportdate: '',   
    file: '',   
  });
  const { createMessage } = useMessage();
  const labelCol = ref<any>({ xs: { span: 24 }, sm: { span: 5 } });
  const wrapperCol = ref<any>({ xs: { span: 24 }, sm: { span: 16 } });
  const confirmLoading = ref<boolean>(false);
  // 控制附件区域显示的变量
  const showAttachment = ref<boolean>(false);
   //表单验证
  const validatorRules = reactive({
    pathogentype: [{ required: true, message: '请输入病原体类型!'},],
    samplecollectiondate: [{ required: true, message: '请输入样本采集日期!'},],
    testdate: [{ required: true, message: '请输入检测日期!'},],
    state: [{ required: true, message: '请输入报告状态!'},],
  });
  const { resetFields, validate, validateInfos } = useForm(formData, validatorRules, { immediate: false });

  // 表单禁用
  const disabled = computed(()=>{
    if(props.formBpm === true){
      if(props.formData.disabled === false){
        return false;
      }else{
        return true;
      }
    }
    return props.formDisabled;
  });

  
  /**
   * 新增
   */
  function add() {
    edit({});
  }

  /**
   * 编辑
   */
  function edit(record) {
    nextTick(() => {
      resetFields();
      const tmpData = {};
      Object.keys(formData).forEach((key) => {
        if(record.hasOwnProperty(key)){
          tmpData[key] = record[key]
        }
      })
      //赋值
      Object.assign(formData, tmpData);

        // 根据generation值设置附件区域显示状态
     if(record.generation === undefined|| record.generation === "1"){
        showAttachment.value = false;
      } else if(record.generation === '2'){
        showAttachment.value = true;
      }
    });
  }

  /**
   * 提交数据
   */
  async function submitForm() {
    // 触发表单验证
    await validate();
    confirmLoading.value = true;
    const isUpdate = ref<boolean>(false);
    //时间格式化
    let model = formData;
    if (model.id) {
      isUpdate.value = true;
    }
    //循环数据
    for (let data in model) {
      //如果该数据是数组并且是字符串类型
      if (model[data] instanceof Array) {
        let valueType = getValueType(formRef.value.getProps, data);
        //如果是字符串类型的需要变成以逗号分割的字符串
        if (valueType === 'string') {
          model[data] = model[data].join(',');
        }
      }
    }
    await saveOrUpdate(model, isUpdate.value)
      .then((res) => {
        if (res.success) {
          createMessage.success(res.message);
          emit('ok');
        } else {
          createMessage.warning(res.message);
        }
      })
      .finally(() => {
        confirmLoading.value = false;
      });
  }


  defineExpose({
    add,
    edit,
    submitForm,
  });
</script>

<style lang="less" scoped>
  .antd-modal-form {
    padding: 14px;
  }
</style>
