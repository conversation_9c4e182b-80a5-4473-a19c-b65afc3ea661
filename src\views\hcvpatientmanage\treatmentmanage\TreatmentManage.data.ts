import {BasicColumn} from '/@/components/Table';
import {FormSchema} from '/@/components/Table';
import { rules} from '/@/utils/helper/validator';
import { render } from '/@/utils/common/renderUtils';
import { getWeekMonthQuarterYear } from '/@/utils';
//列表数据
export const columns: BasicColumn[] = [
   {
    title: '卡片编号',
    align:"center",
    dataIndex: 'cardNum'
   },
   {
     title: '治疗机构名称',
     align:"center",
     dataIndex: 'treatOrg'
   },
   {
    title: '患者姓名',
    align:"center",
    dataIndex: 'patientName'
   },
   {
     title: '有效证件号',
     align:"center",
     dataIndex: 'idCard'
   },
   {
    title: '开始治疗日期',
    align:"center",
    dataIndex: 'treatBeginTime'
   },
   {
    title: '既往抗病毒治疗情况',
    align:"center",
    dataIndex: 'treatCondition_dictText'
   },
   {
    title: '起始治疗方案',
    align:"center",
    dataIndex: 'treatMethod_dictText'
   },
  {
   title: '责任医生',
   align:"center",
   dataIndex: 'respDoctor'
  },
   {
    title: '填表日期',
    align:"center",
    dataIndex: 'fillDate'
   },
   {
    title: '备注',
    align:"center",
    dataIndex: 'note'
   },
];
//查询数据
export const searchFormSchema: FormSchema[] = [
];
//表单数据
export const formSchema: FormSchema[] = [
  {
    label: '卡片编号',
    field: 'cardNum',
    component: 'Input',
  },
  {
    label: '治疗机构名称',
    field: 'treatOrg',
   component: 'Input',
  },
  {
    label: '患者姓名',
    field: 'patientName',
    component: 'Input',
  },
  {
    label: '有效证件号',
    field: 'idCard',
    component: 'Input',
  },
  {
    label: '开始治疗日期',
    field: 'treatBeginTime',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
   label: '既往抗病毒治疗情况',
   field: 'treatCondition',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"treat_condition",
       type: "radio"
    },
  },
  {
   label: '起始治疗方案',
   field: 'treatMethod',
   component: 'JDictSelectTag',
   componentProps:{
       dictCode:"treat_method",
       type: "radio"
    },
  },
  {
    label: '责任医生',
    field: 'respDoctor',
    component: 'Input',
  },
  {
    label: '填表日期',
    field: 'fillDate',
    component: 'DatePicker',
    componentProps: {
       showTime: true,
       valueFormat: 'YYYY-MM-DD HH:mm:ss'
     },
  },
  {
    label: '备注',
    field: 'note',
    component: 'Input',
  },
  
	// TODO 主键隐藏字段，目前写死为ID
	{
	  label: '',
	  field: 'id',
	  component: 'Input',
	  show: false
	},
];

// 高级查询数据
export const superQuerySchema = {
  cardNum: {title: '卡片编号',order: 0,view: 'text', type: 'string',},
  treatOrg: {title: '治疗机构',order: 1,view: 'list', type: 'string',},
  patientName: {title: '患者姓名',order: 2,view: 'text', type: 'string',},
  idCard: {title: '有效证件号',order: 3,view: 'text', type: 'string',},
  treatBeginTime: {title: '开始治疗日期',order: 4,view: 'datetime', type: 'string',},
  treatCondition: {title: '既往抗病毒治疗情况',order: 5,view: 'radio', type: 'string',dictCode: 'treat_condition',},
  treatMethod: {title: '起始治疗方案',order: 6,view: 'radio', type: 'string',dictCode: 'treat_method',},
  respDoctor: {title: '责任医生',order: 7,view: 'text', type: 'string',},
  fillDate: {title: '填表日期',order: 8,view: 'datetime', type: 'string',},
  note: {title: '备注',order: 9,view: 'text', type: 'string',},
};

/**
* 流程表单调用这个方法获取formSchema
* @param param
*/
export function getBpmFormSchema(_formData): FormSchema[]{
  // 默认和原始表单保持一致 如果流程中配置了权限数据，这里需要单独处理formSchema
  return formSchema;
}
